using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.Controls;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 函数属性面板交互逻辑
    /// </summary>
    public partial class FunctionPropertyPanel : UserControl
    {
        private ModuleNodeViewModel? _currentNode;
        private FunctionNodeConfig _config = null!;
        private FieldReferenceTracker _referenceTracker = null!;
        private ConnectionStyleSyncService _connectionStyleService = null!;

        /// <summary>当前节点</summary>
        public ModuleNodeViewModel? CurrentNode
        {
            get => _currentNode;
            private set
            {
                _currentNode = value;
                OnCurrentNodeChanged();
            }
        }

        /// <summary>属性值变化事件</summary>
        public event EventHandler<PropertyValueChangedEventArgs>? PropertyValueChanged;

        /// <summary>应用按钮点击事件</summary>
        public event EventHandler? ApplyClicked;

        /// <summary>重置按钮点击事件</summary>
        public event EventHandler? ResetClicked;

        /// <summary>可用函数类型</summary>
        public List<FunctionType> AvailableFunctionTypes { get; } = Enum.GetValues<FunctionType>().ToList();

        public FunctionPropertyPanel()
        {
            InitializeComponent();
            InitializeConfig();
            InitializeEventHandlers();
        }

        /// <summary>
        /// 设置当前节点
        /// </summary>
        /// <param name="node">节点视图模型</param>
        public void SetNode(ModuleNodeViewModel node)
        {
            CurrentNode = node;
        }

        /// <summary>
        /// 初始化配置
        /// </summary>
        private void InitializeConfig()
        {
            _config = new FunctionNodeConfig();
            _referenceTracker = new FieldReferenceTracker();
            _connectionStyleService = new ConnectionStyleSyncService();

            // 设置数据上下文
            DataContext = _config;

            // 建立服务之间的关联
            _connectionStyleService.SetReferenceTracker(_referenceTracker);

            // 订阅引用追踪器事件
            _referenceTracker.FieldReferenceChanged += OnFieldReferenceChanged;
            _referenceTracker.ConnectionStateChanged += OnConnectionStateChanged;

            // 订阅连接线样式变化事件
            _connectionStyleService.ConnectionStyleChanged += OnConnectionStyleChanged;
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            // 数据来源模式切换
            DataSourceModeToggle.Checked += (s, e) => OnDataSourceModeChanged(DataSourceMode.ExternalFile);
            DataSourceModeToggle.Unchecked += (s, e) => OnDataSourceModeChanged(DataSourceMode.NodeOutput);

            // 字段搜索
            FieldSearchBox.TextChanged += OnFieldSearchTextChanged;

            // 字段树双击添加
            FieldTreeView.MouseDoubleClick += OnFieldTreeViewDoubleClick;

            // 清空已选字段
            ClearSelectedFieldsButton.Click += OnClearSelectedFieldsClick;

            // 添加函数
            AddFunctionButton.Click += OnAddFunctionClick;

            // 导入模板
            ImportTemplateButton.Click += OnImportTemplateClick;

            // 执行所有函数
            ExecuteAllButton.Click += OnExecuteAllClick;

            // 导出结果
            ExportResultsButton.Click += OnExportResultsClick;

            // 监听函数表达式变化
            _config.Functions.CollectionChanged += OnFunctionsCollectionChanged;
        }

        /// <summary>
        /// 当前节点变化处理
        /// </summary>
        private void OnCurrentNodeChanged()
        {
            if (_currentNode?.Module == null)
            {
                ClearConfig();
                return;
            }

            LoadNodeConfig();
            LoadAvailableFields();
        }

        /// <summary>
        /// 加载节点配置
        /// </summary>
        private void LoadNodeConfig()
        {
            if (_currentNode?.PropertyValues == null)
                return;

            try
            {
                // 从节点属性中加载函数配置
                var configJson = _currentNode.PropertyValues.GetValue("functionConfig")?.ToString();
                if (!string.IsNullOrEmpty(configJson))
                {
                    // TODO: 实现JSON反序列化加载配置
                    // _config = JsonSerializer.Deserialize<FunctionNodeConfig>(configJson);
                }

                // 加载已选字段
                LoadSelectedFields();

                // 加载函数表达式
                LoadFunctions();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载节点配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载可用字段
        /// </summary>
        private void LoadAvailableFields()
        {
            _config.AvailableFields.Clear();

            if (_config.DataSourceMode == DataSourceMode.NodeOutput)
            {
                LoadFieldsFromConnectedNodes();
            }
            else
            {
                LoadFieldsFromExternalFile();
            }
        }

        /// <summary>
        /// 从连接的节点加载字段
        /// </summary>
        private void LoadFieldsFromConnectedNodes()
        {
            if (_currentNode?.Inputs == null)
                return;

            foreach (var input in _currentNode.Inputs)
            {
                if (input.IsConnected)
                {
                    // 获取连接的源节点
                    var sourceNode = GetSourceNode(input);
                    if (sourceNode != null)
                    {
                        var fields = GetNodeOutputFields(sourceNode);
                        foreach (var field in fields)
                        {
                            field.SourceType = "node";
                            field.SourceId = sourceNode.Module?.Id ?? "";
                            _config.AvailableFields.Add(field);
                            _referenceTracker.RegisterField(field);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 从外部文件加载字段
        /// </summary>
        private void LoadFieldsFromExternalFile()
        {
            if (string.IsNullOrEmpty(_config.ExternalFilePath))
                return;

            try
            {
                // TODO: 实现从外部文件读取字段信息
                // var fields = FileFieldReader.ReadFields(_config.ExternalFilePath);
                // foreach (var field in fields)
                // {
                //     field.SourceType = "file";
                //     field.SourceId = _config.ExternalFilePath;
                //     _config.AvailableFields.Add(field);
                //     _referenceTracker.RegisterField(field);
                // }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从外部文件加载字段失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载已选字段
        /// </summary>
        private void LoadSelectedFields()
        {
            // TODO: 从节点属性中加载已选字段
        }

        /// <summary>
        /// 加载函数表达式
        /// </summary>
        private void LoadFunctions()
        {
            // TODO: 从节点属性中加载函数表达式
            // 如果没有函数，添加一个默认函数
            if (!_config.Functions.Any())
            {
                AddDefaultFunction();
            }
        }

        /// <summary>
        /// 添加默认函数
        /// </summary>
        private void AddDefaultFunction()
        {
            var defaultFunction = new FunctionExpression
            {
                Name = "函数1",
                Expression = "",
                Type = FunctionType.Math,
                IsEnabled = true
            };

            _config.Functions.Add(defaultFunction);
        }

        /// <summary>
        /// 清空配置
        /// </summary>
        private void ClearConfig()
        {
            _config.AvailableFields.Clear();
            _config.SelectedFields.Clear();
            _config.Functions.Clear();
            _referenceTracker.Clear();
        }

        /// <summary>
        /// 获取源节点
        /// </summary>
        private ModuleNodeViewModel? GetSourceNode(ConnectorViewModel input)
        {
            // TODO: 实现获取连接的源节点逻辑
            return null;
        }

        /// <summary>
        /// 获取节点输出字段
        /// </summary>
        private List<FieldInfo> GetNodeOutputFields(ModuleNodeViewModel node)
        {
            // TODO: 实现获取节点输出字段逻辑
            return new List<FieldInfo>();
        }

        /// <summary>
        /// 数据来源模式变化处理
        /// </summary>
        private void OnDataSourceModeChanged(DataSourceMode newMode)
        {
            if (_config.DataSourceMode != newMode)
            {
                _config.DataSourceMode = newMode;
                LoadAvailableFields();
                OnPropertyValueChanged("dataSourceMode", newMode);
            }
        }

        /// <summary>
        /// 字段搜索文本变化处理
        /// </summary>
        private void OnFieldSearchTextChanged(object sender, TextChangedEventArgs e)
        {
            var searchText = FieldSearchBox.Text?.Trim().ToLower();
            // TODO: 实现字段搜索过滤逻辑
        }

        /// <summary>
        /// 字段树双击处理
        /// </summary>
        private void OnFieldTreeViewDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (FieldTreeView.SelectedItem is FieldInfo selectedField)
            {
                AddSelectedField(selectedField);
            }
        }

        /// <summary>
        /// 添加已选字段
        /// </summary>
        private void AddSelectedField(FieldInfo field)
        {
            if (!_config.SelectedFields.Contains(field))
            {
                _config.SelectedFields.Add(field);
                OnPropertyValueChanged("selectedFields", _config.SelectedFields.ToList());
            }
        }

        /// <summary>
        /// 清空已选字段处理
        /// </summary>
        private void OnClearSelectedFieldsClick(object sender, RoutedEventArgs e)
        {
            _config.SelectedFields.Clear();
            OnPropertyValueChanged("selectedFields", _config.SelectedFields.ToList());
        }

        /// <summary>
        /// 添加函数处理
        /// </summary>
        private void OnAddFunctionClick(object sender, RoutedEventArgs e)
        {
            var newFunction = new FunctionExpression
            {
                Name = $"函数{_config.Functions.Count + 1}",
                Expression = "",
                Type = FunctionType.Math,
                IsEnabled = true
            };

            _config.Functions.Add(newFunction);
            OnPropertyValueChanged("functions", _config.Functions.ToList());
        }

        /// <summary>
        /// 导入模板处理
        /// </summary>
        private void OnImportTemplateClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现模板导入逻辑
        }

        /// <summary>
        /// 执行所有函数处理
        /// </summary>
        private void OnExecuteAllClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现函数执行逻辑
        }

        /// <summary>
        /// 导出结果处理
        /// </summary>
        private void OnExportResultsClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现结果导出逻辑
        }

        /// <summary>
        /// 函数集合变化处理
        /// </summary>
        private void OnFunctionsCollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // 更新表达式引用追踪
            if (e.NewItems != null)
            {
                foreach (FunctionExpression function in e.NewItems)
                {
                    function.PropertyChanged += OnFunctionPropertyChanged;
                }
            }

            if (e.OldItems != null)
            {
                foreach (FunctionExpression function in e.OldItems)
                {
                    function.PropertyChanged -= OnFunctionPropertyChanged;
                    _referenceTracker.RemoveExpressionReferences(function.Id);
                }
            }
        }

        /// <summary>
        /// 函数属性变化处理
        /// </summary>
        private void OnFunctionPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (sender is FunctionExpression function && e.PropertyName == nameof(FunctionExpression.Expression))
            {
                _referenceTracker.UpdateExpressionReferences(function.Id, function.Expression);
            }
        }

        /// <summary>
        /// 字段引用变化处理
        /// </summary>
        private void OnFieldReferenceChanged(object? sender, FieldReferenceChangedEventArgs e)
        {
            // 更新UI中字段的引用状态显示
            var field = _config.AvailableFields.FirstOrDefault(f => f.Name == e.FieldId);
            if (field != null)
            {
                field.IsReferenced = e.IsReferenced;
            }
        }

        /// <summary>
        /// 连接状态变化处理
        /// </summary>
        private void OnConnectionStateChanged(object? sender, ConnectionStateChangedEventArgs e)
        {
            // 更新UI中字段的连接状态显示
            var field = _config.AvailableFields.FirstOrDefault(f => f.Name == e.FieldId);
            if (field != null)
            {
                field.IsConnected = e.IsConnected;
            }

            // 注册或注销连接线
            if (e.IsConnected && e.Connection != null)
            {
                _connectionStyleService.RegisterConnection(e.Connection, e.FieldId);
            }
            else
            {
                // 注销连接线（需要连接ID）
                if (e.Connection != null)
                {
                    _connectionStyleService.UnregisterConnection(e.Connection.Id, e.FieldId);
                }
            }
        }

        /// <summary>
        /// 连接线样式变化处理
        /// </summary>
        private void OnConnectionStyleChanged(object? sender, ConnectionStyleChangedEventArgs e)
        {
            System.Diagnostics.Debug.WriteLine(
                $"连接线样式已更新: 字段 {e.FieldId}, 引用状态 {e.IsReferenced}");
        }

        /// <summary>
        /// 触发属性值变化事件
        /// </summary>
        private void OnPropertyValueChanged(string propertyName, object? value)
        {
            PropertyValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(propertyName, null, value));
        }
    }
}
