using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Linq;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using Telerik.Windows.Controls;
using Telerik.Windows.Documents.Model;
using Telerik.Windows.Documents.FormatProviders.OpenXml.Docx;
using Telerik.Windows.Documents.Primitives;
using Telerik.Windows.Documents.Selection;
using Telerik.Windows.Controls.RibbonView;
using Telerik.Windows.Controls.RichTextBoxUI;
using Telerik.Windows.Documents.Layout;
using Microsoft.Win32;

namespace ProjectDigitizer.Studio.Windows
{
    /// <summary>
    /// 智能文档模板设计器窗口 (Telerik版本)
    /// </summary>
    public partial class TelerikTemplateDesignerWindow : Window, INotifyPropertyChanged
    {
        #region 静态字段

        /// <summary>
        /// 域字段匹配的正则表达式
        /// </summary>
        private static readonly Regex FieldPattern = new(@"\{\{[^}]+\}\}", RegexOptions.Compiled);

        #endregion

        #region 智能文档字段

        /// <summary>
        /// 章节数据集合
        /// </summary>
        private ObservableCollection<ChapterItem> _chapters = new();

        /// <summary>
        /// 域字段数据集合
        /// </summary>
        private Dictionary<string, object> _fieldData = new();

        /// <summary>
        /// 数学符号字典
        /// </summary>
        private static readonly Dictionary<string, string> MathSymbols = new()
        {
            { "平方", "²" },
            { "立方", "³" },
            { "度", "°" },
            { "正负", "±" },
            { "乘号", "×" },
            { "除号", "÷" },
            { "约等于", "≈" },
            { "不等于", "≠" },
            { "小于等于", "≤" },
            { "大于等于", "≥" },
            { "无穷", "∞" },
            { "求和", "∑" },
            { "积分", "∫" },
            { "阿尔法", "α" },
            { "贝塔", "β" },
            { "伽马", "γ" },
            { "德尔塔", "δ" },
            { "派", "π" }
        };

        #endregion

        #region 章节数据模型

        /// <summary>
        /// 章节项数据模型
        /// </summary>
        public class ChapterItem
        {
            public string Title { get; set; } = "";
            public string Content { get; set; } = "";
            public int Level { get; set; } = 1;
            public List<ChapterItem> SubChapters { get; set; } = new();
        }

        #endregion

        #region INotifyPropertyChanged实现

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion

        public TelerikTemplateDesignerWindow()
        {
            // 本地化已在App.xaml.cs中统一设置，无需重复调用
            InitializeComponent();

            InitializeTelerikRichTextBox();
            SetupEventHandlers();
            InitializeRibbonControls();
            InitializeSmartDocument();
        }

        /// <summary>
        /// 窗口加载事件
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 应用主题特定的图标（如果可用）
                try
                {
                    // 尝试应用主题图标，如果失败则忽略
                    ApplyThemeSpecificIcons();
                }
                catch (Exception)
                {
                    // 忽略主题图标应用失败
                }

                UpdateStatus("模板设计器已加载完成");
            }
            catch (Exception ex)
            {
                UpdateStatus($"窗口加载时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 窗口卸载事件
        /// </summary>
        private void Window_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // 清理资源
                CleanupResources();
            }
            catch (Exception)
            {
                // 忽略卸载时的错误
            }
        }

        /// <summary>
        /// 应用主题特定图标
        /// </summary>
        private void ApplyThemeSpecificIcons()
        {
            // 这里可以添加主题图标应用逻辑
            // 目前使用默认图标
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        private void CleanupResources()
        {
            // 清理任何需要释放的资源
        }

        /// <summary>
        /// 初始化Telerik RichTextBox控件
        /// </summary>
        private void InitializeTelerikRichTextBox()
        {
            try
            {
                // 配置Telerik RichTextBox选项
                TelerikRichTextBox.IsSpellCheckingEnabled = true;
                TelerikRichTextBox.IsReadOnly = false;
                TelerikRichTextBox.LayoutMode = DocumentLayoutMode.Paged;
                TelerikRichTextBox.ShowComments = true;

                // 设置文档属性
                SetupNewDocument(TelerikRichTextBox.Document);

                // 创建默认文档内容
                CreateDefaultTemplate();

                UpdateStatus("Telerik模板已初始化");
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化Telerik RichTextBox时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置新文档的属性
        /// </summary>
        private void SetupNewDocument(RadDocument document)
        {
            document.LayoutMode = DocumentLayoutMode.Paged;
            document.ParagraphDefaultSpacingAfter = 10;
            document.SectionDefaultPageMargin = new Telerik.Windows.Documents.Layout.Padding(95);
        }

        /// <summary>
        /// 创建默认模板内容
        /// </summary>
        private void CreateDefaultTemplate()
        {
            // 插入标题
            var titleText = "智能文档模板 (Telerik版本)\n\n";
            TelerikRichTextBox.Insert(titleText);

            // 插入项目信息段落
            var projectInfoText = "项目名称：{{项目名称}}\n项目描述：{{项目描述}}\n创建日期：{{创建日期}}\n\n";
            TelerikRichTextBox.Insert(projectInfoText);

            // 插入统计信息
            var statsText = "节点统计：\n• 总节点数：{{节点数量}}\n• 连接数量：{{连接数量}}\n\n";
            TelerikRichTextBox.Insert(statsText);

            // 插入详细信息段落
            var detailText = "详细信息：\n在此处添加更多内容...\n";
            TelerikRichTextBox.Insert(detailText);
        }

        /// <summary>
        /// 拖放事件处理程序
        /// </summary>
        private void TelerikRichTextBox_Drop(object sender, DragEventArgs e)
        {
            try
            {
                string[]? droppedFiles = e.Data.GetData(DataFormats.FileDrop) as string[];
                if (droppedFiles != null)
                {
                    foreach (string droppedFile in droppedFiles)
                    {
                        string extension = Path.GetExtension(droppedFile);
                        if (IsSupportedImageFormat(extension))
                        {
                            FileInfo file = new FileInfo(droppedFile);
                            using (Stream imageStream = file.OpenRead())
                            {
                                TelerikRichTextBox.InsertImage(imageStream, extension);
                            }
                            UpdateStatus($"已插入图片: {Path.GetFileName(droppedFile)}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"拖放文件时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否为支持的图片格式
        /// </summary>
        private bool IsSupportedImageFormat(string? extension)
        {
            if (extension != null)
            {
                extension = extension.ToLower();
            }

            return extension == ".jpg" ||
                   extension == ".jpeg" ||
                   extension == ".png" ||
                   extension == ".bmp" ||
                   extension == ".tif" ||
                   extension == ".tiff" ||
                   extension == ".ico" ||
                   extension == ".gif" ||
                   extension == ".wdp" ||
                   extension == ".hdp";
        }

        /// <summary>
        /// 高亮显示文档中的域字段 - 简化版本
        /// </summary>
        private void HighlightFieldsInDocument()
        {
            try
            {
                // 简化实现，暂时跳过高亮功能
                // TODO: 实现Telerik RichTextBox的域字段高亮
                UpdateStatus("域字段高亮功能待实现");
            }
            catch (Exception ex)
            {
                UpdateStatus($"高亮域字段时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化智能文档功能
        /// </summary>
        private void InitializeSmartDocument()
        {
            try
            {
                // 初始化域字段数据
                InitializeFieldData();

                // 初始化章节数据
                InitializeChapterData();

                // 初始化域列表UI
                InitializeFieldListUI();

                // 初始化右侧面板
                InitializeRightPanel();

                UpdateStatus("智能文档功能已初始化");
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化智能文档功能时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化域字段数据
        /// </summary>
        private void InitializeFieldData()
        {
            _fieldData = new Dictionary<string, object>
            {
                { "项目名称", "智能文档项目" },
                { "项目描述", "基于Telerik的智能文档模板设计器" },
                { "创建日期", DateTime.Now.ToString("yyyy年MM月dd日") },
                { "当前时间", DateTime.Now.ToString("HH:mm:ss") },
                { "节点数量", "0" },
                { "连接数量", "0" },
                { "创建者", Environment.UserName },
                { "版本号", "1.0.0" }
            };
        }

        /// <summary>
        /// 初始化章节数据
        /// </summary>
        private void InitializeChapterData()
        {
            _chapters = new ObservableCollection<ChapterItem>
            {
                new ChapterItem
                {
                    Title = "第一章 项目概述",
                    Level = 1,
                    SubChapters = new List<ChapterItem>
                    {
                        new ChapterItem { Title = "1.1 项目背景", Level = 2 },
                        new ChapterItem { Title = "1.2 项目目标", Level = 2 },
                        new ChapterItem { Title = "1.3 项目范围", Level = 2 }
                    }
                },
                new ChapterItem
                {
                    Title = "第二章 技术规范",
                    Level = 1,
                    SubChapters = new List<ChapterItem>
                    {
                        new ChapterItem { Title = "2.1 系统架构", Level = 2 },
                        new ChapterItem { Title = "2.2 技术要求", Level = 2 }
                    }
                },
                new ChapterItem
                {
                    Title = "第三章 实施计划",
                    Level = 1,
                    SubChapters = new List<ChapterItem>
                    {
                        new ChapterItem { Title = "3.1 时间安排", Level = 2 },
                        new ChapterItem { Title = "3.2 资源配置", Level = 2 }
                    }
                }
            };
        }

        /// <summary>
        /// 初始化域列表UI
        /// </summary>
        private void InitializeFieldListUI()
        {
            try
            {
                FieldListBox.Items.Clear();

                var predefinedFields = new Dictionary<string, string>
                {
                    { "{{项目名称}}", "项目的正式名称" },
                    { "{{项目描述}}", "项目的详细描述" },
                    { "{{创建日期}}", "文档创建日期" },
                    { "{{当前时间}}", "当前系统时间" },
                    { "{{节点数量}}", "流程图中的节点总数" },
                    { "{{连接数量}}", "流程图中的连接总数" },
                    { "{{创建者}}", "文档创建者" },
                    { "{{版本号}}", "文档版本号" }
                };

                foreach (var field in predefinedFields)
                {
                    var item = new RadListBoxItem
                    {
                        Content = field.Key,
                        ToolTip = field.Value
                    };
                    FieldListBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化域列表UI时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化右侧面板
        /// </summary>
        private void InitializeRightPanel()
        {
            try
            {
                // 初始化验证结果列表
                ValidationResultsListBox.Items.Clear();

                // 初始化统计面板
                RefreshDocumentStatistics();

                UpdateStatus("右侧功能面板已初始化");
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化右侧面板时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置事件处理程序
        /// </summary>
        private void SetupEventHandlers()
        {
            // 监听文档内容变化
            TelerikRichTextBox.DocumentContentChanged += (s, e) => UpdateWordCount();

            // 域列表双击事件已移除，因为新设计中没有这些控件
        }

        /// <summary>
        /// 初始化Ribbon控件
        /// </summary>
        private void InitializeRibbonControls()
        {
            try
            {
                // RadRichTextBoxRibbonUI会通过DataContext自动关联
                // XAML中已经设置了正确的DataContext绑定
                UpdateStatus("Ribbon工具栏已初始化");
            }
            catch (Exception ex)
            {
                UpdateStatus($"初始化Ribbon工具栏时出错: {ex.Message}");
            }
        }



        /// <summary>
        /// 在光标位置插入域
        /// </summary>
        private void InsertFieldAtCursor(string? fieldText)
        {
            if (string.IsNullOrEmpty(fieldText))
                return;

            try
            {
                // 插入域文本
                TelerikRichTextBox.Insert(fieldText);

                UpdateStatus($"已插入域: {fieldText}");
            }
            catch (Exception ex)
            {
                UpdateStatus($"插入域字段时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新字数统计
        /// </summary>
        private void UpdateWordCount()
        {
            try
            {
                var text = TelerikRichTextBox.Document?.ToString() ?? "";

                // 更准确的字数统计（排除空白字符）
                var actualWordCount = text.Where(c => !char.IsWhiteSpace(c)).Count();

                // 更新状态栏中的字数显示
                WordCountDisplay.Text = $"字数: {actualWordCount}";

                // 简化页数统计
                PageCountDisplay.Text = "第 1 页，共 1 页";

                // 统计域字段数量并更新状态
                var fieldCount = FieldPattern.Matches(text).Count;
                if (fieldCount > 0)
                {
                    UpdateStatus($"文档包含 {fieldCount} 个域字段");
                }
                else
                {
                    UpdateStatus("就绪");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"更新统计信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 缩放滑块值变化事件
        /// </summary>
        private void ZoomSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            try
            {
                if (ZoomTextBlock != null && TelerikRichTextBox != null)
                {
                    var zoomValue = (int)e.NewValue;
                    ZoomTextBlock.Text = $"{zoomValue}%";

                    // 设置RichTextBox的缩放 - 使用正确的属性
                    var transform = new System.Windows.Media.ScaleTransform(zoomValue / 100.0, zoomValue / 100.0);
                    TelerikRichTextBox.RenderTransform = transform;
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"设置缩放时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusTextBlock.Text = message;
        }

        #region 工具栏事件处理

        /// <summary>
        /// 新建模板
        /// </summary>
        private void NewTemplate_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("确定要创建新模板吗？当前内容将被清除。", "确认",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    TelerikRichTextBox.Document = new RadDocument();
                    SetupNewDocument(TelerikRichTextBox.Document);
                    CreateDefaultTemplate();
                    UpdateStatus("已创建新模板");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"创建新模板失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 打开模板
        /// </summary>
        private void OpenTemplate_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Filter = "Word文档 (*.docx)|*.docx|所有文件 (*.*)|*.*",
                Title = "打开模板文件"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    var provider = new DocxFormatProvider();
                    using var stream = File.OpenRead(openFileDialog.FileName);
                    var document = provider.Import(stream);
                    TelerikRichTextBox.Document = document;

                    UpdateStatus($"已打开模板: {Path.GetFileName(openFileDialog.FileName)}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开文件失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 保存模板
        /// </summary>
        private void SaveTemplate_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "Word文档 (*.docx)|*.docx",
                Title = "保存模板文件",
                FileName = "智能文档模板"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var provider = new DocxFormatProvider();
                    using var stream = File.Create(saveFileDialog.FileName);
                    provider.Export(TelerikRichTextBox.Document, stream);

                    UpdateStatus($"模板已保存: {Path.GetFileName(saveFileDialog.FileName)}");
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"保存文件失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 插入域
        /// </summary>
        private void InsertField_Click(object sender, RoutedEventArgs e)
        {
            // 显示域选择对话框
            ShowFieldSelectionDialog();
        }

        /// <summary>
        /// 插入表格
        /// </summary>
        private void InsertTable_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 使用Telerik的表格插入功能
                TelerikRichTextBox.InsertTable(3, 3); // 默认3x3表格
                UpdateStatus("已插入表格");
            }
            catch (Exception ex)
            {
                UpdateStatus($"插入表格时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 预览模板
        /// </summary>
        private void Preview_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 简单的预览功能 - 显示当前文档内容
                var previewWindow = new Window
                {
                    Title = "模板预览",
                    Width = 800,
                    Height = 600,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = this
                };

                var previewRichTextBox = new RadRichTextBox
                {
                    Document = TelerikRichTextBox.Document,
                    IsReadOnly = true,
                    Margin = new Thickness(10)
                };

                previewWindow.Content = previewRichTextBox;
                previewWindow.ShowDialog();

                UpdateStatus("预览已显示");
            }
            catch (Exception ex)
            {
                UpdateStatus($"预览时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出Word文档
        /// </summary>
        private void ExportWord_Click(object sender, RoutedEventArgs e)
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "Word文档 (*.docx)|*.docx",
                Title = "导出Word文档",
                FileName = "智能文档模板_导出"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var provider = new DocxFormatProvider();
                    using var stream = File.Create(saveFileDialog.FileName);
                    provider.Export(TelerikRichTextBox.Document, stream);

                    UpdateStatus($"Word文档已导出: {Path.GetFileName(saveFileDialog.FileName)}");
                    MessageBox.Show("Word文档导出成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出Word文档失败: {ex.Message}", "错误",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 验证模板
        /// </summary>
        private void ValidateTemplate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                var validationMessage = $"模板验证结果：\n" +
                                      $"• 发现 {fieldMatches.Count} 个域字段\n" +
                                      $"• 文档长度: {text.Length} 字符\n" +
                                      $"• 字数统计: {text.Where(c => !char.IsWhiteSpace(c)).Count()} 字符";

                if (fieldMatches.Count > 0)
                {
                    validationMessage += "\n\n域字段列表：\n";
                    foreach (Match match in fieldMatches)
                    {
                        validationMessage += $"• {match.Value}\n";
                    }
                }

                MessageBox.Show(validationMessage, "模板验证", MessageBoxButton.OK, MessageBoxImage.Information);
                UpdateStatus("模板验证完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证模板时出错: {ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示域选择对话框
        /// </summary>
        private void ShowFieldSelectionDialog()
        {
            var fieldDialog = new Window
            {
                Title = "选择域字段",
                Width = 400,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var stackPanel = new StackPanel { Margin = new Thickness(20) };

            var instruction = new TextBlock
            {
                Text = "请选择要插入的域字段：",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            stackPanel.Children.Add(instruction);

            var fieldListBox = new ListBox { Height = 150, Margin = new Thickness(0, 0, 0, 10) };
            var commonFields = new[]
            {
                "{{项目名称}}", "{{项目描述}}", "{{创建日期}}",
                "{{节点数量}}", "{{连接数量}}", "{{当前时间}}",
                "{{创建者}}", "{{版本号}}", "{{修改日期}}"
            };

            foreach (var field in commonFields)
            {
                fieldListBox.Items.Add(field);
            }
            fieldListBox.SelectedIndex = 0;
            stackPanel.Children.Add(fieldListBox);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new Button
            {
                Content = "确定",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0)
            };
            okButton.Click += (s, e) =>
            {
                if (fieldListBox.SelectedItem != null)
                {
                    InsertFieldAtCursor(fieldListBox.SelectedItem.ToString());
                }
                fieldDialog.Close();
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30
            };
            cancelButton.Click += (s, e) => fieldDialog.Close();

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            fieldDialog.Content = stackPanel;
            fieldDialog.ShowDialog();
        }









        #endregion

        #region 智能文档事件处理程序

        /// <summary>
        /// 文档内容变化事件
        /// </summary>
        private void TelerikRichTextBox_DocumentContentChanged(object sender, EventArgs e)
        {
            UpdateWordCount();
            UpdateFieldHighlighting();
        }

        /// <summary>
        /// 章节树选择变化事件
        /// </summary>
        private void ChapterTreeView_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ChapterTreeView.SelectedItem is RadTreeViewItem selectedItem)
            {
                var chapterTitle = selectedItem.Header?.ToString();
                UpdateStatus($"选中章节: {chapterTitle}");

                // 可以在这里添加跳转到对应章节的逻辑
                NavigateToChapter(chapterTitle);
            }
        }

        /// <summary>
        /// 域列表双击事件
        /// </summary>
        private void FieldListBox_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (FieldListBox.SelectedItem is RadListBoxItem selectedItem)
            {
                var fieldText = selectedItem.Content?.ToString();
                if (!string.IsNullOrEmpty(fieldText))
                {
                    InsertFieldAtCursor(fieldText);
                    FieldInfoTextBlock.Text = $"已插入域: {fieldText}";
                }
            }
        }

        /// <summary>
        /// 添加章节按钮点击事件
        /// </summary>
        private void AddChapter_Click(object sender, RoutedEventArgs e)
        {
            ShowAddChapterDialog();
        }

        /// <summary>
        /// 删除章节按钮点击事件
        /// </summary>
        private void DeleteChapter_Click(object sender, RoutedEventArgs e)
        {
            if (ChapterTreeView.SelectedItem is RadTreeViewItem selectedItem)
            {
                var result = MessageBox.Show($"确定要删除章节 '{selectedItem.Header}' 吗？",
                                           "确认删除",
                                           MessageBoxButton.YesNo,
                                           MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    RemoveChapterFromTree(selectedItem);
                    UpdateStatus($"已删除章节: {selectedItem.Header}");
                }
            }
            else
            {
                MessageBox.Show("请先选择要删除的章节。", "提示",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 添加域按钮点击事件
        /// </summary>
        private void AddField_Click(object sender, RoutedEventArgs e)
        {
            ShowAddFieldDialog();
        }

        /// <summary>
        /// 刷新域按钮点击事件
        /// </summary>
        private void RefreshFields_Click(object sender, RoutedEventArgs e)
        {
            RefreshFieldList();
            UpdateStatus("域列表已刷新");
        }

        /// <summary>
        /// 插入章节按钮点击事件
        /// </summary>
        private void InsertChapter_Click(object sender, RoutedEventArgs e)
        {
            ShowInsertChapterDialog();
        }

        /// <summary>
        /// 更新域按钮点击事件
        /// </summary>
        private void UpdateFields_Click(object sender, RoutedEventArgs e)
        {
            UpdateAllFields();
            UpdateStatus("所有域字段已更新");
        }

        /// <summary>
        /// 插入数学符号按钮点击事件
        /// </summary>
        private void InsertMathSymbol_Click(object sender, RoutedEventArgs e)
        {
            ShowMathSymbolDialog();
        }

        /// <summary>
        /// 生成目录按钮点击事件
        /// </summary>
        private void GenerateTableOfContents_Click(object sender, RoutedEventArgs e)
        {
            GenerateTableOfContents();
            UpdateStatus("目录已生成");
        }

        /// <summary>
        /// 验证域按钮点击事件
        /// </summary>
        private void ValidateFields_Click(object sender, RoutedEventArgs e)
        {
            ValidateAllFields();
        }

        /// <summary>
        /// 导出PDF按钮点击事件
        /// </summary>
        private void ExportPdf_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("PDF导出功能正在开发中...", "提示",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 导出模板按钮点击事件
        /// </summary>
        private void ExportTemplate_Click(object sender, RoutedEventArgs e)
        {
            ExportAsTemplate();
        }

        /// <summary>
        /// 显示统计信息按钮点击事件
        /// </summary>
        private void ShowStatistics_Click(object sender, RoutedEventArgs e)
        {
            ShowDocumentStatistics();
        }

        /// <summary>
        /// 验证文档按钮点击事件
        /// </summary>
        private void ValidateDocument_Click(object sender, RoutedEventArgs e)
        {
            PerformDocumentValidation();
        }

        /// <summary>
        /// 拼写检查按钮点击事件
        /// </summary>
        private void CheckSpelling_Click(object sender, RoutedEventArgs e)
        {
            PerformSpellCheck();
        }

        /// <summary>
        /// 验证结果双击事件
        /// </summary>
        private void ValidationResult_DoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (ValidationResultsListBox.SelectedItem is RadListBoxItem selectedItem)
            {
                var resultText = selectedItem.Content?.ToString();
                if (!string.IsNullOrEmpty(resultText))
                {
                    // 跳转到问题位置
                    NavigateToValidationIssue(resultText);
                }
            }
        }

        /// <summary>
        /// 刷新统计按钮点击事件
        /// </summary>
        private void RefreshStats_Click(object sender, RoutedEventArgs e)
        {
            RefreshDocumentStatistics();
        }

        /// <summary>
        /// 导出统计按钮点击事件
        /// </summary>
        private void ExportStats_Click(object sender, RoutedEventArgs e)
        {
            ExportDocumentStatistics();
        }

        #endregion

        #region 智能文档功能实现

        /// <summary>
        /// 更新域字段高亮
        /// </summary>
        private void UpdateFieldHighlighting()
        {
            try
            {
                // TODO: 实现域字段高亮功能
                // 这里可以添加高亮显示文档中域字段的逻辑
            }
            catch (Exception ex)
            {
                UpdateStatus($"更新域字段高亮时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 导航到指定章节
        /// </summary>
        private void NavigateToChapter(string? chapterTitle)
        {
            if (string.IsNullOrEmpty(chapterTitle)) return;

            try
            {
                // TODO: 实现章节导航功能
                // 这里可以添加在文档中查找并跳转到指定章节的逻辑
                UpdateStatus($"导航到章节: {chapterTitle}");
            }
            catch (Exception ex)
            {
                UpdateStatus($"导航到章节时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示添加章节对话框
        /// </summary>
        private void ShowAddChapterDialog()
        {
            var dialog = new Window
            {
                Title = "添加章节",
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var stackPanel = new StackPanel { Margin = new Thickness(20) };

            var titleLabel = new TextBlock { Text = "章节标题:", Margin = new Thickness(0, 0, 0, 5) };
            stackPanel.Children.Add(titleLabel);

            var titleTextBox = new TextBox { Margin = new Thickness(0, 0, 0, 10) };
            stackPanel.Children.Add(titleTextBox);

            var levelLabel = new TextBlock { Text = "章节级别:", Margin = new Thickness(0, 0, 0, 5) };
            stackPanel.Children.Add(levelLabel);

            var levelComboBox = new ComboBox { Margin = new Thickness(0, 0, 0, 20) };
            levelComboBox.Items.Add("1级章节");
            levelComboBox.Items.Add("2级章节");
            levelComboBox.Items.Add("3级章节");
            levelComboBox.SelectedIndex = 0;
            stackPanel.Children.Add(levelComboBox);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new Button
            {
                Content = "确定",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0)
            };
            okButton.Click += (s, e) =>
            {
                if (!string.IsNullOrWhiteSpace(titleTextBox.Text))
                {
                    AddChapterToTree(titleTextBox.Text, levelComboBox.SelectedIndex + 1);
                    dialog.Close();
                }
                else
                {
                    MessageBox.Show("请输入章节标题。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30
            };
            cancelButton.Click += (s, e) => dialog.Close();

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;
            dialog.ShowDialog();
        }

        /// <summary>
        /// 添加章节到树控件
        /// </summary>
        private void AddChapterToTree(string title, int level)
        {
            try
            {
                var newItem = new RadTreeViewItem { Header = title };

                if (level == 1)
                {
                    ChapterTreeView.Items.Add(newItem);
                }
                else if (ChapterTreeView.SelectedItem is RadTreeViewItem selectedItem)
                {
                    selectedItem.Items.Add(newItem);
                    selectedItem.IsExpanded = true;
                }
                else
                {
                    // 如果没有选中项，添加到最后一个一级章节下
                    if (ChapterTreeView.Items.Count > 0)
                    {
                        var lastItem = ChapterTreeView.Items[ChapterTreeView.Items.Count - 1] as RadTreeViewItem;
                        lastItem?.Items.Add(newItem);
                        if (lastItem != null) lastItem.IsExpanded = true;
                    }
                }

                UpdateStatus($"已添加章节: {title}");
            }
            catch (Exception ex)
            {
                UpdateStatus($"添加章节时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 从树控件中移除章节
        /// </summary>
        private void RemoveChapterFromTree(RadTreeViewItem item)
        {
            try
            {
                if (item.Parent is RadTreeViewItem parentItem)
                {
                    parentItem.Items.Remove(item);
                }
                else
                {
                    ChapterTreeView.Items.Remove(item);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"删除章节时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示添加域对话框
        /// </summary>
        private void ShowAddFieldDialog()
        {
            var dialog = new Window
            {
                Title = "添加域字段",
                Width = 450,
                Height = 300,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var stackPanel = new StackPanel { Margin = new Thickness(20) };

            var nameLabel = new TextBlock { Text = "域名称:", Margin = new Thickness(0, 0, 0, 5) };
            stackPanel.Children.Add(nameLabel);

            var nameTextBox = new TextBox { Margin = new Thickness(0, 0, 0, 10) };
            stackPanel.Children.Add(nameTextBox);

            var valueLabel = new TextBlock { Text = "默认值:", Margin = new Thickness(0, 0, 0, 5) };
            stackPanel.Children.Add(valueLabel);

            var valueTextBox = new TextBox { Margin = new Thickness(0, 0, 0, 10) };
            stackPanel.Children.Add(valueTextBox);

            var descLabel = new TextBlock { Text = "描述:", Margin = new Thickness(0, 0, 0, 5) };
            stackPanel.Children.Add(descLabel);

            var descTextBox = new TextBox
            {
                Height = 60,
                TextWrapping = TextWrapping.Wrap,
                AcceptsReturn = true,
                Margin = new Thickness(0, 0, 0, 20)
            };
            stackPanel.Children.Add(descTextBox);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new Button
            {
                Content = "确定",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0)
            };
            okButton.Click += (s, e) =>
            {
                if (!string.IsNullOrWhiteSpace(nameTextBox.Text))
                {
                    AddFieldToList(nameTextBox.Text, valueTextBox.Text, descTextBox.Text);
                    dialog.Close();
                }
                else
                {
                    MessageBox.Show("请输入域名称。", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30
            };
            cancelButton.Click += (s, e) => dialog.Close();

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;
            dialog.ShowDialog();
        }

        /// <summary>
        /// 添加域到列表
        /// </summary>
        private void AddFieldToList(string name, string value, string description)
        {
            try
            {
                var fieldName = $"{{{{{name}}}}}";
                var newItem = new RadListBoxItem
                {
                    Content = fieldName,
                    ToolTip = description
                };

                FieldListBox.Items.Add(newItem);

                // 添加到域数据字典
                if (!_fieldData.ContainsKey(name))
                {
                    _fieldData[name] = value;
                }

                UpdateStatus($"已添加域: {fieldName}");
            }
            catch (Exception ex)
            {
                UpdateStatus($"添加域时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新域列表
        /// </summary>
        private void RefreshFieldList()
        {
            try
            {
                // 扫描文档中的域字段
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                // 清空现有列表
                FieldListBox.Items.Clear();

                // 添加预定义域
                var predefinedFields = new[]
                {
                    "{{项目名称}}", "{{项目描述}}", "{{创建日期}}", "{{当前时间}}",
                    "{{节点数量}}", "{{连接数量}}", "{{创建者}}", "{{版本号}}"
                };

                foreach (var field in predefinedFields)
                {
                    var item = new RadListBoxItem { Content = field };
                    FieldListBox.Items.Add(item);
                }

                // 添加文档中发现的新域
                var foundFields = fieldMatches.Cast<Match>()
                    .Select(m => m.Value)
                    .Distinct()
                    .Where(f => !predefinedFields.Contains(f));

                foreach (var field in foundFields)
                {
                    var item = new RadListBoxItem
                    {
                        Content = field,
                        ToolTip = "文档中发现的域字段"
                    };
                    FieldListBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"刷新域列表时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示插入章节对话框
        /// </summary>
        private void ShowInsertChapterDialog()
        {
            var dialog = new Window
            {
                Title = "插入章节",
                Width = 400,
                Height = 250,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var stackPanel = new StackPanel { Margin = new Thickness(20) };

            var instruction = new TextBlock
            {
                Text = "选择要插入的章节模板:",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            stackPanel.Children.Add(instruction);

            var templateListBox = new ListBox { Height = 120, Margin = new Thickness(0, 0, 0, 10) };
            var templates = new[]
            {
                "标准章节模板",
                "技术规范章节",
                "实施计划章节",
                "总结章节",
                "附录章节"
            };

            foreach (var template in templates)
            {
                templateListBox.Items.Add(template);
            }
            templateListBox.SelectedIndex = 0;
            stackPanel.Children.Add(templateListBox);

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new Button
            {
                Content = "插入",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0)
            };
            okButton.Click += (s, e) =>
            {
                if (templateListBox.SelectedItem != null)
                {
                    InsertChapterTemplate(templateListBox.SelectedItem.ToString());
                }
                dialog.Close();
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30
            };
            cancelButton.Click += (s, e) => dialog.Close();

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;
            dialog.ShowDialog();
        }

        /// <summary>
        /// 插入章节模板
        /// </summary>
        private void InsertChapterTemplate(string? templateName)
        {
            if (string.IsNullOrEmpty(templateName)) return;

            try
            {
                string templateContent = templateName switch
                {
                    "标准章节模板" => "\n\n# 章节标题\n\n## 概述\n\n在此处添加章节概述内容...\n\n## 详细内容\n\n在此处添加详细内容...\n\n",
                    "技术规范章节" => "\n\n# 技术规范\n\n## 系统要求\n\n• 操作系统：{{操作系统}}\n• 内存：{{内存要求}}\n• 存储：{{存储要求}}\n\n## 技术标准\n\n在此处添加技术标准...\n\n",
                    "实施计划章节" => "\n\n# 实施计划\n\n## 时间安排\n\n| 阶段 | 开始时间 | 结束时间 | 负责人 |\n|------|----------|----------|--------|\n| 阶段1 | {{开始时间1}} | {{结束时间1}} | {{负责人1}} |\n\n## 资源配置\n\n在此处添加资源配置信息...\n\n",
                    "总结章节" => "\n\n# 总结\n\n## 主要成果\n\n在此处总结主要成果...\n\n## 经验教训\n\n在此处总结经验教训...\n\n## 后续建议\n\n在此处提出后续建议...\n\n",
                    "附录章节" => "\n\n# 附录\n\n## 附录A - 相关文档\n\n在此处列出相关文档...\n\n## 附录B - 技术参数\n\n在此处添加技术参数...\n\n",
                    _ => "\n\n# 新章节\n\n在此处添加章节内容...\n\n"
                };

                TelerikRichTextBox.Insert(templateContent);
                UpdateStatus($"已插入章节模板: {templateName}");
            }
            catch (Exception ex)
            {
                UpdateStatus($"插入章节模板时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新所有域字段
        /// </summary>
        private void UpdateAllFields()
        {
            try
            {
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                foreach (Match match in fieldMatches)
                {
                    var fieldName = match.Value.Trim('{', '}');
                    if (_fieldData.ContainsKey(fieldName))
                    {
                        // TODO: 实现域字段值的替换
                        // 这里需要使用Telerik的文档操作API来替换域字段
                    }
                }

                // 更新时间相关的域
                _fieldData["当前时间"] = DateTime.Now.ToString("HH:mm:ss");
                _fieldData["创建日期"] = DateTime.Now.ToString("yyyy年MM月dd日");

                UpdateStatus($"已更新 {fieldMatches.Count} 个域字段");
            }
            catch (Exception ex)
            {
                UpdateStatus($"更新域字段时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示数学符号对话框
        /// </summary>
        private void ShowMathSymbolDialog()
        {
            var dialog = new Window
            {
                Title = "插入数学符号",
                Width = 400,
                Height = 350,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this
            };

            var stackPanel = new StackPanel { Margin = new Thickness(20) };

            var instruction = new TextBlock
            {
                Text = "选择要插入的数学符号:",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            stackPanel.Children.Add(instruction);

            var symbolListBox = new ListBox { Height = 200, Margin = new Thickness(0, 0, 0, 10) };

            foreach (var symbol in MathSymbols)
            {
                var item = new ListBoxItem
                {
                    Content = $"{symbol.Key} ({symbol.Value})",
                    Tag = symbol.Value
                };
                symbolListBox.Items.Add(item);
            }
            symbolListBox.SelectedIndex = 0;
            stackPanel.Children.Add(symbolListBox);

            var previewLabel = new TextBlock
            {
                Text = "预览:",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            stackPanel.Children.Add(previewLabel);

            var previewTextBlock = new TextBlock
            {
                FontSize = 24,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 20)
            };
            stackPanel.Children.Add(previewTextBlock);

            symbolListBox.SelectionChanged += (s, e) =>
            {
                if (symbolListBox.SelectedItem is ListBoxItem item)
                {
                    previewTextBlock.Text = item.Tag?.ToString();
                }
            };

            // 初始预览
            if (symbolListBox.SelectedItem is ListBoxItem selectedItem)
            {
                previewTextBlock.Text = selectedItem.Tag?.ToString();
            }

            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right
            };

            var okButton = new Button
            {
                Content = "插入",
                Width = 80,
                Height = 30,
                Margin = new Thickness(0, 0, 10, 0)
            };
            okButton.Click += (s, e) =>
            {
                if (symbolListBox.SelectedItem is ListBoxItem item && item.Tag != null)
                {
                    TelerikRichTextBox.Insert(item.Tag.ToString());
                    UpdateStatus($"已插入数学符号: {item.Tag}");
                }
                dialog.Close();
            };

            var cancelButton = new Button
            {
                Content = "取消",
                Width = 80,
                Height = 30
            };
            cancelButton.Click += (s, e) => dialog.Close();

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            stackPanel.Children.Add(buttonPanel);

            dialog.Content = stackPanel;
            dialog.ShowDialog();
        }

        /// <summary>
        /// 生成目录
        /// </summary>
        private void GenerateTableOfContents()
        {
            try
            {
                var tocContent = "\n\n# 目录\n\n";

                // 遍历章节树生成目录
                foreach (RadTreeViewItem item in ChapterTreeView.Items)
                {
                    tocContent += GenerateTocEntry(item, 1);
                }

                tocContent += "\n\n";

                // 在文档开头插入目录
                TelerikRichTextBox.Document.CaretPosition.MoveToDocumentStart();
                TelerikRichTextBox.Insert(tocContent);

                UpdateStatus("目录已生成");
            }
            catch (Exception ex)
            {
                UpdateStatus($"生成目录时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 生成目录条目
        /// </summary>
        private string GenerateTocEntry(RadTreeViewItem item, int level)
        {
            var indent = new string(' ', (level - 1) * 4);
            var entry = $"{indent}• {item.Header}\n";

            foreach (RadTreeViewItem subItem in item.Items)
            {
                entry += GenerateTocEntry(subItem, level + 1);
            }

            return entry;
        }

        /// <summary>
        /// 验证所有域字段
        /// </summary>
        private void ValidateAllFields()
        {
            try
            {
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                var validFields = new List<string>();
                var invalidFields = new List<string>();

                foreach (Match match in fieldMatches)
                {
                    var fieldName = match.Value.Trim('{', '}');
                    if (_fieldData.ContainsKey(fieldName))
                    {
                        validFields.Add(match.Value);
                    }
                    else
                    {
                        invalidFields.Add(match.Value);
                    }
                }

                var validationMessage = $"域字段验证结果：\n\n" +
                                      $"✅ 有效域字段: {validFields.Count} 个\n" +
                                      $"❌ 无效域字段: {invalidFields.Count} 个\n\n";

                if (invalidFields.Count > 0)
                {
                    validationMessage += "无效域字段列表：\n";
                    foreach (var field in invalidFields)
                    {
                        validationMessage += $"• {field}\n";
                    }
                }

                MessageBox.Show(validationMessage, "域字段验证",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                UpdateStatus($"域字段验证完成 - 有效: {validFields.Count}, 无效: {invalidFields.Count}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"验证域字段时出错: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出为模板
        /// </summary>
        private void ExportAsTemplate()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "智能文档模板 (*.docx)|*.docx",
                Title = "导出为模板",
                FileName = "智能文档模板"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var provider = new DocxFormatProvider();
                    using var stream = File.Create(saveFileDialog.FileName);
                    provider.Export(TelerikRichTextBox.Document, stream);

                    UpdateStatus($"模板已导出: {Path.GetFileName(saveFileDialog.FileName)}");
                    MessageBox.Show("模板导出成功！", "成功",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出模板失败: {ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 显示文档统计信息
        /// </summary>
        private void ShowDocumentStatistics()
        {
            try
            {
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                var wordCount = text.Where(c => !char.IsWhiteSpace(c)).Count();
                var paragraphCount = text.Split('\n').Length;
                var chapterCount = ChapterTreeView.Items.Count;
                var fieldCount = fieldMatches.Count;

                var statistics = $"文档统计信息：\n\n" +
                               $"📄 字符数: {wordCount:N0}\n" +
                               $"📝 段落数: {paragraphCount:N0}\n" +
                               $"📚 章节数: {chapterCount}\n" +
                               $"🔗 域字段数: {fieldCount}\n" +
                               $"📅 创建时间: {_fieldData.GetValueOrDefault("创建日期", "未知")}\n" +
                               $"👤 创建者: {_fieldData.GetValueOrDefault("创建者", "未知")}\n" +
                               $"🔢 版本号: {_fieldData.GetValueOrDefault("版本号", "未知")}";

                MessageBox.Show(statistics, "文档统计信息",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                UpdateStatus("已显示文档统计信息");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取统计信息时出错: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 右侧面板功能实现

        /// <summary>
        /// 执行文档验证
        /// </summary>
        private void PerformDocumentValidation()
        {
            try
            {
                ValidationResultsListBox.Items.Clear();
                var validationIssues = new List<string>();

                // 1. 验证域字段
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                foreach (Match match in fieldMatches)
                {
                    var fieldName = match.Value.Trim('{', '}');
                    if (!_fieldData.ContainsKey(fieldName))
                    {
                        validationIssues.Add($"❌ 未定义的域字段: {match.Value}");
                    }
                }

                // 2. 验证章节结构
                if (ChapterTreeView.Items.Count == 0)
                {
                    validationIssues.Add("⚠️ 文档缺少章节结构");
                }

                // 3. 验证文档长度
                if (text.Length < 100)
                {
                    validationIssues.Add("⚠️ 文档内容过短，建议添加更多内容");
                }

                // 4. 验证图片引用
                // TODO: 添加图片引用验证逻辑

                // 5. 验证表格结构
                // TODO: 添加表格结构验证逻辑

                // 显示验证结果
                if (validationIssues.Count == 0)
                {
                    var successItem = new RadListBoxItem
                    {
                        Content = "✅ 文档验证通过，未发现问题",
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green)
                    };
                    ValidationResultsListBox.Items.Add(successItem);
                    ValidationStatusTextBlock.Text = "验证完成 - 文档状态良好";
                }
                else
                {
                    foreach (var issue in validationIssues)
                    {
                        var item = new RadListBoxItem
                        {
                            Content = issue,
                            Foreground = issue.StartsWith("❌") ?
                                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red) :
                                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Orange)
                        };
                        ValidationResultsListBox.Items.Add(item);
                    }
                    ValidationStatusTextBlock.Text = $"验证完成 - 发现 {validationIssues.Count} 个问题";
                }

                UpdateStatus($"文档验证完成 - 发现 {validationIssues.Count} 个问题");
            }
            catch (Exception ex)
            {
                UpdateStatus($"文档验证时出错: {ex.Message}");
                ValidationStatusTextBlock.Text = "验证失败";
            }
        }

        /// <summary>
        /// 执行拼写检查
        /// </summary>
        private void PerformSpellCheck()
        {
            try
            {
                // 简化的拼写检查实现
                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var commonMisspellings = new Dictionary<string, string>
                {
                    { "teh", "the" },
                    { "recieve", "receive" },
                    { "seperate", "separate" },
                    { "definately", "definitely" },
                    { "occured", "occurred" }
                };

                var spellIssues = new List<string>();
                foreach (var misspelling in commonMisspellings)
                {
                    if (text.Contains(misspelling.Key, StringComparison.OrdinalIgnoreCase))
                    {
                        spellIssues.Add($"📝 拼写建议: '{misspelling.Key}' → '{misspelling.Value}'");
                    }
                }

                // 清空验证结果并显示拼写检查结果
                ValidationResultsListBox.Items.Clear();

                if (spellIssues.Count == 0)
                {
                    var successItem = new RadListBoxItem
                    {
                        Content = "✅ 拼写检查完成，未发现常见拼写错误",
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green)
                    };
                    ValidationResultsListBox.Items.Add(successItem);
                    ValidationStatusTextBlock.Text = "拼写检查完成 - 无错误";
                }
                else
                {
                    foreach (var issue in spellIssues)
                    {
                        var item = new RadListBoxItem
                        {
                            Content = issue,
                            Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Blue)
                        };
                        ValidationResultsListBox.Items.Add(item);
                    }
                    ValidationStatusTextBlock.Text = $"拼写检查完成 - 发现 {spellIssues.Count} 个建议";
                }

                UpdateStatus($"拼写检查完成 - 发现 {spellIssues.Count} 个建议");
            }
            catch (Exception ex)
            {
                UpdateStatus($"拼写检查时出错: {ex.Message}");
                ValidationStatusTextBlock.Text = "拼写检查失败";
            }
        }

        /// <summary>
        /// 导航到验证问题位置
        /// </summary>
        private void NavigateToValidationIssue(string issueText)
        {
            try
            {
                // 简化实现：显示问题详情
                MessageBox.Show($"验证问题详情：\n\n{issueText}\n\n建议：请检查相关内容并进行修正。",
                              "验证问题详情",
                              MessageBoxButton.OK,
                              MessageBoxImage.Information);

                UpdateStatus($"查看验证问题: {issueText.Substring(0, Math.Min(30, issueText.Length))}...");
            }
            catch (Exception ex)
            {
                UpdateStatus($"导航到验证问题时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新文档统计信息
        /// </summary>
        private void RefreshDocumentStatistics()
        {
            try
            {
                StatisticsPanel.Children.Clear();

                var text = TelerikRichTextBox.Document?.ToString() ?? "";
                var fieldMatches = FieldPattern.Matches(text);

                // 基础统计
                var stats = new Dictionary<string, object>
                {
                    { "📄 总字符数", text.Length.ToString("N0") },
                    { "📝 有效字符数", text.Where(c => !char.IsWhiteSpace(c)).Count().ToString("N0") },
                    { "📋 段落数", text.Split('\n').Length.ToString("N0") },
                    { "📚 章节数", ChapterTreeView.Items.Count.ToString() },
                    { "🔗 域字段数", fieldMatches.Count.ToString() },
                    { "📅 最后修改", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") },
                    { "👤 创建者", _fieldData.GetValueOrDefault("创建者", "未知") },
                    { "🔢 版本号", _fieldData.GetValueOrDefault("版本号", "1.0.0") }
                };

                // 添加统计项到面板
                foreach (var stat in stats)
                {
                    var stackPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(0, 2, 0, 2)
                    };

                    var labelBlock = new TextBlock
                    {
                        Text = stat.Key + ":",
                        Width = 120,
                        FontWeight = FontWeights.Bold,
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    var valueBlock = new TextBlock
                    {
                        Text = stat.Value.ToString(),
                        Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.DarkBlue),
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    stackPanel.Children.Add(labelBlock);
                    stackPanel.Children.Add(valueBlock);
                    StatisticsPanel.Children.Add(stackPanel);
                }

                StatisticsStatusTextBlock.Text = $"统计信息已更新 - {DateTime.Now:HH:mm:ss}";
                UpdateStatus("文档统计信息已刷新");
            }
            catch (Exception ex)
            {
                UpdateStatus($"刷新统计信息时出错: {ex.Message}");
                StatisticsStatusTextBlock.Text = "统计信息更新失败";
            }
        }

        /// <summary>
        /// 导出文档统计信息
        /// </summary>
        private void ExportDocumentStatistics()
        {
            var saveFileDialog = new SaveFileDialog
            {
                Filter = "文本文件 (*.txt)|*.txt|CSV文件 (*.csv)|*.csv",
                Title = "导出统计信息",
                FileName = $"文档统计_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                try
                {
                    var text = TelerikRichTextBox.Document?.ToString() ?? "";
                    var fieldMatches = FieldPattern.Matches(text);

                    var exportContent = "";

                    if (Path.GetExtension(saveFileDialog.FileName).ToLower() == ".csv")
                    {
                        // CSV格式
                        exportContent = "统计项,数值\n";
                        exportContent += $"总字符数,{text.Length}\n";
                        exportContent += $"有效字符数,{text.Where(c => !char.IsWhiteSpace(c)).Count()}\n";
                        exportContent += $"段落数,{text.Split('\n').Length}\n";
                        exportContent += $"章节数,{ChapterTreeView.Items.Count}\n";
                        exportContent += $"域字段数,{fieldMatches.Count}\n";
                        exportContent += $"最后修改,{DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                        exportContent += $"创建者,{_fieldData.GetValueOrDefault("创建者", "未知")}\n";
                        exportContent += $"版本号,{_fieldData.GetValueOrDefault("版本号", "1.0.0")}\n";
                    }
                    else
                    {
                        // 文本格式
                        exportContent = "智能文档统计报告\n";
                        exportContent += "=" + new string('=', 30) + "\n\n";
                        exportContent += $"📄 总字符数: {text.Length:N0}\n";
                        exportContent += $"📝 有效字符数: {text.Where(c => !char.IsWhiteSpace(c)).Count():N0}\n";
                        exportContent += $"📋 段落数: {text.Split('\n').Length:N0}\n";
                        exportContent += $"📚 章节数: {ChapterTreeView.Items.Count}\n";
                        exportContent += $"🔗 域字段数: {fieldMatches.Count}\n";
                        exportContent += $"📅 最后修改: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                        exportContent += $"👤 创建者: {_fieldData.GetValueOrDefault("创建者", "未知")}\n";
                        exportContent += $"🔢 版本号: {_fieldData.GetValueOrDefault("版本号", "1.0.0")}\n\n";

                        // 添加域字段详情
                        if (fieldMatches.Count > 0)
                        {
                            exportContent += "域字段详情:\n";
                            exportContent += "-" + new string('-', 20) + "\n";
                            foreach (Match match in fieldMatches)
                            {
                                exportContent += $"• {match.Value}\n";
                            }
                        }

                        exportContent += $"\n报告生成时间: {DateTime.Now:yyyy年MM月dd日 HH:mm:ss}\n";
                    }

                    File.WriteAllText(saveFileDialog.FileName, exportContent, System.Text.Encoding.UTF8);

                    UpdateStatus($"统计信息已导出: {Path.GetFileName(saveFileDialog.FileName)}");
                    MessageBox.Show("统计信息导出成功！", "成功",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出统计信息失败: {ex.Message}", "错误",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        #endregion

    }
}
