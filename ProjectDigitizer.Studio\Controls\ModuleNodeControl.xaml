<UserControl x:Class="ProjectDigitizer.Studio.Controls.ModuleNodeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ProjectDigitizer.Studio.Controls"
             mc:Ignorable="d" 
             Width="200" Height="150">
    <Border CornerRadius="5" BorderThickness="1" BorderBrush="Gray" Background="White">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="30"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="30"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="{Binding HeaderBackground}" CornerRadius="5,5,0,0">
                <DockPanel LastChildFill="True">
                    <CheckBox DockPanel.Dock="Left" Margin="5,0,0,0" IsChecked="{Binding Module.IsEnabled}" VerticalAlignment="Center"/>
                    <Button DockPanel.Dock="Right" Content="✕" Width="20" Height="20" Margin="0,0,5,0" 
                            BorderThickness="0" Background="Transparent" Foreground="White"/>
                    <Button DockPanel.Dock="Right" Content="💡" Width="20" Height="20" Margin="0,0,5,0" 
                            BorderThickness="0" Background="Transparent" Foreground="White"
                            IsEnabled="{Binding IsLightBulbOn}"/>
                    <TextBlock Text="{Binding Title}" FontWeight="Bold" Foreground="White" VerticalAlignment="Center" Margin="5,0,0,0"/>
                </DockPanel>
            </Border>
            
            <!-- 内容区 -->
            <Grid Grid.Row="1" Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 条件区域 -->
                <StackPanel Grid.Row="0">
                    <TextBlock Text="条件:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ItemsControl ItemsSource="{Binding Conditions}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" Margin="5,2"/>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                    <Button Content="+ 添加条件" Margin="0,5,0,0" HorizontalAlignment="Left"/>
                </StackPanel>
                
                <!-- 参数区域 -->
                <StackPanel Grid.Row="1" Margin="0,10,0,0">
                    <TextBlock Text="参数:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <!-- 这里可以根据模块类型显示不同的参数编辑器 -->
                </StackPanel>
            </Grid>
            
            <!-- 底部连接点 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Border Width="10" Height="10" CornerRadius="5" Background="Green" Margin="5,0"/>
                <Border Width="10" Height="10" CornerRadius="5" Background="Red" Margin="5,0"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl> 