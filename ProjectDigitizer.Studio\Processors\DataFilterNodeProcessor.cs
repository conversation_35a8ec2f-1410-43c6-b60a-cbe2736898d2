using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Processors
{
    /// <summary>
    /// DataFilter 节点处理器 - 实现 INodeProcessor 接口
    /// 集成到系统的节点执行框架中
    /// </summary>
    public class DataFilterNodeProcessor : INodeProcessor
    {
        private readonly DataFilterProcessor _processor;

        public DataFilterNodeProcessor()
        {
            _processor = new DataFilterProcessor();
        }

        /// <summary>
        /// 支持的节点类型
        /// </summary>
        public ModuleType SupportedModuleType => ModuleType.DataFilter;

        /// <summary>
        /// 执行节点处理逻辑
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>处理结果</returns>
        public Interfaces.NodeExecutionResult Execute(INode node, Dictionary<string, object> inputData)
        {
            var result = new Interfaces.NodeExecutionResult
            {
                IsSuccess = false,
                ExecutionTime = TimeSpan.Zero
            };

            try
            {
                // 提取输入数据
                var dataRows = ExtractDataRows(inputData);
                if (dataRows == null || !dataRows.Any())
                {
                    result.ErrorMessage = "输入数据为空或格式不正确";
                    return result;
                }

                // 获取节点属性
                var properties = GetNodeProperties(node);

                // 执行数据筛选和统计
                var filterResult = _processor.ProcessData(dataRows, properties);

                // 转换结果
                result.IsSuccess = filterResult.IsSuccess;
                result.ErrorMessage = filterResult.ErrorMessage;
                result.ExecutionTime = filterResult.ExecutionTime;

                if (filterResult.IsSuccess)
                {
                    // 设置输出数据
                    result.OutputData["filteredData"] = filterResult.FilteredData;
                    result.OutputData["originalCount"] = filterResult.OriginalRecordCount;
                    result.OutputData["filteredCount"] = filterResult.FilteredRecordCount;

                    // 如果有统计结果，也添加到输出
                    if (filterResult.AggregationResult != null)
                    {
                        result.OutputData["aggregationResult"] = filterResult.AggregationResult;
                        result.OutputData["statistics"] = filterResult.AggregationResult.Results
                            .ToDictionary(r => $"{r.Function}_{r.FieldName}", r => r.Value);
                    }

                    // 设置处理摘要
                    result.Metadata["Summary"] = filterResult.GetSummary();
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"节点执行失败: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"DataFilter节点执行异常: {ex}");
            }

            return result;
        }

        /// <summary>
        /// 验证节点配置
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <returns>验证结果</returns>
        public Models.ValidationResult ValidateConfiguration(INode node)
        {
            var result = new Models.ValidationResult();

            try
            {
                var properties = GetNodeProperties(node);
                var config = ParseNodeConfiguration(properties);

                // 验证筛选条件
                if (string.IsNullOrWhiteSpace(config.FilterCondition))
                {
                    result.AddError("筛选条件不能为空");
                    return result;
                }

                // 验证表达式语法
                var expressionEngine = new Services.ExpressionEngine();
                var expressionResult = expressionEngine.ValidateExpression(config.FilterCondition);
                if (!expressionResult.IsValid)
                {
                    result.AddError($"表达式语法错误: {expressionResult.ErrorMessage}");
                    return result;
                }

                // 验证统计配置
                if (config.AggregationEnabled)
                {
                    if (config.AggregationFunction != "count" && string.IsNullOrWhiteSpace(config.AggregationField))
                    {
                        result.AddError("统计函数需要指定字段名称");
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                result.AddError($"配置验证失败: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 获取输入端口定义
        /// </summary>
        /// <returns>输入端口列表</returns>
        public IEnumerable<IPortDefinition> GetInputPorts()
        {
            return new[]
            {
                new PortDefinition
                {
                    Id = "input",
                    Name = "数据输入",
                    Description = "需要筛选的数据集合",
                    DataType = "DataTable",
                    IsRequired = true,
                    SupportedFormats = new[] { "Dictionary[]", "DataTable", "List<Dictionary>" }
                }
            };
        }

        /// <summary>
        /// 获取输出端口定义
        /// </summary>
        /// <returns>输出端口列表</returns>
        public IEnumerable<IPortDefinition> GetOutputPorts()
        {
            return new[]
            {
                new PortDefinition
                {
                    Id = "output",
                    Name = "筛选结果",
                    Description = "筛选后的数据集合",
                    DataType = "DataTable",
                    IsRequired = false,
                    SupportedFormats = new[] { "Dictionary[]", "DataTable", "List<Dictionary>" }
                },
                new PortDefinition
                {
                    Id = "statistics",
                    Name = "统计结果",
                    Description = "统计计算结果",
                    DataType = "Statistics",
                    IsRequired = false,
                    SupportedFormats = new[] { "AggregationResultSet", "Dictionary" }
                }
            };
        }

        #region 私有方法

        /// <summary>
        /// 解析节点配置
        /// </summary>
        private DataFilterConfiguration ParseNodeConfiguration(Dictionary<string, object?> properties)
        {
            var config = new DataFilterConfiguration();

            // 基础筛选配置
            config.ExpressionMode = GetStringValue(properties, "expressionMode", "simple");
            config.FilterCondition = GetStringValue(properties, "filterCondition", "");
            config.CaseSensitive = GetBoolValue(properties, "caseSensitive", false);
            config.FilterType = GetStringValue(properties, "filterType", "include");

            // 统计配置
            config.AggregationEnabled = GetBoolValue(properties, "aggregationEnabled", false);
            config.AggregationFunction = GetStringValue(properties, "aggregationFunction", "count");
            config.AggregationField = GetStringValue(properties, "aggregationField", "");

            // 显示配置
            config.DisplayMode = GetStringValue(properties, "displayMode", "none");
            config.PreviewEnabled = GetBoolValue(properties, "previewEnabled", false);
            config.MaxPreviewRows = GetIntValue(properties, "maxPreviewRows", 100);

            // 高级选项
            config.ExportEnabled = GetBoolValue(properties, "exportEnabled", true);
            config.CacheResults = GetBoolValue(properties, "cacheResults", true);

            return config;
        }

        /// <summary>
        /// 从输入数据中提取数据行
        /// </summary>
        private List<Dictionary<string, object?>>? ExtractDataRows(Dictionary<string, object> inputData)
        {
            // 尝试从不同的键中获取数据
            var possibleKeys = new[] { "input", "data", "rows", "dataset", "table" };

            foreach (var key in possibleKeys)
            {
                if (inputData.TryGetValue(key, out var value))
                {
                    return ConvertToDataRows(value);
                }
            }

            // 如果没有找到标准键，尝试第一个值
            if (inputData.Values.FirstOrDefault() is var firstValue && firstValue != null)
            {
                return ConvertToDataRows(firstValue);
            }

            return null;
        }

        /// <summary>
        /// 转换对象为数据行集合
        /// </summary>
        private List<Dictionary<string, object?>>? ConvertToDataRows(object value)
        {
            return value switch
            {
                List<Dictionary<string, object?>> list => list,
                Dictionary<string, object?>[] array => array.ToList(),
                System.Data.DataTable dataTable => ConvertDataTableToRows(dataTable),
                IEnumerable<Dictionary<string, object?>> enumerable => enumerable.ToList(),
                _ => null
            };
        }

        /// <summary>
        /// 转换 DataTable 为字典集合
        /// </summary>
        private List<Dictionary<string, object?>> ConvertDataTableToRows(System.Data.DataTable dataTable)
        {
            var rows = new List<Dictionary<string, object?>>();

            foreach (System.Data.DataRow row in dataTable.Rows)
            {
                var dict = new Dictionary<string, object?>();
                foreach (System.Data.DataColumn column in dataTable.Columns)
                {
                    dict[column.ColumnName] = row[column] == DBNull.Value ? null : row[column];
                }
                rows.Add(dict);
            }

            return rows;
        }

        /// <summary>
        /// 获取节点属性
        /// </summary>
        private Dictionary<string, object?> GetNodeProperties(INode node)
        {
            var properties = new Dictionary<string, object?>();

            if (node.Properties is INodeProperties nodeProperties)
            {
                // 这里需要根据实际的属性接口来获取属性值
                // 暂时使用反射或已知的属性访问方式
                try
                {
                    // 假设有一个方法可以获取所有属性值
                    // 尝试获取常见属性
                    TryAddProperty(properties, "expressionMode", nodeProperties);
                    TryAddProperty(properties, "filterCondition", nodeProperties);
                    TryAddProperty(properties, "caseSensitive", nodeProperties);
                    TryAddProperty(properties, "filterType", nodeProperties);
                    TryAddProperty(properties, "aggregationEnabled", nodeProperties);
                    TryAddProperty(properties, "aggregationFunction", nodeProperties);
                    TryAddProperty(properties, "aggregationField", nodeProperties);
                    TryAddProperty(properties, "displayMode", nodeProperties);
                    TryAddProperty(properties, "previewEnabled", nodeProperties);
                    TryAddProperty(properties, "maxPreviewRows", nodeProperties);
                    TryAddProperty(properties, "exportEnabled", nodeProperties);
                    TryAddProperty(properties, "cacheResults", nodeProperties);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"获取节点属性失败: {ex.Message}");
                }
            }

            return properties;
        }

        /// <summary>
        /// 尝试添加属性
        /// </summary>
        private void TryAddProperty(Dictionary<string, object?> properties, string propertyName, object source)
        {
            try
            {
                var value = source.GetType().GetProperty(propertyName)?.GetValue(source);
                properties[propertyName] = value;
            }
            catch
            {
                // 忽略获取失败的属性
            }
        }

        /// <summary>
        /// 获取字符串属性值
        /// </summary>
        private string GetStringValue(Dictionary<string, object?> properties, string key, string defaultValue)
        {
            return properties.TryGetValue(key, out var value) ? value?.ToString() ?? defaultValue : defaultValue;
        }

        /// <summary>
        /// 获取布尔属性值
        /// </summary>
        private bool GetBoolValue(Dictionary<string, object?> properties, string key, bool defaultValue)
        {
            if (properties.TryGetValue(key, out var value))
            {
                if (value is bool boolValue) return boolValue;
                if (bool.TryParse(value?.ToString(), out var parsedValue)) return parsedValue;
            }
            return defaultValue;
        }

        /// <summary>
        /// 获取整数属性值
        /// </summary>
        private int GetIntValue(Dictionary<string, object?> properties, string key, int defaultValue)
        {
            if (properties.TryGetValue(key, out var value))
            {
                if (value is int intValue) return intValue;
                if (int.TryParse(value?.ToString(), out var parsedValue)) return parsedValue;
            }
            return defaultValue;
        }

        #endregion
    }

    /// <summary>
    /// 端口定义实现
    /// </summary>
    public class PortDefinition : IPortDefinition
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsRequired { get; set; }
        public bool AllowMultipleConnections { get; set; } = false;
        public PortPosition Position { get; set; } = PortPosition.Left;
        public string[] SupportedFormats { get; set; } = Array.Empty<string>();
    }


}
