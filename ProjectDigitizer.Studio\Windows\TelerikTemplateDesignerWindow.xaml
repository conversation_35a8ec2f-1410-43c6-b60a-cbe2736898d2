<Window x:Class="ProjectDigitizer.Studio.Windows.TelerikTemplateDesignerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:telerik="http://schemas.telerik.com/2008/xaml/presentation"
        Title="智能文档模板设计器"
        Height="900"
        Width="1400"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        FontFamily="Segoe UI"
        FontSize="12"
        Loaded="Window_Loaded"
        Unloaded="Window_Unloaded">

    <Window.Resources>
        <telerik:IconSources x:Key="IconSources"
                             LightBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/MSOffice/"
                             DarkBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/MSOffice/Dark/"
                             ModernBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/MSOffice/Modern/"/>

        <telerik:IconSources x:Key="ShapesIconSources"
                             LightBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/Shapes/Light/"
                             DarkBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/Shapes/Dark/"
                             ModernBasePath="pack://application:,,,/Telerik.Windows.Controls.RichTextBox;component/RichTextBoxUI/Images/Shapes/Light/"/>

        <telerik:ThicknessToOrientedThicknessConverter x:Key="ThicknessToOrientedThicknessConverter"/>
        <telerik:FontFamiliesProvider x:Key="FontFamiliesProvider"/>
        <telerik:EditingContextTypeToBoolConverter x:Key="EditingContextTypeToBoolConverter"/>
    </Window.Resources>

    <Grid x:Name="MainGrid">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主工作区域 - 包含左侧面板和主编辑区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280"
                                  MinWidth="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="300"
                                  MinWidth="250"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧面板 -->
            <Grid Grid.Column="0"
                  Background="#F8F9FA">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="300"
                                   MinHeight="200"/>
                </Grid.RowDefinitions>

                <!-- 章节导航面板 -->
                <GroupBox Grid.Row="0"
                          Header="章节导航"
                          Margin="5"
                          Padding="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 章节工具栏 -->
                        <StackPanel Grid.Row="0"
                                    Orientation="Horizontal"
                                    Margin="0,0,0,5">
                            <Button x:Name="AddChapterButton"
                                    Content="添加章节"
                                    Width="70"
                                    Height="25"
                                    Click="AddChapter_Click"
                                    Margin="0,0,5,0"/>
                            <Button x:Name="DeleteChapterButton"
                                    Content="删除"
                                    Width="50"
                                    Height="25"
                                    Click="DeleteChapter_Click"/>
                        </StackPanel>

                        <!-- 章节树 -->
                        <telerik:RadTreeView x:Name="ChapterTreeView"
                                             Grid.Row="1"
                                             SelectionChanged="ChapterTreeView_SelectionChanged"
                                             Background="White"
                                             BorderBrush="#E1E1E1"
                                             BorderThickness="1">
                            <telerik:RadTreeViewItem Header="第一章 项目概述"
                                                     IsExpanded="True">
                                <telerik:RadTreeViewItem Header="1.1 项目背景"/>
                                <telerik:RadTreeViewItem Header="1.2 项目目标"/>
                                <telerik:RadTreeViewItem Header="1.3 项目范围"/>
                            </telerik:RadTreeViewItem>
                            <telerik:RadTreeViewItem Header="第二章 技术规范"
                                                     IsExpanded="True">
                                <telerik:RadTreeViewItem Header="2.1 系统架构"/>
                                <telerik:RadTreeViewItem Header="2.2 技术要求"/>
                            </telerik:RadTreeViewItem>
                            <telerik:RadTreeViewItem Header="第三章 实施计划">
                                <telerik:RadTreeViewItem Header="3.1 时间安排"/>
                                <telerik:RadTreeViewItem Header="3.2 资源配置"/>
                            </telerik:RadTreeViewItem>
                        </telerik:RadTreeView>
                    </Grid>
                </GroupBox>

                <!-- 分隔条 -->
                <GridSplitter Grid.Row="1"
                              Height="5"
                              HorizontalAlignment="Stretch"
                              Background="#E1E1E1"
                              ResizeBehavior="PreviousAndNext"/>

                <!-- 域关联索引管理面板 -->
                <GroupBox Grid.Row="2"
                          Header="域关联索引"
                          Margin="5"
                          Padding="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 域管理工具栏 -->
                        <StackPanel Grid.Row="0"
                                    Orientation="Horizontal"
                                    Margin="0,0,0,5">
                            <Button x:Name="AddFieldButton"
                                    Content="添加域"
                                    Width="60"
                                    Height="25"
                                    Click="AddField_Click"
                                    Margin="0,0,5,0"/>
                            <Button x:Name="RefreshFieldsButton"
                                    Content="刷新"
                                    Width="50"
                                    Height="25"
                                    Click="RefreshFields_Click"/>
                        </StackPanel>

                        <!-- 域列表 -->
                        <telerik:RadListBox x:Name="FieldListBox"
                                            Grid.Row="1"
                                            Background="White"
                                            BorderBrush="#E1E1E1"
                                            BorderThickness="1"
                                            MouseDoubleClick="FieldListBox_MouseDoubleClick">
                            <!-- 域列表项将在代码中动态添加 -->
                        </telerik:RadListBox>

                        <!-- 域信息显示 -->
                        <TextBlock Grid.Row="2"
                                   x:Name="FieldInfoTextBlock"
                                   Text="双击域名称插入到文档中"
                                   FontSize="10"
                                   Foreground="Gray"
                                   Margin="0,5,0,0"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </GroupBox>
            </Grid>

            <!-- 垂直分隔条 -->
            <GridSplitter Grid.Column="1"
                          Width="5"
                          HorizontalAlignment="Center"
                          Background="#E1E1E1"
                          ResizeBehavior="PreviousAndNext"/>

            <!-- 主编辑区域 -->
            <telerik:DocumentRuler Grid.Column="2"
                                   Background="White"
                                   BorderBrush="#E1E1E1"
                                   BorderThickness="1,1,0,0">
                <telerik:RadRichTextBox x:Name="TelerikRichTextBox"
                                        ShowComments="True"
                                        LayoutMode="Paged"
                                        Drop="TelerikRichTextBox_Drop"
                                        AllowDrop="True"
                                        Background="White"
                                        BorderThickness="0"
                                        Margin="20,20,20,0"
                                        DocumentContentChanged="TelerikRichTextBox_DocumentContentChanged"/>
            </telerik:DocumentRuler>

            <!-- 右侧垂直分隔条 -->
            <GridSplitter Grid.Column="3"
                          Width="5"
                          HorizontalAlignment="Center"
                          Background="#E1E1E1"
                          ResizeBehavior="PreviousAndNext"/>

            <!-- 右侧功能面板 -->
            <Grid Grid.Column="4"
                  Background="#F8F9FA">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 文档验证面板 -->
                <GroupBox Grid.Row="0"
                          Header="文档验证"
                          Margin="5"
                          Padding="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 验证工具栏 -->
                        <StackPanel Grid.Row="0"
                                    Orientation="Horizontal"
                                    Margin="0,0,0,5">
                            <Button x:Name="ValidateDocumentButton"
                                    Content="验证文档"
                                    Width="70"
                                    Height="25"
                                    Click="ValidateDocument_Click"
                                    Margin="0,0,5,0"/>
                            <Button x:Name="CheckSpellingButton"
                                    Content="拼写检查"
                                    Width="70"
                                    Height="25"
                                    Click="CheckSpelling_Click"/>
                        </StackPanel>

                        <!-- 验证结果列表 -->
                        <telerik:RadListBox x:Name="ValidationResultsListBox"
                                            Grid.Row="1"
                                            Background="White"
                                            BorderBrush="#E1E1E1"
                                            BorderThickness="1"
                                            MouseDoubleClick="ValidationResult_DoubleClick">
                            <!-- 验证结果将在代码中动态添加 -->
                        </telerik:RadListBox>

                        <!-- 验证状态显示 -->
                        <TextBlock Grid.Row="2"
                                   x:Name="ValidationStatusTextBlock"
                                   Text="点击验证文档开始检查"
                                   FontSize="10"
                                   Foreground="Gray"
                                   Margin="0,5,0,0"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </GroupBox>

                <!-- 分隔条 -->
                <GridSplitter Grid.Row="1"
                              Height="5"
                              HorizontalAlignment="Stretch"
                              Background="#E1E1E1"
                              ResizeBehavior="PreviousAndNext"/>

                <!-- 文档统计面板 -->
                <GroupBox Grid.Row="2"
                          Header="文档统计"
                          Margin="5"
                          Padding="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 统计工具栏 -->
                        <StackPanel Grid.Row="0"
                                    Orientation="Horizontal"
                                    Margin="0,0,0,5">
                            <Button x:Name="RefreshStatsButton"
                                    Content="刷新统计"
                                    Width="70"
                                    Height="25"
                                    Click="RefreshStats_Click"
                                    Margin="0,0,5,0"/>
                            <Button x:Name="ExportStatsButton"
                                    Content="导出统计"
                                    Width="70"
                                    Height="25"
                                    Click="ExportStats_Click"/>
                        </StackPanel>

                        <!-- 统计信息显示 -->
                        <ScrollViewer Grid.Row="1"
                                      VerticalScrollBarVisibility="Auto"
                                      Background="White"
                                      BorderBrush="#E1E1E1"
                                      BorderThickness="1"
                                      Margin="0,0,0,5">
                            <StackPanel x:Name="StatisticsPanel"
                                        Margin="10">
                                <!-- 统计信息将在代码中动态添加 -->
                            </StackPanel>
                        </ScrollViewer>

                        <!-- 统计状态显示 -->
                        <TextBlock Grid.Row="2"
                                   x:Name="StatisticsStatusTextBlock"
                                   Text="点击刷新统计获取最新数据"
                                   FontSize="10"
                                   Foreground="Gray"
                                   Margin="0,5,0,0"
                                   TextWrapping="Wrap"/>
                    </Grid>
                </GroupBox>
            </Grid>
        </Grid>



        <!-- Ribbon工具栏 - 基于官方示例 -->
        <telerik:RadRichTextBoxRibbonUI x:Name="RibbonUI"
                                        Grid.Row="0"
                                        BackstageClippingElement="{Binding ElementName=MainGrid}"
                                        CollapseThresholdSize="50,50"
                                        DataContext="{Binding Commands, ElementName=TelerikRichTextBox}"
                                        ApplicationName="智能文档模板设计器">
            <telerik:RadRichTextBoxRibbonUI.Resources>
                <telerik:FontFamiliesProvider x:Key="FontFamiliesProvider"/>
                <telerik:EditingContextTypeToBoolConverter x:Key="EditingContextTypeToBoolConverter"/>
            </telerik:RadRichTextBoxRibbonUI.Resources>

            <!-- 上下文组 -->
            <telerik:RadRichTextBoxRibbonUI.ContextualGroups>
                <telerik:RadRibbonContextualGroup x:Name="TableTools"
                                                  Header="Table Tools">
                    <telerik:RadRibbonContextualGroup.IsActive>
                        <Binding Converter="{StaticResource EditingContextTypeToBoolConverter}"
                                 Mode="OneWay"
                                 Path="AssociatedRichTextBox.CurrentEditingContext.Type">
                            <Binding.ConverterParameter>
                                <telerik:EditingContextTypes>Table</telerik:EditingContextTypes>
                            </Binding.ConverterParameter>
                        </Binding>
                    </telerik:RadRibbonContextualGroup.IsActive>
                </telerik:RadRibbonContextualGroup>
            </telerik:RadRichTextBoxRibbonUI.ContextualGroups>

            <!-- 快速访问工具栏 -->
            <telerik:RadRichTextBoxRibbonUI.QuickAccessToolBar>
                <telerik:QuickAccessToolBar>
                    <telerik:RadRibbonButton telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding SaveCommand}"
                                             Size="Small"
                                             SmallImage="{telerik:IconResource IconRelativePath=16/save.png, IconSources={StaticResource IconSources}}"
                                             Text="保存"/>
                    <telerik:RadRibbonButton telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding UndoCommand}"
                                             Size="Small"
                                             SmallImage="{telerik:IconResource IconRelativePath=16/undo.png, IconSources={StaticResource IconSources}}"
                                             Text="撤销"/>
                    <telerik:RadRibbonButton telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding RedoCommand}"
                                             Size="Small"
                                             SmallImage="{telerik:IconResource IconRelativePath=16/redo.png, IconSources={StaticResource IconSources}}"
                                             Text="重做"/>
                </telerik:QuickAccessToolBar>
            </telerik:RadRichTextBoxRibbonUI.QuickAccessToolBar>

            <!-- 主选项卡 - 完整的Word-like功能 -->
            <telerik:RadRibbonTab Header="开始">
                <!-- 剪贴板组 -->
                <telerik:RadRibbonGroup Header="剪贴板">
                    <telerik:RadRibbonDropDownButton telerik:ScreenTip.Description="粘贴剪贴板的内容。"
                                                     LargeImage="{telerik:IconResource IconRelativePath=32/paste.png,IconSources={StaticResource IconSources}}"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding PasteCommand}"
                                                     Size="Large"
                                                     Text="粘贴"
                                                     telerik:ScreenTip.Title="粘贴">
                        <telerik:RadRibbonDropDownButton.DropDownContent>
                            <telerik:PasteOptionsPicker Owner="{Binding AssociatedRichTextBox}"/>
                        </telerik:RadRibbonDropDownButton.DropDownContent>
                    </telerik:RadRibbonDropDownButton>
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 telerik:ScreenTip.Description="剪切选定内容并放到剪贴板。"
                                                 telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding CutCommand}"
                                                 Size="Medium"
                                                 SmallImage="{telerik:IconResource IconRelativePath=16/cut.png, IconSources={StaticResource IconSources}}"
                                                 Text="剪切"
                                                 telerik:ScreenTip.Title="剪切"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 telerik:ScreenTip.Description="复制选定内容并放到剪贴板。"
                                                 telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding CopyCommand}"
                                                 Size="Medium"
                                                 SmallImage="{telerik:IconResource IconRelativePath=16/copy.png, IconSources={StaticResource IconSources}}"
                                                 Text="复制"
                                                 telerik:ScreenTip.Title="复制"/>
                        <telerik:FormatPainterButton CollapseToSmall="WhenGroupIsMedium"
                                                     telerik:ScreenTip.Description="将特定选定内容的外观应用到文档中的其他内容。"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangeFormatPainterStateCommand}"
                                                     Size="Medium"
                                                     SmallImage="{telerik:IconResource IconRelativePath=16/FormatPainter.png,IconSources={StaticResource IconSources}}"
                                                     Text="格式刷"
                                                     telerik:ScreenTip.Title="格式刷"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>

                <!-- 字体组 -->
                <telerik:RadRibbonGroup DialogLauncherVisibility="Visible"
                                        telerik:ScreenTip.Description="显示字体对话框。"
                                        Header="字体"
                                        telerik:ScreenTip.Icon="{telerik:IconResource IconRelativePath=FontDialog.png, IconSources={StaticResource IconSources}}"
                                        telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowFontPropertiesDialogCommand}"
                                        telerik:ScreenTip.Title="字体">
                    <telerik:RadOrderedWrapPanel>
                        <StackPanel Orientation="Horizontal">
                            <telerik:RadRibbonComboBox CanAutocompleteSelectItems="False"
                                                       CanKeyboardNavigationSelectItems="False"
                                                       CommandParameter="{Binding SelectedValue, RelativeSource={RelativeSource Self}}"
                                                       telerik:ScreenTip.Description="Change the font family."
                                                       IsReadOnly="True"
                                                       ItemsSource="{Binding RegisteredFonts, Source={StaticResource FontFamiliesProvider}}"
                                                       MaxDropDownHeight="400"
                                                       OpenDropDownOnFocus="True"
                                                       telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangeFontFamilyCommand}"
                                                       telerik:ScreenTip.Title="Font"
                                                       Width="132"/>
                            <telerik:RadRibbonComboBox CanAutocompleteSelectItems="False"
                                                       CanKeyboardNavigationSelectItems="False"
                                                       CommandParameter="{Binding SelectedItem.Tag, RelativeSource={RelativeSource Self}}"
                                                       telerik:ScreenTip.Description="Change the font size."
                                                       IsEditable="True"
                                                       IsReadOnly="True"
                                                       IsTextSearchEnabled="False"
                                                       MaxDropDownHeight="400"
                                                       OpenDropDownOnFocus="True"
                                                       telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangeFontSizeCommand}"
                                                       telerik:ScreenTip.Title="Font Size"
                                                       Width="45"
                                                       x:Name="fontSizeComboBox">
                                <telerik:RadRibbonComboBoxItem Content="8"
                                                               Tag="10.67"/>
                                <telerik:RadRibbonComboBoxItem Content="9"
                                                               Tag="12"/>
                                <telerik:RadRibbonComboBoxItem Content="10"
                                                               Tag="13.34"/>
                                <telerik:RadRibbonComboBoxItem Content="11"
                                                               Tag="14.67"/>
                                <telerik:RadRibbonComboBoxItem Content="12"
                                                               Tag="16"/>
                                <telerik:RadRibbonComboBoxItem Content="14"
                                                               Tag="18.67"/>
                                <telerik:RadRibbonComboBoxItem Content="16"
                                                               Tag="21.33"/>
                                <telerik:RadRibbonComboBoxItem Content="18"
                                                               Tag="24"/>
                                <telerik:RadRibbonComboBoxItem Content="20"
                                                               Tag="26.67"/>
                                <telerik:RadRibbonComboBoxItem Content="22"
                                                               Tag="29.33"/>
                                <telerik:RadRibbonComboBoxItem Content="24"
                                                               Tag="32"/>
                                <telerik:RadRibbonComboBoxItem Content="26"
                                                               Tag="34.67"/>
                                <telerik:RadRibbonComboBoxItem Content="28"
                                                               Tag="37.33"/>
                                <telerik:RadRibbonComboBoxItem Content="36"
                                                               Tag="48"/>
                                <telerik:RadRibbonComboBoxItem Content="48"
                                                               Tag="64"/>
                                <telerik:RadRibbonComboBoxItem Content="72"
                                                               Tag="96"/>
                            </telerik:RadRibbonComboBox>
                        </StackPanel>
                        <telerik:RadButtonGroup>
                            <telerik:RadRibbonButton telerik:ScreenTip.Description="增大字体。"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding IncrementFontSizeCommand}"
                                                     Size="Small"
                                                     SmallImage="{telerik:IconResource IconRelativePath=16/font-increasesize.png, IconSources={StaticResource IconSources}}"
                                                     telerik:ScreenTip.Title="增大字体"/>
                            <telerik:RadRibbonButton telerik:ScreenTip.Description="减小字体。"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding DecrementFontSizeCommand}"
                                                     Size="Small"
                                                     SmallImage="{telerik:IconResource IconRelativePath=16/font-decreasesize.png, IconSources={StaticResource IconSources}}"
                                                     telerik:ScreenTip.Title="减小字体"/>
                        </telerik:RadButtonGroup>
                        <telerik:RadButtonGroup>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="加粗选定文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleBoldCommand}"
                                                           Size="Small"
                                                           SmallImage="{telerik:IconResource IconRelativePath=16/bold.png, IconSources={StaticResource IconSources}}"
                                                           telerik:ScreenTip.Title="加粗"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="倾斜选定文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleItalicCommand}"
                                                           Size="Small"
                                                           SmallImage="{telerik:IconResource IconRelativePath=16/italic.png, IconSources={StaticResource IconSources}}"
                                                           telerik:ScreenTip.Title="倾斜"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="为选定文本添加下划线。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleUnderlineCommand}"
                                                           Size="Small"
                                                           SmallImage="{telerik:IconResource IconRelativePath=16/underline.png, IconSources={StaticResource IconSources}}"
                                                           telerik:ScreenTip.Title="下划线"/>
                        </telerik:RadButtonGroup>
                    </telerik:RadOrderedWrapPanel>
                </telerik:RadRibbonGroup>

                <!-- 段落组 -->
                <telerik:RadRibbonGroup DialogLauncherVisibility="Visible"
                                        Header="段落"
                                        telerik:ScreenTip.Description="显示段落对话框。"
                                        telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowParagraphPropertiesDialogCommand}"
                                        telerik:ScreenTip.Title="段落">
                    <telerik:RadOrderedWrapPanel>
                        <!-- 对齐按钮组 -->
                        <telerik:RadButtonGroup>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="左对齐文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleTextAlignmentLeftCommand}"
                                                           Size="Small"
                                                           Text="左对齐"
                                                           telerik:ScreenTip.Title="左对齐"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="居中对齐文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleTextAlignmentCenterCommand}"
                                                           Size="Small"
                                                           Text="居中"
                                                           telerik:ScreenTip.Title="居中"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="右对齐文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleTextAlignmentRightCommand}"
                                                           Size="Small"
                                                           Text="右对齐"
                                                           telerik:ScreenTip.Title="右对齐"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="两端对齐文本。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleTextAlignmentJustifyCommand}"
                                                           Size="Small"
                                                           Text="两端对齐"
                                                           telerik:ScreenTip.Title="两端对齐"/>
                        </telerik:RadButtonGroup>
                        <!-- 列表按钮组 -->
                        <telerik:RadButtonGroup>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="创建项目符号列表。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleBulletsCommand}"
                                                           Size="Small"
                                                           Text="项目符号"
                                                           telerik:ScreenTip.Title="项目符号"/>
                            <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="创建编号列表。"
                                                           telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ToggleNumberedCommand}"
                                                           Size="Small"
                                                           Text="编号"
                                                           telerik:ScreenTip.Title="编号"/>
                        </telerik:RadButtonGroup>
                        <!-- 缩进按钮组 -->
                        <telerik:RadButtonGroup>
                            <telerik:RadRibbonButton telerik:ScreenTip.Description="减少段落缩进。"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding DecrementParagraphLeftIndentCommand}"
                                                     Size="Small"
                                                     Text="减少缩进"
                                                     telerik:ScreenTip.Title="减少缩进"/>
                            <telerik:RadRibbonButton telerik:ScreenTip.Description="增加段落缩进。"
                                                     telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding IncrementParagraphLeftIndentCommand}"
                                                     Size="Small"
                                                     Text="增加缩进"
                                                     telerik:ScreenTip.Title="增加缩进"/>
                        </telerik:RadButtonGroup>
                    </telerik:RadOrderedWrapPanel>
                </telerik:RadRibbonGroup>

                <!-- 样式组 -->
                <telerik:RadRibbonGroup Header="样式">
                    <telerik:StylesGallery AssociatedRichTextBox="{Binding ElementName=TelerikRichTextBox}"/>
                </telerik:RadRibbonGroup>

                <!-- 编辑组 -->
                <telerik:RadRibbonGroup Header="编辑">
                    <telerik:RadRibbonDropDownButton telerik:ScreenTip.Description="查找、替换或转到文档中的特定页面。"
                                                     Size="Large"
                                                     Text="查找"
                                                     telerik:ScreenTip.Title="查找">
                        <telerik:RadRibbonDropDownButton.DropDownContent>
                            <StackPanel>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowFindReplaceDialogCommand}"
                                                         Size="Medium"
                                                         Text="查找..."/>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowFindReplaceDialogCommand}"
                                                         Size="Medium"
                                                         Text="替换..."/>
                            </StackPanel>
                        </telerik:RadRibbonDropDownButton.DropDownContent>
                    </telerik:RadRibbonDropDownButton>
                </telerik:RadRibbonGroup>
            </telerik:RadRibbonTab>

            <!-- 插入选项卡 -->
            <telerik:RadRibbonTab Header="插入">
                <!-- 页面组 -->
                <telerik:RadRibbonGroup Header="页面">
                    <telerik:RadRibbonButton telerik:ScreenTip.Description="在当前位置插入分页符。"
                                             telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding InsertPageBreakCommand}"
                                             Size="Large"
                                             Text="分页符"
                                             telerik:ScreenTip.Title="分页符"/>
                </telerik:RadRibbonGroup>

                <!-- 表格组 -->
                <telerik:RadRibbonGroup Header="表格">
                    <telerik:RadRibbonDropDownButton CollapseToMedium="WhenGroupIsMedium"
                                                     Command="{Binding InsertTableCommand}"
                                                     telerik:ScreenTip.Description="在文档中插入表格。"
                                                     Size="Large"
                                                     Text="表格"
                                                     telerik:ScreenTip.Title="表格">
                        <telerik:RadRibbonDropDownButton.DropDownContent>
                            <StackPanel>
                                <telerik:RadRibbonButton ClickMode="Press">
                                    <telerik:TableSizePicker Command="{Binding InsertTableCommand}"/>
                                </telerik:RadRibbonButton>
                                <telerik:RadMenuItem IsSeparator="True"/>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowInsertTableDialogCommand}"
                                                         Size="Medium"
                                                         Text="插入表格..."/>
                            </StackPanel>
                        </telerik:RadRibbonDropDownButton.DropDownContent>
                    </telerik:RadRibbonDropDownButton>
                </telerik:RadRibbonGroup>

                <!-- 插图组 -->
                <telerik:RadRibbonGroup Header="插图">
                    <telerik:RadRibbonButton telerik:ScreenTip.Description="从文件插入图片。"
                                             telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowInsertImageDialogCommand}"
                                             Size="Large"
                                             Text="图片"
                                             telerik:ScreenTip.Title="图片"/>
                </telerik:RadRibbonGroup>

                <!-- 文本组 -->
                <telerik:RadRibbonGroup Header="文本">
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="InsertField_Click"
                                                 Size="Medium"
                                                 Text="域字段"
                                                 telerik:ScreenTip.Description="插入模板域字段。"
                                                 telerik:ScreenTip.Title="域字段"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowInsertDateTimeDialogCommand}"
                                                 Size="Medium"
                                                 Text="日期时间"
                                                 telerik:ScreenTip.Description="插入当前日期或时间。"
                                                 telerik:ScreenTip.Title="日期时间"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>

                <!-- 智能文档组 -->
                <telerik:RadRibbonGroup Header="智能文档">
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="InsertChapter_Click"
                                                 Size="Medium"
                                                 Text="插入章节"
                                                 telerik:ScreenTip.Description="在当前位置插入新章节。"
                                                 telerik:ScreenTip.Title="插入章节"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="UpdateFields_Click"
                                                 Size="Medium"
                                                 Text="更新域"
                                                 telerik:ScreenTip.Description="更新文档中所有域字段的值。"
                                                 telerik:ScreenTip.Title="更新域"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="InsertMathSymbol_Click"
                                                 Size="Medium"
                                                 Text="数学符号"
                                                 telerik:ScreenTip.Description="插入平方、立方等数学符号。"
                                                 telerik:ScreenTip.Title="数学符号"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>
            </telerik:RadRibbonTab>

            <!-- 页面布局选项卡 -->
            <telerik:RadRibbonTab Header="页面布局">
                <!-- 页面设置组 -->
                <telerik:RadRibbonGroup Header="页面设置"
                                        DialogLauncherVisibility="Visible"
                                        telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowDocumentPropertiesDialogCommand}">
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangePageOrientationCommand}"
                                                 CommandParameter="Portrait"
                                                 Size="Medium"
                                                 Text="纵向"
                                                 telerik:ScreenTip.Description="将页面方向更改为纵向。"
                                                 telerik:ScreenTip.Title="纵向"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangePageOrientationCommand}"
                                                 CommandParameter="Landscape"
                                                 Size="Medium"
                                                 Text="横向"
                                                 telerik:ScreenTip.Description="将页面方向更改为横向。"
                                                 telerik:ScreenTip.Title="横向"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>
            </telerik:RadRibbonTab>

            <!-- 智能文档选项卡 -->
            <telerik:RadRibbonTab Header="智能文档">
                <!-- 章节管理组 -->
                <telerik:RadRibbonGroup Header="章节管理">
                    <telerik:RadRibbonButton telerik:ScreenTip.Description="生成文档目录。"
                                             Click="GenerateTableOfContents_Click"
                                             Size="Large"
                                             Text="生成目录"
                                             telerik:ScreenTip.Title="生成目录"/>
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="AddChapter_Click"
                                                 Size="Medium"
                                                 Text="添加章节"
                                                 telerik:ScreenTip.Description="添加新的章节到导航树。"
                                                 telerik:ScreenTip.Title="添加章节"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="DeleteChapter_Click"
                                                 Size="Medium"
                                                 Text="删除章节"
                                                 telerik:ScreenTip.Description="删除选中的章节。"
                                                 telerik:ScreenTip.Title="删除章节"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>

                <!-- 域管理组 -->
                <telerik:RadRibbonGroup Header="域管理">
                    <telerik:RadRibbonButton telerik:ScreenTip.Description="更新文档中所有域字段的值。"
                                             Click="UpdateFields_Click"
                                             Size="Large"
                                             Text="更新所有域"
                                             telerik:ScreenTip.Title="更新所有域"/>
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="AddField_Click"
                                                 Size="Medium"
                                                 Text="添加域"
                                                 telerik:ScreenTip.Description="添加新的域字段到索引。"
                                                 telerik:ScreenTip.Title="添加域"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="ValidateFields_Click"
                                                 Size="Medium"
                                                 Text="验证域"
                                                 telerik:ScreenTip.Description="验证文档中的域字段。"
                                                 telerik:ScreenTip.Title="验证域"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>

                <!-- 导出组 -->
                <telerik:RadRibbonGroup Header="导出">
                    <telerik:RadRibbonDropDownButton telerik:ScreenTip.Description="导出智能文档到不同格式。"
                                                     Size="Large"
                                                     Text="导出文档"
                                                     telerik:ScreenTip.Title="导出文档">
                        <telerik:RadRibbonDropDownButton.DropDownContent>
                            <StackPanel>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         Click="ExportWord_Click"
                                                         Size="Medium"
                                                         Text="导出为Word (.docx)"/>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         Click="ExportPdf_Click"
                                                         Size="Medium"
                                                         Text="导出为PDF"/>
                                <telerik:RadRibbonButton HorizontalAlignment="Stretch"
                                                         Click="ExportTemplate_Click"
                                                         Size="Medium"
                                                         Text="导出为模板"/>
                            </StackPanel>
                        </telerik:RadRibbonDropDownButton.DropDownContent>
                    </telerik:RadRibbonDropDownButton>
                </telerik:RadRibbonGroup>

                <!-- 工具组 -->
                <telerik:RadRibbonGroup Header="工具">
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="Preview_Click"
                                                 Size="Medium"
                                                 Text="预览"
                                                 telerik:ScreenTip.Description="预览文档效果。"
                                                 telerik:ScreenTip.Title="预览"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="ValidateTemplate_Click"
                                                 Size="Medium"
                                                 Text="验证模板"
                                                 telerik:ScreenTip.Description="验证模板的完整性。"
                                                 telerik:ScreenTip.Title="验证模板"/>
                        <telerik:RadRibbonButton CollapseToSmall="WhenGroupIsMedium"
                                                 Click="ShowStatistics_Click"
                                                 Size="Medium"
                                                 Text="统计信息"
                                                 telerik:ScreenTip.Description="显示文档统计信息。"
                                                 telerik:ScreenTip.Title="统计信息"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>
            </telerik:RadRibbonTab>

            <!-- 视图选项卡 -->
            <telerik:RadRibbonTab Header="视图">
                <!-- 文档视图组 -->
                <telerik:RadRibbonGroup Header="文档视图">
                    <telerik:RadButtonGroup>
                        <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="以页面布局显示文档。"
                                                       telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangeDocumentLayoutModeCommand}"
                                                       CommandParameter="Paged"
                                                       Size="Large"
                                                       Text="页面视图"
                                                       telerik:ScreenTip.Title="页面视图"/>
                        <telerik:RadRibbonToggleButton telerik:ScreenTip.Description="以Web布局显示文档。"
                                                       telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ChangeDocumentLayoutModeCommand}"
                                                       CommandParameter="Flow"
                                                       Size="Large"
                                                       Text="Web视图"
                                                       telerik:ScreenTip.Title="Web视图"/>
                    </telerik:RadButtonGroup>
                </telerik:RadRibbonGroup>

                <!-- 显示组 -->
                <telerik:RadRibbonGroup Header="显示">
                    <telerik:RadCollapsiblePanel>
                        <telerik:RadRibbonToggleButton CollapseToSmall="WhenGroupIsMedium"
                                                       IsChecked="{Binding ElementName=TelerikRichTextBox, Path=ShowComments}"
                                                       Size="Medium"
                                                       Text="批注"
                                                       telerik:ScreenTip.Description="显示或隐藏批注。"
                                                       telerik:ScreenTip.Title="批注"/>
                    </telerik:RadCollapsiblePanel>
                </telerik:RadRibbonGroup>

                <!-- 缩放组 -->
                <telerik:RadRibbonGroup Header="缩放">
                    <telerik:RadRibbonButton telerik:ScreenTip.Description="打开缩放对话框。"
                                             telerik:RadRichTextBoxRibbonUI.RichTextCommand="{Binding ShowZoomDialogCommand}"
                                             Size="Large"
                                             Text="缩放"
                                             telerik:ScreenTip.Title="缩放"/>
                </telerik:RadRibbonGroup>
            </telerik:RadRibbonTab>
        </telerik:RadRichTextBoxRibbonUI>

        <!-- Word-like状态栏 -->
        <StatusBar x:Name="StatusBar"
                   Grid.Row="2"
                   Background="#F0F0F0"
                   BorderBrush="#E0E0E0"
                   BorderThickness="0,1,0,0"
                   Height="22">
            <StatusBarItem>
                <TextBlock Name="StatusTextBlock"
                           Text="就绪"
                           FontSize="11"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="WordCountDisplay"
                           Text="字数: 0"
                           FontSize="11"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Name="PageCountDisplay"
                           Text="第 1 页，共 1 页"
                           FontSize="11"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="ZoomTextBlock"
                               Text="100%"
                               FontSize="11"
                               Margin="0,0,5,0"/>
                    <Slider Name="ZoomSlider"
                            Width="100"
                            Minimum="10"
                            Maximum="500"
                            Value="100"
                            VerticalAlignment="Center"
                            ValueChanged="ZoomSlider_ValueChanged"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
