using System;
using System.Collections.Generic;
using System.Linq;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 验证结果类
    /// </summary>
    public class ValidationResult
    {
        private readonly List<string> _errors = new();
        private readonly List<string> _warnings = new();

        /// <summary>
        /// 是否验证成功（无错误）
        /// </summary>
        public bool IsValid => !_errors.Any();

        /// <summary>
        /// 错误列表
        /// </summary>
        public IReadOnlyList<string> Errors => _errors.AsReadOnly();

        /// <summary>
        /// 警告列表
        /// </summary>
        public IReadOnlyList<string> Warnings => _warnings.AsReadOnly();

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => _warnings.Any();

        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount => _errors.Count;

        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount => _warnings.Count;

        /// <summary>
        /// 添加错误
        /// </summary>
        /// <param name="error">错误信息</param>
        public void AddError(string error)
        {
            if (!string.IsNullOrEmpty(error))
            {
                _errors.Add(error);
            }
        }

        /// <summary>
        /// 添加警告
        /// </summary>
        /// <param name="warning">警告信息</param>
        public void AddWarning(string warning)
        {
            if (!string.IsNullOrEmpty(warning))
            {
                _warnings.Add(warning);
            }
        }

        /// <summary>
        /// 添加多个错误
        /// </summary>
        /// <param name="errors">错误列表</param>
        public void AddErrors(IEnumerable<string> errors)
        {
            if (errors != null)
            {
                foreach (var error in errors.Where(e => !string.IsNullOrEmpty(e)))
                {
                    _errors.Add(error);
                }
            }
        }

        /// <summary>
        /// 添加多个警告
        /// </summary>
        /// <param name="warnings">警告列表</param>
        public void AddWarnings(IEnumerable<string> warnings)
        {
            if (warnings != null)
            {
                foreach (var warning in warnings.Where(w => !string.IsNullOrEmpty(w)))
                {
                    _warnings.Add(warning);
                }
            }
        }

        /// <summary>
        /// 合并另一个验证结果
        /// </summary>
        /// <param name="other">其他验证结果</param>
        public void Merge(ValidationResult other)
        {
            if (other != null)
            {
                AddErrors(other.Errors);
                AddWarnings(other.Warnings);
            }
        }

        /// <summary>
        /// 清除所有错误和警告
        /// </summary>
        public void Clear()
        {
            _errors.Clear();
            _warnings.Clear();
        }

        /// <summary>
        /// 获取所有消息的摘要
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            var parts = new List<string>();

            if (_errors.Any())
            {
                parts.Add($"{_errors.Count} 个错误");
            }

            if (_warnings.Any())
            {
                parts.Add($"{_warnings.Count} 个警告");
            }

            return parts.Any() ? string.Join(", ", parts) : "验证通过";
        }

        /// <summary>
        /// 获取详细的验证报告
        /// </summary>
        /// <returns>详细报告</returns>
        public string GetDetailedReport()
        {
            var report = new List<string>();

            if (_errors.Any())
            {
                report.Add("错误:");
                for (int i = 0; i < _errors.Count; i++)
                {
                    report.Add($"  {i + 1}. {_errors[i]}");
                }
            }

            if (_warnings.Any())
            {
                if (report.Any())
                {
                    report.Add("");
                }
                report.Add("警告:");
                for (int i = 0; i < _warnings.Count; i++)
                {
                    report.Add($"  {i + 1}. {_warnings[i]}");
                }
            }

            return report.Any() ? string.Join(Environment.NewLine, report) : "验证通过，无错误或警告。";
        }

        /// <summary>
        /// 创建成功的验证结果
        /// </summary>
        /// <returns>成功的验证结果</returns>
        public static ValidationResult Success()
        {
            return new ValidationResult();
        }

        /// <summary>
        /// 创建包含错误的验证结果
        /// </summary>
        /// <param name="error">错误信息</param>
        /// <returns>包含错误的验证结果</returns>
        public static ValidationResult Error(string error)
        {
            var result = new ValidationResult();
            result.AddError(error);
            return result;
        }

        /// <summary>
        /// 创建包含多个错误的验证结果
        /// </summary>
        /// <param name="errors">错误列表</param>
        /// <returns>包含错误的验证结果</returns>
        public static ValidationResult WithErrors(params string[] errors)
        {
            var result = new ValidationResult();
            result.AddErrors(errors);
            return result;
        }

        /// <summary>
        /// 创建包含警告的验证结果
        /// </summary>
        /// <param name="warning">警告信息</param>
        /// <returns>包含警告的验证结果</returns>
        public static ValidationResult Warning(string warning)
        {
            var result = new ValidationResult();
            result.AddWarning(warning);
            return result;
        }

        /// <summary>
        /// 重写ToString方法
        /// </summary>
        /// <returns>验证结果的字符串表示</returns>
        public override string ToString()
        {
            return GetSummary();
        }
    }
}
