using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 统计聚合引擎
    /// 提供各种统计函数的计算功能
    /// </summary>
    public class AggregationEngine
    {
        /// <summary>
        /// 执行单个统计函数
        /// </summary>
        /// <param name="data">数据集合</param>
        /// <param name="config">统计配置</param>
        /// <returns>统计结果</returns>
        public AggregationResult ExecuteAggregation(
            IEnumerable<Dictionary<string, object?>> data, 
            AggregationConfig config)
        {
            var stopwatch = Stopwatch.StartNew();
            var result = new AggregationResult
            {
                Function = config.Function,
                FieldName = config.FieldName ?? string.Empty
            };

            try
            {
                if (!config.IsValid())
                {
                    result.IsSuccess = false;
                    result.ErrorMessage = "统计配置无效";
                    return result;
                }

                var dataList = data.ToList();
                result.RecordCount = dataList.Count;

                if (dataList.Count == 0)
                {
                    result.Value = GetDefaultValue(config.Function);
                    result.ValidValueCount = 0;
                    return result;
                }

                switch (config.Function)
                {
                    case AggregationFunction.Count:
                        ExecuteCount(dataList, config, result);
                        break;
                    case AggregationFunction.Sum:
                        ExecuteSum(dataList, config, result);
                        break;
                    case AggregationFunction.Average:
                        ExecuteAverage(dataList, config, result);
                        break;
                    case AggregationFunction.Min:
                        ExecuteMin(dataList, config, result);
                        break;
                    case AggregationFunction.Max:
                        ExecuteMax(dataList, config, result);
                        break;
                    default:
                        result.IsSuccess = false;
                        result.ErrorMessage = $"不支持的统计函数: {config.Function}";
                        break;
                }
            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrorMessage = $"统计执行失败: {ex.Message}";
            }
            finally
            {
                stopwatch.Stop();
                result.ExecutionTime = stopwatch.Elapsed;
            }

            return result;
        }

        /// <summary>
        /// 执行多个统计函数
        /// </summary>
        /// <param name="data">数据集合</param>
        /// <param name="configs">统计配置列表</param>
        /// <returns>统计结果集</returns>
        public AggregationResultSet ExecuteMultipleAggregations(
            IEnumerable<Dictionary<string, object?>> data,
            IEnumerable<AggregationConfig> configs)
        {
            var stopwatch = Stopwatch.StartNew();
            var resultSet = new AggregationResultSet();
            var dataList = data.ToList();
            
            resultSet.TotalRecords = dataList.Count;
            resultSet.FilteredRecords = dataList.Count;

            foreach (var config in configs)
            {
                var result = ExecuteAggregation(dataList, config);
                resultSet.AddResult(result);
            }

            stopwatch.Stop();
            resultSet.TotalExecutionTime = stopwatch.Elapsed;

            return resultSet;
        }

        #region 私有方法 - 具体统计函数实现

        /// <summary>
        /// 执行计数统计
        /// </summary>
        private void ExecuteCount(List<Dictionary<string, object?>> data, AggregationConfig config, AggregationResult result)
        {
            if (string.IsNullOrEmpty(config.FieldName))
            {
                // 计算总记录数
                result.Value = data.Count;
                result.ValidValueCount = data.Count;
            }
            else
            {
                // 计算指定字段的非空值数量
                var validValues = GetValidValues(data, config.FieldName, config);
                result.Value = validValues.Count;
                result.ValidValueCount = validValues.Count;
            }
        }

        /// <summary>
        /// 执行求和统计
        /// </summary>
        private void ExecuteSum(List<Dictionary<string, object?>> data, AggregationConfig config, AggregationResult result)
        {
            var validValues = GetValidNumericValues(data, config.FieldName!, config);
            result.ValidValueCount = validValues.Count;

            if (validValues.Count == 0)
            {
                result.Value = 0.0;
                return;
            }

            var sum = validValues.Sum();
            result.Value = sum;
        }

        /// <summary>
        /// 执行平均值统计
        /// </summary>
        private void ExecuteAverage(List<Dictionary<string, object?>> data, AggregationConfig config, AggregationResult result)
        {
            var validValues = GetValidNumericValues(data, config.FieldName!, config);
            result.ValidValueCount = validValues.Count;

            if (validValues.Count == 0)
            {
                result.Value = 0.0;
                return;
            }

            var average = validValues.Average();
            result.Value = Math.Round(average, config.Precision);
        }

        /// <summary>
        /// 执行最小值统计
        /// </summary>
        private void ExecuteMin(List<Dictionary<string, object?>> data, AggregationConfig config, AggregationResult result)
        {
            var validValues = GetValidComparableValues(data, config.FieldName!, config);
            result.ValidValueCount = validValues.Count;

            if (validValues.Count == 0)
            {
                result.Value = null;
                return;
            }

            result.Value = validValues.Min();
        }

        /// <summary>
        /// 执行最大值统计
        /// </summary>
        private void ExecuteMax(List<Dictionary<string, object?>> data, AggregationConfig config, AggregationResult result)
        {
            var validValues = GetValidComparableValues(data, config.FieldName!, config);
            result.ValidValueCount = validValues.Count;

            if (validValues.Count == 0)
            {
                result.Value = null;
                return;
            }

            result.Value = validValues.Max();
        }

        /// <summary>
        /// 获取有效的数值列表
        /// </summary>
        private List<double> GetValidNumericValues(List<Dictionary<string, object?>> data, string fieldName, AggregationConfig config)
        {
            var validValues = new List<double>();

            foreach (var row in data)
            {
                if (!row.TryGetValue(fieldName, out var value))
                    continue;

                if (config.IgnoreNullValues && value == null)
                    continue;

                if (config.IgnoreEmptyStrings && value is string str && string.IsNullOrEmpty(str))
                    continue;

                if (TryConvertToDouble(value, out var numericValue))
                {
                    validValues.Add(numericValue);
                }
            }

            return validValues;
        }

        /// <summary>
        /// 获取有效的可比较值列表
        /// </summary>
        private List<IComparable> GetValidComparableValues(List<Dictionary<string, object?>> data, string fieldName, AggregationConfig config)
        {
            var validValues = new List<IComparable>();

            foreach (var row in data)
            {
                if (!row.TryGetValue(fieldName, out var value))
                    continue;

                if (config.IgnoreNullValues && value == null)
                    continue;

                if (config.IgnoreEmptyStrings && value is string str && string.IsNullOrEmpty(str))
                    continue;

                if (value is IComparable comparable)
                {
                    validValues.Add(comparable);
                }
                else if (TryConvertToDouble(value, out var numericValue))
                {
                    validValues.Add(numericValue);
                }
            }

            return validValues;
        }

        /// <summary>
        /// 获取有效值列表（用于计数）
        /// </summary>
        private List<object> GetValidValues(List<Dictionary<string, object?>> data, string fieldName, AggregationConfig config)
        {
            var validValues = new List<object>();

            foreach (var row in data)
            {
                if (!row.TryGetValue(fieldName, out var value))
                    continue;

                if (config.IgnoreNullValues && value == null)
                    continue;

                if (config.IgnoreEmptyStrings && value is string str && string.IsNullOrEmpty(str))
                    continue;

                validValues.Add(value!);
            }

            return validValues;
        }

        /// <summary>
        /// 尝试转换为双精度数值
        /// </summary>
        private static bool TryConvertToDouble(object? value, out double result)
        {
            result = 0.0;

            if (value == null)
                return false;

            if (value is double d)
            {
                result = d;
                return true;
            }

            if (value is int i)
            {
                result = i;
                return true;
            }

            if (value is float f)
            {
                result = f;
                return true;
            }

            if (value is decimal dec)
            {
                result = (double)dec;
                return true;
            }

            return double.TryParse(value.ToString(), out result);
        }

        /// <summary>
        /// 获取统计函数的默认值
        /// </summary>
        private static object GetDefaultValue(AggregationFunction function)
        {
            return function switch
            {
                AggregationFunction.Count => 0,
                AggregationFunction.Sum => 0.0,
                AggregationFunction.Average => 0.0,
                AggregationFunction.Min => null!,
                AggregationFunction.Max => null!,
                _ => null!
            };
        }

        #endregion

        /// <summary>
        /// 创建统计配置的便捷方法
        /// </summary>
        public static AggregationConfig CreateConfig(AggregationFunction function, string? fieldName = null, int precision = 2)
        {
            return new AggregationConfig
            {
                Function = function,
                FieldName = fieldName,
                Precision = precision,
                IgnoreNullValues = true,
                IgnoreEmptyStrings = true
            };
        }

        /// <summary>
        /// 创建常用统计配置集合
        /// </summary>
        public static List<AggregationConfig> CreateCommonConfigs(string fieldName)
        {
            return new List<AggregationConfig>
            {
                CreateConfig(AggregationFunction.Count),
                CreateConfig(AggregationFunction.Sum, fieldName),
                CreateConfig(AggregationFunction.Average, fieldName),
                CreateConfig(AggregationFunction.Min, fieldName),
                CreateConfig(AggregationFunction.Max, fieldName)
            };
        }
    }
}
