using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 表达式属性编辑器控件
    /// 支持语法高亮、验证和错误提示
    /// </summary>
    public class ExpressionPropertyWidget : IPropertyWidget
    {
        private readonly RichTextBox _richTextBox;
        private readonly TextBlock _errorMessage;
        private readonly StackPanel _container;
        private readonly Button _helpButton;
        private readonly TextBlock _helpText;

        private PropertyDefinition _propertyDefinition = new();
        private object? _value;
        private bool _isUpdatingText = false;

        // 语法高亮颜色定义
        private static readonly SolidColorBrush KeywordBrush = new(Colors.Blue);
        private static readonly SolidColorBrush OperatorBrush = new(Colors.DarkRed);
        private static readonly SolidColorBrush StringBrush = new(Colors.Green);
        private static readonly SolidColorBrush NumberBrush = new(Colors.Purple);
        private static readonly SolidColorBrush FieldBrush = new(Colors.DarkBlue);
        private static readonly SolidColorBrush ErrorBrush = new(Colors.Red);

        // 关键字和运算符定义
        private static readonly HashSet<string> Keywords = new() { "AND", "OR", "NOT", "and", "or", "not", "&&", "||", "!" };
        private static readonly HashSet<string> Operators = new() { "==", "!=", ">=", "<=", ">", "<", "=", "+" };

        public ExpressionPropertyWidget()
        {
            // 创建主容器
            _container = new StackPanel
            {
                Orientation = Orientation.Vertical,
                Margin = new Thickness(0, 8, 0, 16)
            };

            // 创建工具栏
            var toolbar = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 0, 0, 4)
            };

            _helpButton = new Button
            {
                Content = "语法帮助",
                Padding = new Thickness(8, 2, 8, 2),
                Margin = new Thickness(0, 0, 8, 0),
                FontSize = 11,
                Background = Brushes.LightGray,
                BorderThickness = new Thickness(1),
                BorderBrush = Brushes.Gray
            };
            _helpButton.Click += OnHelpButtonClick;

            toolbar.Children.Add(_helpButton);

            // 创建富文本编辑器
            _richTextBox = new RichTextBox
            {
                Height = 80,
                FontFamily = new FontFamily("Consolas, Courier New"),
                FontSize = 12,
                BorderThickness = new Thickness(1),
                BorderBrush = Brushes.LightGray,
                Padding = new Thickness(4),
                AcceptsReturn = true,
                AcceptsTab = true,
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto
            };

            _richTextBox.TextChanged += OnTextChanged;
            _richTextBox.LostFocus += OnLostFocus;

            // 创建错误消息显示
            _errorMessage = new TextBlock
            {
                Foreground = ErrorBrush,
                FontSize = 11,
                Margin = new Thickness(0, 4, 0, 0),
                TextWrapping = TextWrapping.Wrap,
                Visibility = Visibility.Collapsed
            };

            // 创建帮助文本
            _helpText = new TextBlock
            {
                FontSize = 11,
                Foreground = Brushes.Gray,
                Margin = new Thickness(0, 4, 0, 0),
                TextWrapping = TextWrapping.Wrap,
                Visibility = Visibility.Collapsed,
                Text = GetHelpText()
            };

            // 组装控件
            _container.Children.Add(toolbar);
            _container.Children.Add(_richTextBox);
            _container.Children.Add(_errorMessage);
            _container.Children.Add(_helpText);
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    UpdateTextContent();
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _richTextBox.IsEnabled;
            set
            {
                _richTextBox.IsEnabled = value;
                _helpButton.IsEnabled = value;
            }
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Models.ValidationResult Validate()
        {
            var result = new Models.ValidationResult();
            var text = GetPlainText();

            // 必填验证
            if (PropertyDefinition.Required && string.IsNullOrWhiteSpace(text))
            {
                result.AddError($"{PropertyDefinition.Title} 是必填项");
                return result;
            }

            // 表达式语法验证
            if (!string.IsNullOrWhiteSpace(text))
            {
                var syntaxResult = ValidateExpressionSyntax(text);
                if (!syntaxResult.IsValid)
                {
                    result.AddError($"表达式语法错误: {syntaxResult.ErrorMessage}");
                }
            }

            return result;
        }

        public FrameworkElement GetElement()
        {
            return _container;
        }

        private void UpdateUI()
        {
            // 设置帮助文本的可见性
            if (PropertyDefinition.Name == "syntaxHelp")
            {
                var showHelp = PropertyDefinition.DefaultValue as bool? ?? true;
                _helpText.Visibility = showHelp ? Visibility.Visible : Visibility.Collapsed;
            }

            UpdateTextContent();
        }

        private void UpdateTextContent()
        {
            if (_isUpdatingText) return;

            _isUpdatingText = true;
            try
            {
                var text = _value?.ToString() ?? PropertyDefinition.DefaultValue?.ToString() ?? string.Empty;
                SetRichText(text);
            }
            finally
            {
                _isUpdatingText = false;
            }
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingText) return;

            var plainText = GetPlainText();
            _value = plainText;

            // 应用语法高亮
            ApplySyntaxHighlighting();

            // 实时验证
            var validation = Validate();
            ShowValidationResult(validation);

            // 触发值变化事件
            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, null, plainText));
        }

        private void OnLostFocus(object sender, RoutedEventArgs e)
        {
            // 失去焦点时进行完整验证
            var validation = Validate();
            ShowValidationResult(validation);
        }

        private void OnHelpButtonClick(object sender, RoutedEventArgs e)
        {
            _helpText.Visibility = _helpText.Visibility == Visibility.Visible
                ? Visibility.Collapsed
                : Visibility.Visible;
        }

        private string GetPlainText()
        {
            var textRange = new TextRange(_richTextBox.Document.ContentStart, _richTextBox.Document.ContentEnd);
            return textRange.Text.Trim();
        }

        private void SetRichText(string text)
        {
            _richTextBox.Document.Blocks.Clear();
            if (!string.IsNullOrEmpty(text))
            {
                var paragraph = new Paragraph(new Run(text));
                _richTextBox.Document.Blocks.Add(paragraph);
                ApplySyntaxHighlighting();
            }
        }

        private void ApplySyntaxHighlighting()
        {
            // 简化的语法高亮实现
            // 在实际项目中可以使用更复杂的语法分析器
            try
            {
                var document = _richTextBox.Document;
                var textRange = new TextRange(document.ContentStart, document.ContentEnd);

                // 重置所有格式
                textRange.ClearAllProperties();

                var text = textRange.Text;
                if (string.IsNullOrWhiteSpace(text)) return;

                // 应用基础字体
                textRange.ApplyPropertyValue(TextElement.FontFamilyProperty, new FontFamily("Consolas, Courier New"));
                textRange.ApplyPropertyValue(TextElement.FontSizeProperty, 12.0);

                // 这里可以添加更复杂的语法高亮逻辑
                // 由于 RichTextBox 的复杂性，暂时保持简单实现
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"语法高亮应用失败: {ex.Message}");
            }
        }

        private ExpressionValidationResult ValidateExpressionSyntax(string expression)
        {
            try
            {
                // 基础语法验证
                if (string.IsNullOrWhiteSpace(expression))
                    return new ExpressionValidationResult { IsValid = true };

                // 检查括号匹配
                var openCount = expression.Count(c => c == '(');
                var closeCount = expression.Count(c => c == ')');
                if (openCount != closeCount)
                {
                    return new ExpressionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = "括号不匹配"
                    };
                }

                // 检查基本运算符
                var invalidChars = expression.Where(c => !IsValidExpressionChar(c)).ToList();
                if (invalidChars.Any())
                {
                    return new ExpressionValidationResult
                    {
                        IsValid = false,
                        ErrorMessage = $"包含无效字符: {string.Join(", ", invalidChars.Distinct())}"
                    };
                }

                return new ExpressionValidationResult { IsValid = true };
            }
            catch (Exception ex)
            {
                return new ExpressionValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"验证失败: {ex.Message}"
                };
            }
        }

        private static bool IsValidExpressionChar(char c)
        {
            return char.IsLetterOrDigit(c) ||
                   char.IsWhiteSpace(c) ||
                   "()[]{}\"'.,;:!@#$%^&*-+=<>?/\\|_".Contains(c);
        }

        private void ShowValidationResult(Models.ValidationResult result)
        {
            if (result.IsValid)
            {
                _errorMessage.Visibility = Visibility.Collapsed;
                _richTextBox.BorderBrush = Brushes.LightGray;
            }
            else
            {
                _errorMessage.Text = string.Join("; ", result.Errors);
                _errorMessage.Visibility = Visibility.Visible;
                _richTextBox.BorderBrush = ErrorBrush;
            }
        }

        private static string GetHelpText()
        {
            return @"表达式语法帮助：
• 比较运算符：== != > < >= <=
• 逻辑运算符：&& || !
• 括号分组：(condition1) && (condition2)
• 字符串：使用双引号 ""文本""
• 数字：直接输入数值
• 字段名：直接使用字段名称
示例：score > 90 && (class == ""A"" || status == ""VIP"")";
        }
    }

    /// <summary>
    /// 表达式验证结果
    /// </summary>
    public class ExpressionValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
