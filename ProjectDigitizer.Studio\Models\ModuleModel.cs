using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 节点类型分类（基于Kettle ETL设计模式）
    /// </summary>
    public enum NodeType
    {
        /// <summary>
        /// 输入节点 - 数据源节点
        /// </summary>
        Input,

        /// <summary>
        /// 转换节点 - 数据处理节点
        /// </summary>
        Transform,

        /// <summary>
        /// 输出节点 - 数据目标节点
        /// </summary>
        Output,

        /// <summary>
        /// 控制节点 - 流程控制节点
        /// </summary>
        Control
    }

    /// <summary>
    /// 节点类型元数据
    /// </summary>
    public class NodeTypeMetadata
    {
        public NodeType NodeType { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string IconGlyph { get; set; } = "\uE8F4"; // 默认Segoe MDL2 Assets图标
        public string ColorTheme { get; set; } = "#2196F3"; // 默认蓝色主题
        public List<string> SupportedDataTypes { get; set; } = new();
        public bool AllowMultipleInputs { get; set; } = true;
        public bool AllowMultipleOutputs { get; set; } = true;
    }

    /// <summary>
    /// 子模板类型
    /// </summary>
    public enum ModuleType
    {
        // 输入类 - 数据源
        FileInput,          // 文件输入
        DatabaseInput,      // 数据库输入
        APIInput,           // API输入
        CADInput,           // CAD输入
        ExcelInput,         // Excel输入
        CSVInput,           // CSV输入
        XMLInput,           // XML输入
        JSONInput,          // JSON输入
        ManualDataInput,    // 手动输入数据节点

        // 常规数据类
        PipeLine,           // 平面管线类
        RiserPipe,          // 立管类
        PressureBox,        // 调压箱调压柜类
        Excavation,         // 开挖回填
        Demolition,         // 破除恢复
        AntiCorrosion,      // 防腐
        LightningProtection,// 防雷防静电

        // 数据衍生关联类
        WarningBand,        // 警示带示踪线
        WeldInspection,     // 焊口探伤
        InstallationTeam,   // 安装台班
        Measures,           // 措施

        // 触发器类
        ClickTrigger,       // 点击触发器
        AssociationTrigger, // 关联触发器
        TimedTrigger,       // 定时触发器
        FileChangeTrigger,  // 文件变化触发器
        EnvironmentTrigger, // 环境触发器

        // 处理类
        DataFilter,         // 数据过滤
        TagSearch,          // 标签搜索
        DataCalculation,    // 数据计算
        DataValidation,     // 数据验证
        DataTransform,      // 数据转换
        DataCondition,      // 数据条件
        DataMerge,          // 数据合并
        DataSort,           // 数据排序
        DataGroup,          // 数据分组
        ArrayExpansion,     // 数组展开节点
        Other,              // 其他

        // 整理类
        TableManager,       // 表格管理
        GraphicsAPI,        // 图形API
        ExcelCSV,           // Excel/CSV
        WordProcessor,      // Word处理

        // 输出类
        FileGeneration,     // 文件生成
        ManualLocation,     // 手动定位
        SpecifiedPath,      // 指定路径
        ThirdPartyAPI,      // 第三方API
        CADExport,          // CAD导出
        ExcelExport,        // Excel导出
        CSVExport,          // CSV导出
        WordExport,         // Word导出
        PPTExport,          // PPT导出
        ImageExport,        // 图片导出
        PublishRelease,     // 发布释放
        NotificationAlert,  // 通知警报
        DatabaseOutput,     // 数据库输出
        ReportGeneration,   // 报表生成
        EmailSender,        // 邮件发送
        PrintOutput,        // 打印输出
        WebServiceOutput,   // Web服务输出
        FTPUpload,          // FTP上传
        CloudStorage,       // 云存储
        DialogChat,         // 对话聊天
        OtherOutput,        // 其他输出

        // 控制类
        ConditionalBranch,  // 条件分支
        LoopProcessor,      // 循环处理
        ErrorHandler,       // 错误处理
        FlowControl,        // 流程控制
        ScriptExecutor,     // 脚本执行
        VariableManager,    // 变量管理
        StateManager,       // 状态管理
        AIAgent             // 智能体节点
    }

    /// <summary>
    /// 子模板的基本模型
    /// </summary>
    public class ModuleModel : INotifyPropertyChanged
    {
        private string _id;
        private string _name;
        private string _description;
        private ModuleType _type;
        private NodeType _nodeType;
        private bool _isEnabled;
        private bool _isVisible;
        private List<string> _conditions;
        private Dictionary<string, object> _parameters;
        private string _codeTemplate;

        /// <summary>
        /// 模块唯一标识
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 模块名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 模块描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// 模块类型
        /// </summary>
        public ModuleType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 节点类型分类
        /// </summary>
        public NodeType NodeType
        {
            get => _nodeType;
            set => SetProperty(ref _nodeType, value);
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 是否可见
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            set => SetProperty(ref _isVisible, value);
        }

        /// <summary>
        /// 条件列表
        /// </summary>
        public List<string> Conditions
        {
            get => _conditions;
            set => SetProperty(ref _conditions, value);
        }

        /// <summary>
        /// 参数字典
        /// </summary>
        public Dictionary<string, object> Parameters
        {
            get => _parameters;
            set => SetProperty(ref _parameters, value);
        }

        /// <summary>
        /// 代码模板（用于代码生成）
        /// </summary>
        public string CodeTemplate
        {
            get => _codeTemplate;
            set => SetProperty(ref _codeTemplate, value);
        }

        public ModuleModel()
        {
            _id = Guid.NewGuid().ToString();
            _name = "未命名模块";
            _description = "模块描述";
            _type = ModuleType.PipeLine;
            _nodeType = NodeType.Input; // 默认为输入节点
            _isEnabled = true;  // 默认启用
            _isVisible = true;
            _conditions = new List<string>();
            _parameters = new Dictionary<string, object>();
            _codeTemplate = string.Empty;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}