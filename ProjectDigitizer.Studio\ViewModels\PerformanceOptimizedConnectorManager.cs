using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Threading;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 性能优化的连接器管理器
    /// </summary>
    public class PerformanceOptimizedConnectorManager
    {
        private readonly DispatcherTimer _updateTimer;
        private readonly HashSet<ConnectorViewModel> _pendingUpdates = new();
        private readonly object _lockObject = new();
        private const int UPDATE_INTERVAL_MS = 16; // 约60FPS

        public PerformanceOptimizedConnectorManager()
        {
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(UPDATE_INTERVAL_MS)
            };
            _updateTimer.Tick += OnUpdateTimerTick;
        }

        /// <summary>
        /// 请求更新连接器状态（节流处理）
        /// </summary>
        public void RequestConnectorUpdate(ConnectorViewModel connector)
        {
            if (connector == null) return;

            lock (_lockObject)
            {
                _pendingUpdates.Add(connector);

                if (!_updateTimer.IsEnabled)
                {
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 批量更新连接器状态
        /// </summary>
        public void BatchUpdateConnectors(IEnumerable<ConnectorViewModel> connectors)
        {
            if (connectors == null) return;

            lock (_lockObject)
            {
                foreach (var connector in connectors)
                {
                    _pendingUpdates.Add(connector);
                }

                if (!_updateTimer.IsEnabled && _pendingUpdates.Count > 0)
                {
                    _updateTimer.Start();
                }
            }
        }

        /// <summary>
        /// 立即处理所有待定更新
        /// </summary>
        public void FlushPendingUpdates()
        {
            lock (_lockObject)
            {
                if (_pendingUpdates.Count > 0)
                {
                    ProcessPendingUpdates();
                }
                _updateTimer.Stop();
            }
        }

        private void OnUpdateTimerTick(object? sender, EventArgs e)
        {
            lock (_lockObject)
            {
                if (_pendingUpdates.Count > 0)
                {
                    ProcessPendingUpdates();
                }
                else
                {
                    _updateTimer.Stop();
                }
            }
        }

        private void ProcessPendingUpdates()
        {
            var connectorsToUpdate = _pendingUpdates.ToList();
            _pendingUpdates.Clear();

            // 在UI线程上批量处理更新
            foreach (var connector in connectorsToUpdate)
            {
                try
                {
                    // 通过重新设置属性值来触发UI更新
                    var currentValue = connector.IsConnected;
                    connector.IsConnected = currentValue;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"连接器更新错误: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _updateTimer?.Stop();
            lock (_lockObject)
            {
                _pendingUpdates.Clear();
            }
        }
    }

    /// <summary>
    /// 连接器性能优化静态管理器
    /// </summary>
    public static class ConnectorPerformanceManager
    {
        private static readonly PerformanceOptimizedConnectorManager _manager = new();

        /// <summary>
        /// 获取全局连接器管理器
        /// </summary>
        public static PerformanceOptimizedConnectorManager Instance => _manager;

        /// <summary>
        /// 请求优化的连接器更新
        /// </summary>
        public static void RequestUpdate(ConnectorViewModel connector)
        {
            _manager.RequestConnectorUpdate(connector);
        }

        /// <summary>
        /// 批量请求连接器更新
        /// </summary>
        public static void RequestBatchUpdate(IEnumerable<ConnectorViewModel> connectors)
        {
            _manager.BatchUpdateConnectors(connectors);
        }

        /// <summary>
        /// 立即刷新所有待定更新
        /// </summary>
        public static void FlushAllUpdates()
        {
            _manager.FlushPendingUpdates();
        }
    }
}
