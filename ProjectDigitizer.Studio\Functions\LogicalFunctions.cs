using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Functions
{
    /// <summary>
    /// IF条件函数
    /// </summary>
    public class IfFunction : FunctionProviderBase
    {
        public override string Name => "IF";
        public override string DisplayName => "条件判断";
        public override string Category => "逻辑";
        public override string Description => "根据条件返回不同的值";
        public override FunctionType Type => FunctionType.Logical;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "condition",
                    DisplayName = "条件",
                    DataType = FieldDataType.Boolean,
                    IsRequired = true,
                    Description = "判断条件"
                },
                new ParameterDefinition
                {
                    Name = "trueValue",
                    DisplayName = "真值",
                    DataType = FieldDataType.Any,
                    IsRequired = true,
                    Description = "条件为真时返回的值"
                },
                new ParameterDefinition
                {
                    Name = "falseValue",
                    DisplayName = "假值",
                    DataType = FieldDataType.Any,
                    IsRequired = true,
                    Description = "条件为假时返回的值"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var condition = GetParameterValue<bool>(parameters, "condition", false);
                var trueValue = parameters.GetValueOrDefault("trueValue");
                var falseValue = parameters.GetValueOrDefault("falseValue");
                
                var result = condition ? trueValue : falseValue;
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Any,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"条件判断失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "IF(true, \"是\", \"否\") = \"是\"",
                "IF({score} > 60, \"及格\", \"不及格\")",
                "IF({age} >= 18, \"成年\", \"未成年\")"
            };
        }
    }

    /// <summary>
    /// AND逻辑与函数
    /// </summary>
    public class AndFunction : FunctionProviderBase
    {
        public override string Name => "AND";
        public override string DisplayName => "逻辑与";
        public override string Category => "逻辑";
        public override string Description => "所有条件都为真时返回真";
        public override FunctionType Type => FunctionType.Logical;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "conditions",
                    DisplayName = "条件列表",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要进行逻辑与运算的条件列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var conditions = GetArrayParameter<bool>(parameters, "conditions");
                if (!conditions.Any())
                {
                    return FunctionResult.Success(true, FieldDataType.Boolean);
                }

                var result = conditions.All(c => c);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Boolean,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"逻辑与运算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "AND(true, true) = true",
                "AND(true, false) = false",
                "AND({condition1}, {condition2}, {condition3})"
            };
        }
    }

    /// <summary>
    /// OR逻辑或函数
    /// </summary>
    public class OrFunction : FunctionProviderBase
    {
        public override string Name => "OR";
        public override string DisplayName => "逻辑或";
        public override string Category => "逻辑";
        public override string Description => "任一条件为真时返回真";
        public override FunctionType Type => FunctionType.Logical;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "conditions",
                    DisplayName = "条件列表",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要进行逻辑或运算的条件列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var conditions = GetArrayParameter<bool>(parameters, "conditions");
                if (!conditions.Any())
                {
                    return FunctionResult.Success(false, FieldDataType.Boolean);
                }

                var result = conditions.Any(c => c);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Boolean,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"逻辑或运算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "OR(true, false) = true",
                "OR(false, false) = false",
                "OR({condition1}, {condition2}, {condition3})"
            };
        }
    }

    /// <summary>
    /// NOT逻辑非函数
    /// </summary>
    public class NotFunction : FunctionProviderBase
    {
        public override string Name => "NOT";
        public override string DisplayName => "逻辑非";
        public override string Category => "逻辑";
        public override string Description => "对条件取反";
        public override FunctionType Type => FunctionType.Logical;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "condition",
                    DisplayName = "条件",
                    DataType = FieldDataType.Boolean,
                    IsRequired = true,
                    Description = "要取反的条件"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var condition = GetParameterValue<bool>(parameters, "condition", false);
                var result = !condition;
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Boolean,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"逻辑非运算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "NOT(true) = false",
                "NOT(false) = true",
                "NOT({condition})"
            };
        }
    }
}
