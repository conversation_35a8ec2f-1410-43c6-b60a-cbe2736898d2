<UserControl x:Class="ProjectDigitizer.Studio.Controls.NodeEditorToolbar"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Height="50">
    
    <UserControl.Resources>
        <!-- 工具栏按钮样式 -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Width" Value="36"/>
            <Setter Property="Height" Value="36"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="Border"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#E3F2FD"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="Border" Property="Background" Value="#BBDEFB"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 分隔符样式 -->
        <Style x:Key="ToolbarSeparatorStyle" TargetType="Rectangle">
            <Setter Property="Width" Value="1"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="Fill" Value="#E0E0E0"/>
            <Setter Property="Margin" Value="4,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Border Background="White"
            BorderBrush="#E0E0E0"
            BorderThickness="0,0,0,1">
        <Border.Effect>
            <DropShadowEffect Color="Black"
                              Opacity="0.1"
                              ShadowDepth="1"
                              BlurRadius="3"/>
        </Border.Effect>

        <StackPanel Orientation="Horizontal"
                    Margin="12,7"
                    VerticalAlignment="Center">

            <!-- 选择工具 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="选择工具 (V)"
                    Command="{Binding SelectToolCommand}">
                <materialDesign:PackIcon Kind="CursorDefault"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <!-- 手动输入节点 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="手动输入数据节点"
                    Command="{Binding AddManualInputNodeCommand}">
                <materialDesign:PackIcon Kind="Keyboard"
                                         Width="18"
                                         Height="18"
                                         Foreground="#00BCD4"/>
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}"/>

            <!-- 连接工具 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="连接工具"
                    Command="{Binding ConnectToolCommand}">
                <materialDesign:PackIcon Kind="VectorLine"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <!-- 数组展开节点 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="数组展开节点"
                    Command="{Binding AddArrayExpansionNodeCommand}">
                <materialDesign:PackIcon Kind="UnfoldMoreHorizontal"
                                         Width="18"
                                         Height="18"
                                         Foreground="#3F51B5"/>
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}"/>

            <!-- 智能体节点 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="智能体节点"
                    Command="{Binding AddAIAgentNodeCommand}">
                <materialDesign:PackIcon Kind="Robot"
                                         Width="18"
                                         Height="18"
                                         Foreground="#E91E63"/>
            </Button>

            <!-- 属性面板 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="属性面板"
                    Command="{Binding TogglePropertiesCommand}">
                <materialDesign:PackIcon Kind="Cog"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}"/>

            <!-- 自动布局 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="自动布局"
                    Command="{Binding AutoLayoutCommand}">
                <materialDesign:PackIcon Kind="AutoFix"
                                         Width="18"
                                         Height="18"
                                         Foreground="#FF9800"/>
            </Button>

            <!-- 锁定/解锁 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="锁定选中节点"
                    Command="{Binding ToggleLockCommand}">
                <materialDesign:PackIcon Kind="Lock"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}"/>

            <!-- 撤销 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="撤销 (Ctrl+Z)"
                    Command="{Binding UndoCommand}">
                <materialDesign:PackIcon Kind="Undo"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <!-- 重做 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重做 (Ctrl+Y)"
                    Command="{Binding RedoCommand}">
                <materialDesign:PackIcon Kind="Redo"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <Rectangle Style="{StaticResource ToolbarSeparatorStyle}"/>

            <!-- 缩放适应 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="缩放适应"
                    Command="{Binding FitToScreenCommand}">
                <materialDesign:PackIcon Kind="FitToPage"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

            <!-- 重置视图 -->
            <Button Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重置视图"
                    Command="{Binding ResetViewCommand}">
                <materialDesign:PackIcon Kind="Refresh"
                                         Width="18"
                                         Height="18"
                                         Foreground="#424242"/>
            </Button>

        </StackPanel>
    </Border>
</UserControl>
