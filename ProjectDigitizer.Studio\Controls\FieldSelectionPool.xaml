<UserControl x:Class="ProjectDigitizer.Studio.Controls.FieldSelectionPool"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="150" d:DesignWidth="400"
             AllowDrop="True">

    <UserControl.Resources>
        <!-- 字段项样式 -->
        <Style x:Key="FieldChipStyle" TargetType="Border">
            <Setter Property="Background" Value="{DynamicResource MaterialDesignChipBackground}"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <!-- 可用状态 -->
                <DataTrigger Binding="{Binding State}" Value="Available">
                    <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
                    <Setter Property="BorderThickness" Value="1"/>
                </DataTrigger>
                <!-- 已连接状态 -->
                <DataTrigger Binding="{Binding State}" Value="Connected">
                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                </DataTrigger>
                <!-- 已引用状态 -->
                <DataTrigger Binding="{Binding State}" Value="Referenced">
                    <Setter Property="BorderBrush" Value="{DynamicResource SecondaryHueMidBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Setter Property="Background" Value="{DynamicResource SecondaryHueLightBrush}"/>
                </DataTrigger>
                <!-- 激活状态 -->
                <DataTrigger Binding="{Binding State}" Value="Active">
                    <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    <Setter Property="BorderThickness" Value="2"/>
                    <Setter Property="Background" Value="{DynamicResource PrimaryHueLightBrush}"/>
                </DataTrigger>
                <!-- 鼠标悬停效果 -->
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect ShadowDepth="2" Direction="270" Color="Black" Opacity="0.3" BlurRadius="4"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
                <!-- 拖拽时的效果 -->
                <DataTrigger Binding="{Binding IsDragging}" Value="True">
                    <Setter Property="Opacity" Value="0.7"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect ShadowDepth="4" Direction="270" Color="Black" Opacity="0.5" BlurRadius="8"/>
                        </Setter.Value>
                    </Setter>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 数据类型图标样式 -->
        <Style x:Key="FieldTypeIconStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Width" Value="14"/>
            <Setter Property="Height" Value="14"/>
            <Setter Property="Margin" Value="0,0,4,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding DataType}" Value="Number">
                    <Setter Property="Kind" Value="Numeric"/>
                    <Setter Property="Foreground" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Text">
                    <Setter Property="Kind" Value="FormatText"/>
                    <Setter Property="Foreground" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Boolean">
                    <Setter Property="Kind" Value="CheckboxMarked"/>
                    <Setter Property="Foreground" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="DateTime">
                    <Setter Property="Kind" Value="Calendar"/>
                    <Setter Property="Foreground" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="File">
                    <Setter Property="Kind" Value="File"/>
                    <Setter Property="Foreground" Value="#607D8B"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Geometry">
                    <Setter Property="Kind" Value="VectorTriangle"/>
                    <Setter Property="Foreground" Value="#E91E63"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Array">
                    <Setter Property="Kind" Value="FormatListBulleted"/>
                    <Setter Property="Foreground" Value="#795548"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Object">
                    <Setter Property="Kind" Value="CodeBraces"/>
                    <Setter Property="Foreground" Value="#3F51B5"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Any">
                    <Setter Property="Kind" Value="Database"/>
                    <Setter Property="Foreground" Value="#757575"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 移除按钮样式 -->
        <Style x:Key="RemoveButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignToolButton}">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <DockPanel>
        <!-- 顶部工具栏 -->
        <StackPanel DockPanel.Dock="Top" Orientation="Horizontal" Margin="0,0,0,8">
            <TextBlock Text="已选字段" 
                       FontSize="14" 
                       FontWeight="Medium" 
                       Foreground="{DynamicResource MaterialDesignBody}"
                       VerticalAlignment="Center"/>
            
            <TextBlock Text="{Binding SelectedFieldCount, StringFormat=({0})}" 
                       FontSize="12" 
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       Margin="4,0,0,0"
                       VerticalAlignment="Center"/>
            
            <Button x:Name="ClearAllButton" 
                    Style="{StaticResource MaterialDesignIconButton}"
                    Width="24" Height="24"
                    Margin="8,0,0,0"
                    Content="{materialDesign:PackIcon Kind=Clear, Size=14}"
                    ToolTip="清空所有字段"
                    Visibility="{Binding HasSelectedFields, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            
            <Button x:Name="SortButton" 
                    Style="{StaticResource MaterialDesignIconButton}"
                    Width="24" Height="24"
                    Margin="4,0,0,0"
                    Content="{materialDesign:PackIcon Kind=Sort, Size=14}"
                    ToolTip="排序字段"
                    Visibility="{Binding HasSelectedFields, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            
            <Button x:Name="ExportButton" 
                    Style="{StaticResource MaterialDesignIconButton}"
                    Width="24" Height="24"
                    Margin="4,0,0,0"
                    Content="{materialDesign:PackIcon Kind=Export, Size=14}"
                    ToolTip="导出字段列表"
                    Visibility="{Binding HasSelectedFields, Converter={StaticResource BooleanToVisibilityConverter}}"/>
        </StackPanel>

        <!-- 字段统计信息 -->
        <Border DockPanel.Dock="Top" 
                Background="{DynamicResource MaterialDesignChipBackground}"
                CornerRadius="4"
                Padding="8,4"
                Margin="0,0,0,8"
                Visibility="{Binding HasSelectedFields, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="已连接:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="{Binding ConnectedCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="已引用:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="4,0,0,0"/>
                <TextBlock Text="{Binding ReferencedCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="激活:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="4,0,0,0"/>
                <TextBlock Text="{Binding ActiveCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
            </StackPanel>
        </Border>

        <!-- 字段列表 -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled">
            <ItemsControl x:Name="FieldItemsControl" 
                          ItemsSource="{Binding SelectedFields}"
                          Background="Transparent">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Orientation="Horizontal" 
                                   AllowDrop="True"/>
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{StaticResource FieldChipStyle}"
                                MouseLeftButtonDown="OnFieldMouseDown"
                                MouseMove="OnFieldMouseMove"
                                MouseLeftButtonUp="OnFieldMouseUp"
                                Drop="OnFieldDrop"
                                DragOver="OnFieldDragOver"
                                AllowDrop="True">
                            <StackPanel Orientation="Horizontal">
                                <!-- 拖拽手柄 -->
                                <materialDesign:PackIcon Kind="DragHorizontal" 
                                                         Width="12" Height="12"
                                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                         VerticalAlignment="Center"
                                                         Margin="0,0,4,0"
                                                         Cursor="SizeAll"/>
                                
                                <!-- 字段类型图标 -->
                                <materialDesign:PackIcon Style="{StaticResource FieldTypeIconStyle}"/>
                                
                                <!-- 字段名称 -->
                                <TextBlock Text="{Binding DisplayName}" 
                                           FontSize="11" 
                                           VerticalAlignment="Center"
                                           ToolTip="{Binding Description}"/>
                                
                                <!-- 状态指示器 -->
                                <Ellipse Width="6" Height="6" 
                                         Margin="4,0"
                                         VerticalAlignment="Center">
                                    <Ellipse.Style>
                                        <Style TargetType="Ellipse">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding State}" Value="Available">
                                                    <Setter Property="Fill" Value="Transparent"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding State}" Value="Connected">
                                                    <Setter Property="Fill" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding State}" Value="Referenced">
                                                    <Setter Property="Fill" Value="{DynamicResource SecondaryHueMidBrush}"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding State}" Value="Active">
                                                    <Setter Property="Fill" Value="{DynamicResource PrimaryHueDarkBrush}"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Ellipse.Style>
                                </Ellipse>
                                
                                <!-- 移除按钮 -->
                                <Button Style="{StaticResource RemoveButtonStyle}"
                                        Content="{materialDesign:PackIcon Kind=Close, Size=10}"
                                        Click="OnRemoveFieldClick"
                                        ToolTip="移除字段"/>
                            </StackPanel>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>

        <!-- 空状态提示 -->
        <StackPanel x:Name="EmptyStatePanel"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Visibility="{Binding HasSelectedFields, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}">
            <materialDesign:PackIcon Kind="PlaylistPlus"
                                     Width="32"
                                     Height="32"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,8"/>
            <TextBlock Text="双击左侧字段添加到此处"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       FontSize="12"
                       TextAlignment="Center"
                       TextWrapping="Wrap"
                       MaxWidth="200"/>
            <TextBlock Text="或拖拽字段到此区域"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       FontSize="11"
                       TextAlignment="Center"
                       Margin="0,4,0,0"/>
        </StackPanel>

        <!-- 拖拽放置区域指示器 -->
        <Border x:Name="DropIndicator"
                Background="{DynamicResource PrimaryHueLightBrush}"
                BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                BorderThickness="2"
                CornerRadius="4"
                Opacity="0.7"
                Visibility="Collapsed">
            <TextBlock Text="释放以添加字段"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Foreground="{DynamicResource PrimaryHueDarkBrush}"
                       FontWeight="Medium"/>
        </Border>
    </DockPanel>
</UserControl>
