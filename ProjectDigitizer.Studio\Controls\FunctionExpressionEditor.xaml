<UserControl x:Class="ProjectDigitizer.Studio.Controls.FunctionExpressionEditor"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="300"
             d:DesignWidth="500">

        <UserControl.Resources>
                <!-- 函数标签页样式 -->
                <Style x:Key="FunctionTabStyle"
                       TargetType="TabItem"
                       BasedOn="{StaticResource MaterialDesignTabItem}">
                        <Setter Property="MinWidth"
                                Value="120"/>
                        <Setter Property="MaxWidth"
                                Value="200"/>
                </Style>

                <!-- 工具按钮样式 -->
                <Style x:Key="ToolButtonStyle"
                       TargetType="Button"
                       BasedOn="{StaticResource MaterialDesignIconButton}">
                        <Setter Property="Width"
                                Value="28"/>
                        <Setter Property="Height"
                                Value="28"/>
                        <Setter Property="Margin"
                                Value="2"/>
                </Style>

                <!-- 函数类型样式 -->
                <Style x:Key="FunctionTypeStyle"
                       TargetType="ComboBox"
                       BasedOn="{StaticResource MaterialDesignComboBox}">
                        <Setter Property="MinWidth"
                                Value="100"/>
                        <Setter Property="Margin"
                                Value="4,0"/>
                </Style>

                <!-- 表达式编辑器样式 -->
                <Style x:Key="ExpressionTextBoxStyle"
                       TargetType="TextBox"
                       BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="FontFamily"
                                Value="Consolas"/>
                        <Setter Property="FontSize"
                                Value="12"/>
                        <Setter Property="AcceptsReturn"
                                Value="True"/>
                        <Setter Property="TextWrapping"
                                Value="Wrap"/>
                        <Setter Property="VerticalScrollBarVisibility"
                                Value="Auto"/>
                        <Setter Property="HorizontalScrollBarVisibility"
                                Value="Auto"/>
                </Style>

                <!-- 验证错误样式 -->
                <Style x:Key="ValidationErrorStyle"
                       TargetType="TextBlock">
                        <Setter Property="Foreground"
                                Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                        <Setter Property="FontSize"
                                Value="11"/>
                        <Setter Property="Margin"
                                Value="0,4,0,0"/>
                        <Setter Property="TextWrapping"
                                Value="Wrap"/>
                </Style>

                <!-- 字段引用提示样式 -->
                <Style x:Key="FieldReferenceStyle"
                       TargetType="Border">
                        <Setter Property="Background"
                                Value="{DynamicResource PrimaryHueLightBrush}"/>
                        <Setter Property="BorderBrush"
                                Value="{DynamicResource PrimaryHueMidBrush}"/>
                        <Setter Property="BorderThickness"
                                Value="1"/>
                        <Setter Property="CornerRadius"
                                Value="3"/>
                        <Setter Property="Padding"
                                Value="4,2"/>
                        <Setter Property="Margin"
                                Value="2"/>
                </Style>
        </UserControl.Resources>

        <DockPanel>
                <!-- 顶部工具栏 -->
                <StackPanel DockPanel.Dock="Top"
                            Orientation="Horizontal"
                            Margin="0,0,0,8">
                        <TextBlock Text="函数表达式"
                                   FontSize="14"
                                   FontWeight="Medium"
                                   Foreground="{DynamicResource MaterialDesignBody}"
                                   VerticalAlignment="Center"/>

                        <Button x:Name="AddFunctionButton"
                                Style="{StaticResource ToolButtonStyle}"
                                Content="{materialDesign:PackIcon Kind=Plus}"
                                ToolTip="添加函数表达式"
                                Margin="8,0,0,0"/>

                        <Button x:Name="ImportTemplateButton"
                                Style="{StaticResource ToolButtonStyle}"
                                Content="{materialDesign:PackIcon Kind=Import}"
                                ToolTip="导入模板"/>

                        <Button x:Name="ValidateAllButton"
                                Style="{StaticResource ToolButtonStyle}"
                                Content="{materialDesign:PackIcon Kind=CheckCircle}"
                                ToolTip="验证所有表达式"/>

                        <Button x:Name="FormatButton"
                                Style="{StaticResource ToolButtonStyle}"
                                Content="{materialDesign:PackIcon Kind=FormatAlignLeft}"
                                ToolTip="格式化表达式"/>

                        <Button x:Name="HelpButton"
                                Style="{StaticResource ToolButtonStyle}"
                                Content="{materialDesign:PackIcon Kind=Help}"
                                ToolTip="函数帮助"/>
                </StackPanel>

                <!-- 函数标签页 -->
                <TabControl x:Name="FunctionTabControl"
                            ItemsSource="{Binding Functions}"
                            Style="{StaticResource MaterialDesignTabControl}">

                        <!-- 标签页头部模板 -->
                        <TabControl.ItemTemplate>
                                <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                                <!-- 函数类型图标 -->
                                                <materialDesign:PackIcon Width="14"
                                                                         Height="14"
                                                                         Margin="0,0,4,0"
                                                                         VerticalAlignment="Center">
                                                        <materialDesign:PackIcon.Style>
                                                                <Style TargetType="materialDesign:PackIcon">
                                                                        <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="Math">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Calculator"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#2196F3"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="Statistical">
                                                                                        <Setter Property="Kind"
                                                                                                Value="ChartLine"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#4CAF50"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="String">
                                                                                        <Setter Property="Kind"
                                                                                                Value="FormatText"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#FF9800"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="DateTime">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Calendar"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#9C27B0"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="Logical">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Logic"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#E91E63"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="Aggregation">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Sigma"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#795548"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding Type}"
                                                                                             Value="Custom">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Cog"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="#607D8B"/>
                                                                                </DataTrigger>
                                                                        </Style.Triggers>
                                                                </Style>
                                                        </materialDesign:PackIcon.Style>
                                                </materialDesign:PackIcon>

                                                <!-- 函数名称 -->
                                                <TextBlock Text="{Binding Name}"
                                                           FontSize="12"
                                                           VerticalAlignment="Center"/>

                                                <!-- 验证状态指示器 -->
                                                <materialDesign:PackIcon Width="12"
                                                                         Height="12"
                                                                         Margin="4,0,0,0"
                                                                         VerticalAlignment="Center">
                                                        <materialDesign:PackIcon.Style>
                                                                <Style TargetType="materialDesign:PackIcon">
                                                                        <Style.Triggers>
                                                                                <DataTrigger Binding="{Binding ValidationResult.IsValid}"
                                                                                             Value="True">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Check"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="{DynamicResource MaterialDesignValidationSuccessBrush}"/>
                                                                                </DataTrigger>
                                                                                <DataTrigger Binding="{Binding ValidationResult.IsValid}"
                                                                                             Value="False">
                                                                                        <Setter Property="Kind"
                                                                                                Value="Alert"/>
                                                                                        <Setter Property="Foreground"
                                                                                                Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                                                                                </DataTrigger>
                                                                        </Style.Triggers>
                                                                </Style>
                                                        </materialDesign:PackIcon.Style>
                                                </materialDesign:PackIcon>

                                                <!-- 删除按钮 -->
                                                <Button Style="{StaticResource MaterialDesignToolButton}"
                                                        Width="16"
                                                        Height="16"
                                                        Padding="0"
                                                        Margin="4,0,0,0"
                                                        Content="{materialDesign:PackIcon Kind=Close, Size=10}"
                                                        ToolTip="删除函数"
                                                        Click="OnDeleteFunctionClick"/>
                                        </StackPanel>
                                </DataTemplate>
                        </TabControl.ItemTemplate>

                        <!-- 标签页内容模板 -->
                        <TabControl.ContentTemplate>
                                <DataTemplate>
                                        <Grid Margin="8">
                                                <Grid.RowDefinitions>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="*"/>
                                                        <RowDefinition Height="Auto"/>
                                                        <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>

                                                <!-- 函数基本信息 -->
                                                <StackPanel Grid.Row="0"
                                                            Orientation="Horizontal"
                                                            Margin="0,0,0,8">
                                                        <TextBox Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}"
                                                                 materialDesign:HintAssist.Hint="函数名称"
                                                                 Width="120"
                                                                 Margin="0,0,8,0"/>

                                                        <ComboBox ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=AvailableFunctionTypes}"
                                                                  SelectedItem="{Binding Type}"
                                                                  Style="{StaticResource FunctionTypeStyle}"
                                                                  materialDesign:HintAssist.Hint="函数类型"/>

                                                        <CheckBox IsChecked="{Binding IsEnabled}"
                                                                  Content="启用"
                                                                  Margin="8,0,0,0"
                                                                  VerticalAlignment="Center"/>
                                                </StackPanel>

                                                <!-- 字段引用提示区 -->
                                                <Border Grid.Row="1"
                                                        Background="{DynamicResource MaterialDesignChipBackground}"
                                                        CornerRadius="4"
                                                        Padding="8,4"
                                                        Margin="0,0,0,8"
                                                        Visibility="{Binding HasReferencedFields, Converter={StaticResource BooleanToVisibilityConverter}}">
                                                        <StackPanel>
                                                                <TextBlock Text="引用字段:"
                                                                           FontSize="11"
                                                                           FontWeight="Medium"
                                                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                           Margin="0,0,0,4"/>

                                                                <ItemsControl ItemsSource="{Binding ReferencedFields}">
                                                                        <ItemsControl.ItemsPanel>
                                                                                <ItemsPanelTemplate>
                                                                                        <WrapPanel Orientation="Horizontal"/>
                                                                                </ItemsPanelTemplate>
                                                                        </ItemsControl.ItemsPanel>
                                                                        <ItemsControl.ItemTemplate>
                                                                                <DataTemplate>
                                                                                        <Border Style="{StaticResource FieldReferenceStyle}">
                                                                                                <TextBlock Text="{Binding}"
                                                                                                           FontSize="10"
                                                                                                           Foreground="{DynamicResource PrimaryHueDarkBrush}"/>
                                                                                        </Border>
                                                                                </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                </ItemsControl>
                                                        </StackPanel>
                                                </Border>

                                                <!-- 表达式编辑器 -->
                                                <Grid Grid.Row="2">
                                                        <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- 主编辑区 -->
                                                        <TextBox Grid.Column="0"
                                                                 x:Name="ExpressionTextBox"
                                                                 Text="{Binding Expression, UpdateSourceTrigger=PropertyChanged}"
                                                                 Style="{StaticResource ExpressionTextBoxStyle}"
                                                                 materialDesign:HintAssist.Hint="输入表达式..."
                                                                 MinHeight="80"
                                                                 TextChanged="OnExpressionTextChanged"
                                                                 PreviewKeyDown="OnExpressionKeyDown"/>

                                                        <!-- 侧边工具栏 -->
                                                        <StackPanel Grid.Column="1"
                                                                    Margin="8,0,0,0">
                                                                <Button Style="{StaticResource ToolButtonStyle}"
                                                                        Content="{materialDesign:PackIcon Kind=Function}"
                                                                        ToolTip="插入函数"
                                                                        Click="OnInsertFunctionClick"/>

                                                                <Button Style="{StaticResource ToolButtonStyle}"
                                                                        Content="{materialDesign:PackIcon Kind=Variable}"
                                                                        ToolTip="插入字段"
                                                                        Click="OnInsertFieldClick"/>

                                                                <Button Style="{StaticResource ToolButtonStyle}"
                                                                        Content="{materialDesign:PackIcon Kind=CodeBraces}"
                                                                        ToolTip="插入括号"
                                                                        Click="OnInsertParenthesesClick"/>

                                                                <Button Style="{StaticResource ToolButtonStyle}"
                                                                        Content="{materialDesign:PackIcon Kind=Play}"
                                                                        ToolTip="测试表达式"
                                                                        Click="OnTestExpressionClick"/>
                                                        </StackPanel>
                                                </Grid>

                                                <!-- 验证结果 -->
                                                <TextBlock Grid.Row="3"
                                                           Text="{Binding ValidationResult}"
                                                           Style="{StaticResource ValidationErrorStyle}"
                                                           Visibility="{Binding ValidationResult.IsValid, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}"/>

                                                <!-- 函数描述 -->
                                                <TextBox Grid.Row="4"
                                                         Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                                         materialDesign:HintAssist.Hint="函数描述（可选）"
                                                         FontSize="11"
                                                         Margin="0,4,0,0"
                                                         MaxHeight="40"/>
                                        </Grid>
                                </DataTemplate>
                        </TabControl.ContentTemplate>
                </TabControl>

                <!-- 空状态提示 -->
                <StackPanel x:Name="EmptyStatePanel"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Visibility="{Binding HasFunctions, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}">
                        <materialDesign:PackIcon Kind="Function"
                                                 Width="48"
                                                 Height="48"
                                                 Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                 HorizontalAlignment="Center"
                                                 Margin="0,0,0,16"/>
                        <TextBlock Text="点击 + 按钮添加函数表达式"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   FontSize="14"
                                   TextAlignment="Center"/>
                </StackPanel>

                <!-- 函数智能提示弹出框 -->
                <Popup x:Name="IntelliSensePopup"
                       PlacementTarget="{Binding ElementName=FunctionTabControl}"
                       Placement="Bottom"
                       AllowsTransparency="True"
                       StaysOpen="False">
                        <Border Background="{DynamicResource MaterialDesignPaper}"
                                BorderBrush="{DynamicResource MaterialDesignDivider}"
                                BorderThickness="1"
                                CornerRadius="4"
                                Effect="{StaticResource MaterialDesignShadowDepth2}">
                                <ListBox x:Name="IntelliSenseListBox"
                                         MaxHeight="200"
                                         MinWidth="200"
                                         ItemsSource="{Binding IntelliSenseItems}"
                                         SelectedItem="{Binding SelectedIntelliSenseItem}"
                                         KeyDown="OnIntelliSenseKeyDown">
                                        <ListBox.ItemTemplate>
                                                <DataTemplate>
                                                        <StackPanel Orientation="Horizontal">
                                                                <materialDesign:PackIcon Kind="{Binding Icon}"
                                                                                         Width="16"
                                                                                         Height="16"
                                                                                         Margin="0,0,8,0"
                                                                                         VerticalAlignment="Center"/>
                                                                <StackPanel>
                                                                        <TextBlock Text="{Binding Name}"
                                                                                   FontWeight="Medium"/>
                                                                        <TextBlock Text="{Binding Description}"
                                                                                   FontSize="11"
                                                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                                </StackPanel>
                                                        </StackPanel>
                                                </DataTemplate>
                                        </ListBox.ItemTemplate>
                                </ListBox>
                        </Border>
                </Popup>
        </DockPanel>
</UserControl>
