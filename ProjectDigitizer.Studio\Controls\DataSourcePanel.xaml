<UserControl x:Class="ProjectDigitizer.Studio.Controls.DataSourcePanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="400" d:DesignWidth="200">

    <UserControl.Resources>
        <!-- 字段项样式 -->
        <Style x:Key="FieldItemStyle" TargetType="TreeViewItem">
            <Setter Property="IsExpanded" Value="True"/>
            <Setter Property="Padding" Value="2"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 数据类型图标转换器 -->
        <Style x:Key="DataTypeIconStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Margin" Value="0,0,4,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding DataType}" Value="Number">
                    <Setter Property="Kind" Value="Numeric"/>
                    <Setter Property="Foreground" Value="#2196F3"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Text">
                    <Setter Property="Kind" Value="FormatText"/>
                    <Setter Property="Foreground" Value="#4CAF50"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Boolean">
                    <Setter Property="Kind" Value="CheckboxMarked"/>
                    <Setter Property="Foreground" Value="#FF9800"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="DateTime">
                    <Setter Property="Kind" Value="Calendar"/>
                    <Setter Property="Foreground" Value="#9C27B0"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="File">
                    <Setter Property="Kind" Value="File"/>
                    <Setter Property="Foreground" Value="#607D8B"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Geometry">
                    <Setter Property="Kind" Value="VectorTriangle"/>
                    <Setter Property="Foreground" Value="#E91E63"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Array">
                    <Setter Property="Kind" Value="FormatListBulleted"/>
                    <Setter Property="Foreground" Value="#795548"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Object">
                    <Setter Property="Kind" Value="CodeBraces"/>
                    <Setter Property="Foreground" Value="#3F51B5"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding DataType}" Value="Any">
                    <Setter Property="Kind" Value="Database"/>
                    <Setter Property="Foreground" Value="#757575"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StateIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Height" Value="8"/>
            <Setter Property="Margin" Value="4,0,0,0"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding State}" Value="Available">
                    <Setter Property="Fill" Value="Transparent"/>
                    <Setter Property="Stroke" Value="{DynamicResource MaterialDesignDivider}"/>
                    <Setter Property="StrokeThickness" Value="1"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding State}" Value="Connected">
                    <Setter Property="Fill" Value="{DynamicResource PrimaryHueMidBrush}"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding State}" Value="Referenced">
                    <Setter Property="Fill" Value="{DynamicResource SecondaryHueMidBrush}"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding State}" Value="Active">
                    <Setter Property="Fill" Value="{DynamicResource PrimaryHueDarkBrush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <DockPanel>
        <!-- 顶部控制区 -->
        <StackPanel DockPanel.Dock="Top" Margin="0,0,0,12">
            <!-- 数据来源模式切换 -->
            <Grid Margin="0,0,0,8">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                           Text="{Binding DataSourceModeText}" 
                           FontSize="12" 
                           FontWeight="Medium"
                           VerticalAlignment="Center"/>
                
                <ToggleButton Grid.Column="1"
                              x:Name="DataSourceModeToggle" 
                              Style="{StaticResource MaterialDesignSwitchToggleButton}"
                              ToolTip="切换数据来源：节点数据 / 外部文件"
                              IsChecked="{Binding IsExternalFileMode}"/>
            </Grid>
            
            <!-- 外部文件选择（仅在外部文件模式下显示） -->
            <Grid Margin="0,0,0,8" 
                  Visibility="{Binding IsExternalFileMode, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0"
                         x:Name="ExternalFilePathTextBox"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         materialDesign:HintAssist.Hint="选择数据文件..."
                         Text="{Binding ExternalFilePath}"
                         IsReadOnly="True"
                         FontSize="11"/>
                
                <Button Grid.Column="1"
                        x:Name="BrowseFileButton"
                        Style="{StaticResource MaterialDesignIconButton}"
                        Width="32" Height="32"
                        Margin="4,0,0,0"
                        Content="{materialDesign:PackIcon Kind=FolderOpen, Size=16}"
                        ToolTip="浏览文件"/>
            </Grid>
            
            <!-- 搜索框 -->
            <TextBox x:Name="FieldSearchBox" 
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:HintAssist.Hint="搜索字段..."
                     materialDesign:TextFieldAssist.HasLeadingIcon="True"
                     materialDesign:TextFieldAssist.LeadingIcon="Search"
                     FontSize="12"
                     Height="36"
                     Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"/>
        </StackPanel>

        <!-- 字段统计信息 -->
        <Border DockPanel.Dock="Top" 
                Background="{DynamicResource MaterialDesignChipBackground}"
                CornerRadius="4"
                Padding="8,4"
                Margin="0,0,0,8">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="字段:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="{Binding TotalFieldCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="已连接:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="4,0,0,0"/>
                <TextBlock Text="{Binding ConnectedFieldCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="已引用:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="4,0,0,0"/>
                <TextBlock Text="{Binding ReferencedFieldCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
            </StackPanel>
        </Border>

        <!-- 字段树 -->
        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Auto">
            <TreeView x:Name="FieldTreeView" 
                      ItemsSource="{Binding FilteredFields}"
                      Background="Transparent"
                      BorderThickness="0">
                <TreeView.ItemContainerStyle>
                    <Style TargetType="TreeViewItem" BasedOn="{StaticResource FieldItemStyle}">
                        <EventSetter Event="MouseDoubleClick" Handler="OnFieldDoubleClick"/>
                        <EventSetter Event="PreviewMouseRightButtonDown" Handler="OnFieldRightClick"/>
                    </Style>
                </TreeView.ItemContainerStyle>
                
                <TreeView.ItemTemplate>
                    <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                        <StackPanel Orientation="Horizontal" Margin="2">
                            <!-- 字段类型图标 -->
                            <materialDesign:PackIcon Style="{StaticResource DataTypeIconStyle}"/>
                            
                            <!-- 字段名称 -->
                            <TextBlock Text="{Binding DisplayName}" 
                                       FontSize="12"
                                       VerticalAlignment="Center"
                                       ToolTip="{Binding Description}"/>
                            
                            <!-- 状态指示器 -->
                            <Ellipse Style="{StaticResource StateIndicatorStyle}"/>
                            
                            <!-- 示例值（如果有） -->
                            <TextBlock Text="{Binding SampleValueText}" 
                                       FontSize="10"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                       Margin="8,0,0,0"
                                       VerticalAlignment="Center"
                                       Visibility="{Binding HasSampleValue, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </StackPanel>
                    </HierarchicalDataTemplate>
                </TreeView.ItemTemplate>
                
                <!-- 右键菜单 -->
                <TreeView.ContextMenu>
                    <ContextMenu>
                        <MenuItem Header="添加到已选字段" 
                                  Icon="{materialDesign:PackIcon Kind=Plus}"
                                  Click="OnAddToSelectedClick"/>
                        <MenuItem Header="查看字段详情" 
                                  Icon="{materialDesign:PackIcon Kind=Information}"
                                  Click="OnViewFieldDetailsClick"/>
                        <Separator/>
                        <MenuItem Header="刷新字段列表" 
                                  Icon="{materialDesign:PackIcon Kind=Refresh}"
                                  Click="OnRefreshFieldsClick"/>
                    </ContextMenu>
                </TreeView.ContextMenu>
            </TreeView>
        </ScrollViewer>

        <!-- 空状态提示 -->
        <StackPanel x:Name="EmptyStatePanel"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Visibility="{Binding HasFields, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}">
            <materialDesign:PackIcon Kind="DatabaseOff"
                                     Width="48"
                                     Height="48"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,16"/>
            <TextBlock x:Name="EmptyStateText"
                       Text="{Binding EmptyStateMessage}"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       FontSize="14"
                       TextAlignment="Center"
                       TextWrapping="Wrap"
                       MaxWidth="160"/>
        </StackPanel>
    </DockPanel>
</UserControl>
