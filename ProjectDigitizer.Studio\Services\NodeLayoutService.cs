using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using ProjectDigitizer.Studio.ViewModels;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 节点布局算法类型
    /// </summary>
    public enum LayoutAlgorithm
    {
        /// <summary>
        /// 层次布局 - 按照数据流方向排列，支持多行换行
        /// </summary>
        Hierarchical
    }

    /// <summary>
    /// 布局配置选项
    /// </summary>
    public class LayoutOptions
    {
        /// <summary>
        /// 节点间水平间距
        /// </summary>
        public double HorizontalSpacing { get; set; } = 300;

        /// <summary>
        /// 节点间垂直间距
        /// </summary>
        public double VerticalSpacing { get; set; } = 150;

        /// <summary>
        /// 布局起始位置
        /// </summary>
        public Point StartPosition { get; set; } = new Point(100, 100);

        /// <summary>
        /// 是否启用动画过渡
        /// </summary>
        public bool EnableAnimation { get; set; } = true;

        /// <summary>
        /// 动画持续时间（毫秒）
        /// </summary>
        public int AnimationDuration { get; set; } = 800;

        /// <summary>
        /// 画布可见宽度（用于换行计算）
        /// </summary>
        public double CanvasWidth { get; set; } = 1200;

        /// <summary>
        /// 画布可见高度
        /// </summary>
        public double CanvasHeight { get; set; } = 800;

        /// <summary>
        /// 节点估算宽度（用于换行计算）
        /// </summary>
        public double NodeWidth { get; set; } = 250;

        /// <summary>
        /// 节点估算高度
        /// </summary>
        public double NodeHeight { get; set; } = 120;

        /// <summary>
        /// 行间距（多行布局时的行间距）
        /// </summary>
        public double RowSpacing { get; set; } = 200;

        /// <summary>
        /// 锁定节点列表 - 这些节点的位置不会被改变，但会作为障碍物考虑
        /// </summary>
        public List<ModuleNodeViewModel> LockedNodes { get; set; } = new List<ModuleNodeViewModel>();

        /// <summary>
        /// 避免重叠的安全边距
        /// </summary>
        public double SafetyMargin { get; set; } = 20;
    }

    /// <summary>
    /// 节点自动布局服务
    /// </summary>
    public class NodeLayoutService
    {
        /// <summary>
        /// 执行自动布局
        /// </summary>
        /// <param name="nodes">要布局的节点集合</param>
        /// <param name="connections">连接关系</param>
        /// <param name="algorithm">布局算法</param>
        /// <param name="options">布局选项</param>
        /// <returns>布局后的节点位置字典</returns>
        public Dictionary<ModuleNodeViewModel, Point> CalculateLayout(
            IEnumerable<ModuleNodeViewModel> nodes,
            IEnumerable<ConnectionViewModel> connections,
            LayoutAlgorithm algorithm,
            LayoutOptions? options = null)
        {
            options ??= new LayoutOptions();
            var nodeList = nodes.ToList();

            if (!nodeList.Any())
                return new Dictionary<ModuleNodeViewModel, Point>();

            return algorithm switch
            {
                LayoutAlgorithm.Hierarchical => CalculateHierarchicalLayoutWithWrapping(nodeList, connections, options),
                _ => throw new ArgumentException($"不支持的布局算法: {algorithm}")
            };
        }

        /// <summary>
        /// 计算支持换行的层次布局
        /// </summary>
        private Dictionary<ModuleNodeViewModel, Point> CalculateHierarchicalLayoutWithWrapping(
            List<ModuleNodeViewModel> nodes,
            IEnumerable<ConnectionViewModel> connections,
            LayoutOptions options)
        {
            var result = new Dictionary<ModuleNodeViewModel, Point>();
            var connectionList = connections?.ToList() ?? new List<ConnectionViewModel>();

            // 参数验证
            if (nodes == null || !nodes.Any())
            {
                return result;
            }

            if (options == null)
            {
                throw new ArgumentNullException(nameof(options));
            }

            // 分离锁定和未锁定的节点
            var unlockedNodes = nodes.Where(n => n != null && !n.IsLocked).ToList();
            var lockedNodes = nodes.Where(n => n != null && n.IsLocked).ToList();

            System.Diagnostics.Debug.WriteLine($"布局算法：总节点 {nodes.Count}, 未锁定 {unlockedNodes.Count}, 锁定 {lockedNodes.Count}");

            // 将锁定节点的当前位置添加到结果中（它们不参与布局计算）
            foreach (var lockedNode in lockedNodes)
            {
                if (lockedNode != null)
                {
                    result[lockedNode] = lockedNode.Location;
                    System.Diagnostics.Debug.WriteLine($"锁定节点 {lockedNode.Title} 保持位置: {lockedNode.Location}");
                }
            }

            // 如果没有未锁定的节点，直接返回
            if (!unlockedNodes.Any())
            {
                System.Diagnostics.Debug.WriteLine("所有节点都已锁定，直接返回");
                return result;
            }

            // 如果只有一个未锁定节点，需要找到合适的位置避免与锁定节点重叠
            if (unlockedNodes.Count == 1)
            {
                var position = FindSafePosition(options.StartPosition, lockedNodes, options);
                result[unlockedNodes[0]] = position;
                return result;
            }

            // 1. 构建图结构（只包含未锁定的节点）
            var graph = BuildGraph(unlockedNodes, connectionList);

            // 2. 拓扑排序，确定层级
            var layers = PerformTopologicalSort(graph);

            // 3. 对每层内的节点进行排序，确保数据流逻辑正确
            SortNodesWithinLayers(layers, connectionList);

            // 4. 计算支持换行的位置，避免与锁定节点重叠
            if (lockedNodes.Any())
            {
                CalculateWrappedPositionsWithObstacles(layers, options, result, lockedNodes);
            }
            else
            {
                CalculateWrappedPositions(layers, options, result);
            }

            return result;
        }

        /// <summary>
        /// 计算支持换行的节点位置 - 重新设计的层次布局算法
        /// </summary>
        private void CalculateWrappedPositions(
            List<List<ModuleNodeViewModel>> layers,
            LayoutOptions options,
            Dictionary<ModuleNodeViewModel, Point> result)
        {
            var currentX = options.StartPosition.X;
            var currentRow = 0;
            var nodeWidthWithSpacing = options.NodeWidth + options.HorizontalSpacing;
            var maxX = options.StartPosition.X + options.CanvasWidth - options.NodeWidth - options.SafetyMargin;

            System.Diagnostics.Debug.WriteLine($"开始计算位置：层数={layers.Count}, 起始位置=({options.StartPosition.X}, {options.StartPosition.Y})");
            System.Diagnostics.Debug.WriteLine($"节点尺寸：宽度={options.NodeWidth}, 高度={options.NodeHeight}");
            System.Diagnostics.Debug.WriteLine($"间距设置：水平={options.HorizontalSpacing}, 垂直={options.VerticalSpacing}, 行间距={options.RowSpacing}");

            // 改进的层次布局：优先保持层次关系，同时支持换行
            for (int layerIndex = 0; layerIndex < layers.Count; layerIndex++)
            {
                var layer = layers[layerIndex];
                if (!layer.Any()) continue;

                System.Diagnostics.Debug.WriteLine($"处理第 {layerIndex} 层：节点数={layer.Count}");

                // 为当前层的每个节点分配位置
                for (int nodeIndex = 0; nodeIndex < layer.Count; nodeIndex++)
                {
                    var node = layer[nodeIndex];

                    // 检查是否需要换行
                    var nodeEndX = currentX + options.NodeWidth;
                    if (nodeEndX > maxX && currentX > options.StartPosition.X)
                    {
                        // 换行到下一行
                        currentX = options.StartPosition.X;
                        currentRow++;
                        System.Diagnostics.Debug.WriteLine($"换行到第 {currentRow} 行");
                    }

                    // 计算Y位置：基础Y + 行偏移
                    var baseY = options.StartPosition.Y + currentRow * (options.NodeHeight + options.RowSpacing);
                    var y = baseY;

                    // 如果同一层有多个节点，垂直分布它们
                    if (layer.Count > 1)
                    {
                        // 计算层内节点的垂直分布
                        // 第一个节点在最上方，后续节点依次向下排列
                        y = baseY + nodeIndex * options.VerticalSpacing;
                        System.Diagnostics.Debug.WriteLine($"  层内排序：第 {nodeIndex} 位节点 {node.Title} Y坐标: {y}");
                    }

                    var position = new Point(currentX, y);
                    result[node] = position;

                    System.Diagnostics.Debug.WriteLine($"节点 {node.Title} (层{layerIndex}) 位置: ({position.X:F1}, {position.Y:F1})");
                }

                // 处理完一层后，移动到下一列（只有当层不为空时）
                if (layer.Any())
                {
                    currentX += nodeWidthWithSpacing;
                }
            }

            System.Diagnostics.Debug.WriteLine("位置计算完成");
        }

        /// <summary>
        /// 计算支持换行且避免障碍物的节点位置 - 重新设计的层次布局算法
        /// </summary>
        private void CalculateWrappedPositionsWithObstacles(
            List<List<ModuleNodeViewModel>> layers,
            LayoutOptions options,
            Dictionary<ModuleNodeViewModel, Point> result,
            List<ModuleNodeViewModel> obstacles)
        {
            var currentX = options.StartPosition.X;
            var currentRow = 0;
            var nodeWidthWithSpacing = options.NodeWidth + options.HorizontalSpacing;
            var maxX = options.StartPosition.X + options.CanvasWidth - options.NodeWidth - options.SafetyMargin;

            System.Diagnostics.Debug.WriteLine($"开始计算位置（含障碍物）：层数={layers.Count}, 障碍物数={obstacles.Count}");
            System.Diagnostics.Debug.WriteLine($"节点尺寸：宽度={options.NodeWidth}, 高度={options.NodeHeight}");
            System.Diagnostics.Debug.WriteLine($"间距设置：水平={options.HorizontalSpacing}, 垂直={options.VerticalSpacing}, 行间距={options.RowSpacing}");

            // 创建已占用位置的集合，包括障碍物和已分配的节点
            var occupiedPositions = new List<Rect>();

            // 添加障碍物位置
            foreach (var obstacle in obstacles)
            {
                var obstacleRect = new Rect(
                    obstacle.Location.X - options.SafetyMargin,
                    obstacle.Location.Y - options.SafetyMargin,
                    options.NodeWidth + 2 * options.SafetyMargin,
                    options.NodeHeight + 2 * options.SafetyMargin);
                occupiedPositions.Add(obstacleRect);
                System.Diagnostics.Debug.WriteLine($"障碍物位置: ({obstacle.Location.X:F1}, {obstacle.Location.Y:F1})");
            }

            // 改进的层次布局：优先保持层次关系，同时支持换行和避障
            for (int layerIndex = 0; layerIndex < layers.Count; layerIndex++)
            {
                var layer = layers[layerIndex];
                if (!layer.Any()) continue;

                System.Diagnostics.Debug.WriteLine($"处理第 {layerIndex} 层：节点数={layer.Count}");

                // 为当前层的每个节点分配位置
                for (int nodeIndex = 0; nodeIndex < layer.Count; nodeIndex++)
                {
                    var node = layer[nodeIndex];

                    // 检查是否需要换行
                    var nodeEndX = currentX + options.NodeWidth;
                    if (nodeEndX > maxX && currentX > options.StartPosition.X)
                    {
                        // 换行到下一行
                        currentX = options.StartPosition.X;
                        currentRow++;
                        System.Diagnostics.Debug.WriteLine($"换行到第 {currentRow} 行");
                    }

                    // 计算初始Y位置
                    var baseY = options.StartPosition.Y + currentRow * (options.NodeHeight + options.RowSpacing);
                    var y = baseY;

                    // 如果同一层有多个节点，垂直分布它们
                    if (layer.Count > 1)
                    {
                        // 计算层内节点的垂直分布
                        // 第一个节点在最上方，后续节点依次向下排列
                        y = baseY + nodeIndex * options.VerticalSpacing;
                        System.Diagnostics.Debug.WriteLine($"  层内排序（含障碍物）：第 {nodeIndex} 位节点 {node.Title} Y坐标: {y}");
                    }

                    var proposedPosition = new Point(currentX, y);

                    // 寻找安全位置，避免与障碍物和已分配节点重叠
                    var safePosition = FindSafePositionWithOccupiedCheck(proposedPosition, occupiedPositions, options);
                    result[node] = safePosition;

                    // 将新分配的位置添加到已占用位置集合
                    var newOccupiedRect = new Rect(
                        safePosition.X - options.SafetyMargin,
                        safePosition.Y - options.SafetyMargin,
                        options.NodeWidth + 2 * options.SafetyMargin,
                        options.NodeHeight + 2 * options.SafetyMargin);
                    occupiedPositions.Add(newOccupiedRect);

                    System.Diagnostics.Debug.WriteLine($"节点 {node.Title} (层{layerIndex}) 位置: ({safePosition.X:F1}, {safePosition.Y:F1})");
                }

                // 处理完一层后，移动到下一列（只有当层不为空时）
                if (layer.Any())
                {
                    currentX += nodeWidthWithSpacing;
                }
            }

            System.Diagnostics.Debug.WriteLine("位置计算完成（含障碍物）");
        }

        /// <summary>
        /// 寻找安全位置，避免与障碍物重叠并确保在可见范围内
        /// </summary>
        private Point FindSafePosition(Point proposedPosition, List<ModuleNodeViewModel> obstacles, LayoutOptions options)
        {
            if (!obstacles.Any())
                return EnsurePositionInBounds(proposedPosition, options);

            var safePosition = proposedPosition;
            var attempts = 0;
            const int maxAttempts = 50; // 防止无限循环

            while (attempts < maxAttempts && (IsPositionOccupied(safePosition, obstacles, options) || !IsPositionInBounds(safePosition, options)))
            {
                // 尝试不同的位置策略
                if (attempts < 10)
                {
                    // 向右移动
                    safePosition = new Point(safePosition.X + options.HorizontalSpacing, safePosition.Y);
                }
                else if (attempts < 20)
                {
                    // 向下移动
                    safePosition = new Point(proposedPosition.X, proposedPosition.Y + options.VerticalSpacing);
                }
                else if (attempts < 30)
                {
                    // 向右下移动
                    safePosition = new Point(
                        proposedPosition.X + (attempts - 20) * options.HorizontalSpacing / 2,
                        proposedPosition.Y + (attempts - 20) * options.VerticalSpacing / 2);
                }
                else
                {
                    // 螺旋搜索
                    var angle = attempts * 0.5;
                    var radius = (attempts - 30) * 50;
                    safePosition = new Point(
                        proposedPosition.X + radius * Math.Cos(angle),
                        proposedPosition.Y + radius * Math.Sin(angle));
                }

                attempts++;
            }

            return EnsurePositionInBounds(safePosition, options);
        }

        /// <summary>
        /// 寻找安全位置，避免与已占用区域重叠并确保在可见范围内
        /// </summary>
        private Point FindSafePositionWithOccupiedCheck(Point proposedPosition, List<Rect> occupiedPositions, LayoutOptions options)
        {
            var safePosition = proposedPosition;
            var attempts = 0;
            const int maxAttempts = 100; // 增加尝试次数

            while (attempts < maxAttempts && (IsPositionOccupiedByRect(safePosition, occupiedPositions, options) || !IsPositionInBounds(safePosition, options)))
            {
                // 使用更系统的搜索策略
                if (attempts < 20)
                {
                    // 向右移动
                    safePosition = new Point(proposedPosition.X + (attempts + 1) * (options.HorizontalSpacing / 4), proposedPosition.Y);
                }
                else if (attempts < 40)
                {
                    // 向下移动
                    var downOffset = (attempts - 19) * (options.VerticalSpacing / 4);
                    safePosition = new Point(proposedPosition.X, proposedPosition.Y + downOffset);
                }
                else if (attempts < 60)
                {
                    // 向左移动
                    var leftOffset = (attempts - 39) * (options.HorizontalSpacing / 4);
                    safePosition = new Point(proposedPosition.X - leftOffset, proposedPosition.Y);
                }
                else if (attempts < 80)
                {
                    // 向上移动
                    var upOffset = (attempts - 59) * (options.VerticalSpacing / 4);
                    safePosition = new Point(proposedPosition.X, proposedPosition.Y - upOffset);
                }
                else
                {
                    // 螺旋搜索
                    var angle = (attempts - 79) * 0.3;
                    var radius = (attempts - 79) * 30;
                    safePosition = new Point(
                        proposedPosition.X + radius * Math.Cos(angle),
                        proposedPosition.Y + radius * Math.Sin(angle));
                }

                attempts++;
            }

            return EnsurePositionInBounds(safePosition, options);
        }

        /// <summary>
        /// 检查位置是否与已占用区域重叠
        /// </summary>
        private bool IsPositionOccupiedByRect(Point position, List<Rect> occupiedPositions, LayoutOptions options)
        {
            var nodeRect = new Rect(
                position.X,
                position.Y,
                options.NodeWidth,
                options.NodeHeight);

            foreach (var occupiedRect in occupiedPositions)
            {
                if (nodeRect.IntersectsWith(occupiedRect))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 检查位置是否在可见范围内
        /// </summary>
        private bool IsPositionInBounds(Point position, LayoutOptions options)
        {
            var maxX = options.StartPosition.X + options.CanvasWidth - options.NodeWidth - options.SafetyMargin;
            var maxY = options.StartPosition.Y + options.CanvasHeight - options.NodeHeight - options.SafetyMargin;

            return position.X >= options.StartPosition.X &&
                   position.Y >= options.StartPosition.Y &&
                   position.X + options.NodeWidth <= maxX + options.SafetyMargin &&
                   position.Y + options.NodeHeight <= maxY + options.SafetyMargin;
        }

        /// <summary>
        /// 确保位置在可见范围内，如果超出则调整到边界
        /// </summary>
        private Point EnsurePositionInBounds(Point position, LayoutOptions options)
        {
            var maxX = options.StartPosition.X + options.CanvasWidth - options.NodeWidth - options.SafetyMargin;
            var maxY = options.StartPosition.Y + options.CanvasHeight - options.NodeHeight - options.SafetyMargin;

            var adjustedX = Math.Max(options.StartPosition.X, Math.Min(position.X, maxX));
            var adjustedY = Math.Max(options.StartPosition.Y, Math.Min(position.Y, maxY));

            return new Point(adjustedX, adjustedY);
        }

        /// <summary>
        /// 检查位置是否被障碍物占用
        /// </summary>
        private bool IsPositionOccupied(Point position, List<ModuleNodeViewModel> obstacles, LayoutOptions options)
        {
            var nodeRect = new Rect(
                position.X - options.SafetyMargin,
                position.Y - options.SafetyMargin,
                options.NodeWidth + 2 * options.SafetyMargin,
                options.NodeHeight + 2 * options.SafetyMargin);

            foreach (var obstacle in obstacles)
            {
                var obstacleRect = new Rect(
                    obstacle.Location.X - options.SafetyMargin,
                    obstacle.Location.Y - options.SafetyMargin,
                    options.NodeWidth + 2 * options.SafetyMargin,
                    options.NodeHeight + 2 * options.SafetyMargin);

                if (nodeRect.IntersectsWith(obstacleRect))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 构建图结构（只包含未锁定节点，但考虑所有连接关系）
        /// </summary>
        private Dictionary<ModuleNodeViewModel, List<ModuleNodeViewModel>> BuildGraph(
            List<ModuleNodeViewModel> nodes,
            List<ConnectionViewModel> connections)
        {
            var graph = new Dictionary<ModuleNodeViewModel, List<ModuleNodeViewModel>>();

            System.Diagnostics.Debug.WriteLine($"构建图结构：节点数 {nodes.Count}, 连接数 {connections.Count}");

            // 初始化图（只包含传入的节点，通常是未锁定的节点）
            foreach (var node in nodes)
            {
                if (node != null)
                {
                    graph[node] = new List<ModuleNodeViewModel>();
                    System.Diagnostics.Debug.WriteLine($"添加节点到图: {node.Title}");
                }
            }

            // 添加边（只添加两端都在图中的连接）
            var validConnections = 0;
            var skippedConnections = 0;

            foreach (var connection in connections)
            {
                if (connection?.Source?.Node is ModuleNodeViewModel sourceNode &&
                    connection?.Target?.Node is ModuleNodeViewModel targetNode)
                {
                    // 验证连接方向：Source应该是输出连接器，Target应该是输入连接器
                    var sourceIsOutput = connection.Source?.IsInput == false;
                    var targetIsInput = connection.Target?.IsInput == true;

                    if (!sourceIsOutput || !targetIsInput)
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：连接方向异常 - Source.IsInput={connection.Source?.IsInput}, Target.IsInput={connection.Target?.IsInput}");
                    }

                    // 只有当源节点和目标节点都在图中时，才添加连接
                    if (graph.ContainsKey(sourceNode) && graph.ContainsKey(targetNode))
                    {
                        graph[sourceNode].Add(targetNode);
                        validConnections++;
                        System.Diagnostics.Debug.WriteLine($"添加连接: {sourceNode.Title} -> {targetNode.Title} (Source.IsInput={connection.Source?.IsInput}, Target.IsInput={connection.Target?.IsInput})");
                    }
                    else
                    {
                        skippedConnections++;
                        System.Diagnostics.Debug.WriteLine($"跳过连接（涉及锁定节点）: {sourceNode.Title} -> {targetNode.Title}");
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"图构建完成：有效连接 {validConnections}, 跳过连接 {skippedConnections}");
            return graph;
        }

        /// <summary>
        /// 拓扑排序 - 使用Kahn算法进行层次分配
        /// </summary>
        private List<List<ModuleNodeViewModel>> PerformTopologicalSort(
            Dictionary<ModuleNodeViewModel, List<ModuleNodeViewModel>> graph)
        {
            var layers = new List<List<ModuleNodeViewModel>>();
            var inDegree = new Dictionary<ModuleNodeViewModel, int>();
            var processed = new HashSet<ModuleNodeViewModel>();

            System.Diagnostics.Debug.WriteLine("开始拓扑排序...");

            // 计算入度
            foreach (var node in graph.Keys)
            {
                inDegree[node] = 0;
            }

            foreach (var kvp in graph)
            {
                foreach (var target in kvp.Value)
                {
                    // 确保目标节点在入度字典中存在
                    if (inDegree.ContainsKey(target))
                    {
                        inDegree[target]++;
                    }
                }
            }

            // 输出初始入度信息
            System.Diagnostics.Debug.WriteLine("初始入度信息:");
            foreach (var kvp in inDegree)
            {
                System.Diagnostics.Debug.WriteLine($"  {kvp.Key.Title}: 入度 = {kvp.Value}");
            }

            var layerIndex = 0;
            var cycleDetected = false;

            // 分层处理
            while (processed.Count < graph.Count)
            {
                var currentLayer = new List<ModuleNodeViewModel>();

                // 找到当前层的节点（入度为0的节点）
                foreach (var node in graph.Keys)
                {
                    if (!processed.Contains(node) && inDegree[node] == 0)
                    {
                        currentLayer.Add(node);
                    }
                }

                // 如果没有找到入度为0的节点，说明有环
                if (!currentLayer.Any())
                {
                    cycleDetected = true;
                    System.Diagnostics.Debug.WriteLine($"检测到循环依赖！剩余未处理节点:");

                    var remaining = graph.Keys.Where(n => !processed.Contains(n)).ToList();
                    foreach (var node in remaining)
                    {
                        System.Diagnostics.Debug.WriteLine($"  {node.Title}: 入度 = {inDegree[node]}");
                    }

                    // 选择入度最小的节点打破循环
                    var nodeToBreakCycle = remaining.OrderBy(n => inDegree[n]).First();
                    currentLayer.Add(nodeToBreakCycle);
                    System.Diagnostics.Debug.WriteLine($"选择节点 {nodeToBreakCycle.Title} 打破循环");
                }

                System.Diagnostics.Debug.WriteLine($"第 {layerIndex} 层节点:");
                foreach (var node in currentLayer)
                {
                    System.Diagnostics.Debug.WriteLine($"  {node.Title}");
                }

                layers.Add(currentLayer);

                // 更新入度并标记已处理
                foreach (var node in currentLayer)
                {
                    processed.Add(node);

                    // 确保节点在图中存在
                    if (graph.ContainsKey(node))
                    {
                        foreach (var target in graph[node])
                        {
                            if (!processed.Contains(target) && inDegree.ContainsKey(target))
                            {
                                inDegree[target]--;
                                System.Diagnostics.Debug.WriteLine($"    更新 {target.Title} 入度: {inDegree[target] + 1} -> {inDegree[target]}");
                            }
                        }
                    }
                }

                layerIndex++;

                // 防止无限循环
                if (layerIndex > graph.Count)
                {
                    System.Diagnostics.Debug.WriteLine("拓扑排序超出最大层数限制，强制退出");
                    break;
                }
            }

            if (cycleDetected)
            {
                System.Diagnostics.Debug.WriteLine("警告：检测到循环依赖，已自动处理");
            }

            System.Diagnostics.Debug.WriteLine($"拓扑排序完成，共 {layers.Count} 层");
            return layers;
        }

        /// <summary>
        /// 对每层内的节点进行排序，确保数据流逻辑正确
        /// </summary>
        /// <param name="layers">分层后的节点</param>
        /// <param name="connections">连接关系</param>
        private void SortNodesWithinLayers(List<List<ModuleNodeViewModel>> layers, List<ConnectionViewModel> connections)
        {
            System.Diagnostics.Debug.WriteLine("开始层内排序...");

            for (int layerIndex = 0; layerIndex < layers.Count; layerIndex++)
            {
                var layer = layers[layerIndex];
                if (layer.Count <= 1) continue;

                System.Diagnostics.Debug.WriteLine($"排序第 {layerIndex} 层，节点数: {layer.Count}");

                // 对当前层的节点进行排序
                layer.Sort((a, b) => CompareNodesInLayer(a, b, connections, layers, layerIndex));

                // 输出排序结果
                for (int i = 0; i < layer.Count; i++)
                {
                    System.Diagnostics.Debug.WriteLine($"  第 {layerIndex} 层第 {i} 位: {layer[i].Title}");
                }
            }

            System.Diagnostics.Debug.WriteLine("层内排序完成");
        }

        /// <summary>
        /// 比较同一层内两个节点的排序优先级
        /// </summary>
        /// <param name="nodeA">节点A</param>
        /// <param name="nodeB">节点B</param>
        /// <param name="connections">连接关系</param>
        /// <param name="layers">所有层级</param>
        /// <param name="currentLayerIndex">当前层索引</param>
        /// <returns>比较结果</returns>
        private int CompareNodesInLayer(ModuleNodeViewModel nodeA, ModuleNodeViewModel nodeB,
            List<ConnectionViewModel> connections, List<List<ModuleNodeViewModel>> layers, int currentLayerIndex)
        {
            System.Diagnostics.Debug.WriteLine($"    比较节点排序: {nodeA.Title} vs {nodeB.Title}");

            // 1. 首先检查是否有直接的连接关系影响排序
            var connectionOrder = GetConnectionBasedOrder(nodeA, nodeB, connections, layers, currentLayerIndex);
            if (connectionOrder != 0)
            {
                var winner = connectionOrder < 0 ? nodeA.Title : nodeB.Title;
                System.Diagnostics.Debug.WriteLine($"      连接关系决定: {winner} 优先 (connectionOrder={connectionOrder})");
                return connectionOrder;
            }

            // 2. 按节点类型优先级排序：Input > Transform > Control > Output
            var aTypePriority = GetNodeTypePriority(nodeA.NodeType);
            var bTypePriority = GetNodeTypePriority(nodeB.NodeType);
            if (aTypePriority != bTypePriority)
            {
                var winner = aTypePriority < bTypePriority ? nodeA.Title : nodeB.Title;
                System.Diagnostics.Debug.WriteLine($"      节点类型决定: {winner} 优先 ({nodeA.NodeType} vs {nodeB.NodeType})");
                return aTypePriority.CompareTo(bTypePriority);
            }

            // 3. 按连接数量排序（连接多的在上方）
            var aConnectionCount = GetNodeConnectionCount(nodeA, connections);
            var bConnectionCount = GetNodeConnectionCount(nodeB, connections);
            if (aConnectionCount != bConnectionCount)
            {
                var winner = aConnectionCount > bConnectionCount ? nodeA.Title : nodeB.Title;
                System.Diagnostics.Debug.WriteLine($"      连接数量决定: {winner} 优先 ({aConnectionCount} vs {bConnectionCount})");
                return bConnectionCount.CompareTo(aConnectionCount); // 降序，连接多的在前
            }

            // 4. 按节点标题进行稳定排序
            var titleOrder = string.Compare(nodeA.Title, nodeB.Title, StringComparison.Ordinal);
            var winner2 = titleOrder < 0 ? nodeA.Title : nodeB.Title;
            System.Diagnostics.Debug.WriteLine($"      标题排序决定: {winner2} 优先");
            return titleOrder;
        }

        /// <summary>
        /// 基于连接关系确定节点顺序
        /// </summary>
        /// <param name="nodeA">节点A</param>
        /// <param name="nodeB">节点B</param>
        /// <param name="connections">连接关系</param>
        /// <param name="layers">所有层级</param>
        /// <param name="currentLayerIndex">当前层索引</param>
        /// <returns>排序结果：-1表示A在B前，1表示B在A前，0表示无直接关系</returns>
        private int GetConnectionBasedOrder(ModuleNodeViewModel nodeA, ModuleNodeViewModel nodeB,
            List<ConnectionViewModel> connections, List<List<ModuleNodeViewModel>> layers, int currentLayerIndex)
        {
            System.Diagnostics.Debug.WriteLine($"      分析连接关系: {nodeA.Title} vs {nodeB.Title}");

            // 检查前一层的连接影响
            if (currentLayerIndex > 0)
            {
                var previousLayer = layers[currentLayerIndex - 1];
                var aSourceOrder = GetMinSourceNodeOrder(nodeA, previousLayer, connections);
                var bSourceOrder = GetMinSourceNodeOrder(nodeB, previousLayer, connections);

                System.Diagnostics.Debug.WriteLine($"        前层连接: {nodeA.Title}最小源序号={aSourceOrder}, {nodeB.Title}最小源序号={bSourceOrder}");

                if (aSourceOrder != bSourceOrder)
                {
                    var result = aSourceOrder.CompareTo(bSourceOrder);
                    System.Diagnostics.Debug.WriteLine($"        前层连接决定排序: {(result < 0 ? nodeA.Title : nodeB.Title)} 优先");
                    return result;
                }
            }

            // 检查后一层的连接影响
            if (currentLayerIndex < layers.Count - 1)
            {
                var nextLayer = layers[currentLayerIndex + 1];
                var aTargetOrder = GetMinTargetNodeOrder(nodeA, nextLayer, connections);
                var bTargetOrder = GetMinTargetNodeOrder(nodeB, nextLayer, connections);

                System.Diagnostics.Debug.WriteLine($"        后层连接: {nodeA.Title}最小目标序号={aTargetOrder}, {nodeB.Title}最小目标序号={bTargetOrder}");

                if (aTargetOrder != bTargetOrder)
                {
                    var result = aTargetOrder.CompareTo(bTargetOrder);
                    System.Diagnostics.Debug.WriteLine($"        后层连接决定排序: {(result < 0 ? nodeA.Title : nodeB.Title)} 优先");
                    return result;
                }
            }

            System.Diagnostics.Debug.WriteLine($"        无连接关系影响排序");
            return 0; // 无直接连接关系影响
        }

        /// <summary>
        /// 获取节点在前一层中最小源节点的顺序
        /// </summary>
        /// <param name="node">目标节点</param>
        /// <param name="previousLayer">前一层节点</param>
        /// <param name="connections">连接关系</param>
        /// <returns>最小源节点顺序，如果没有连接则返回int.MaxValue</returns>
        private int GetMinSourceNodeOrder(ModuleNodeViewModel node, List<ModuleNodeViewModel> previousLayer, List<ConnectionViewModel> connections)
        {
            var minOrder = int.MaxValue;
            var sourceNodes = new List<string>();

            for (int i = 0; i < previousLayer.Count; i++)
            {
                var sourceNode = previousLayer[i];
                // 检查是否有从sourceNode到node的连接
                if (connections.Any(c => c.Source?.Node == sourceNode && c.Target?.Node == node))
                {
                    minOrder = Math.Min(minOrder, i);
                    sourceNodes.Add($"{sourceNode.Title}(位置{i})");
                }
            }

            if (sourceNodes.Any())
            {
                System.Diagnostics.Debug.WriteLine($"          {node.Title} 的前层源节点: [{string.Join(", ", sourceNodes)}], 最小序号: {minOrder}");
            }

            return minOrder;
        }

        /// <summary>
        /// 获取节点在后一层中最小目标节点的顺序
        /// </summary>
        /// <param name="node">源节点</param>
        /// <param name="nextLayer">后一层节点</param>
        /// <param name="connections">连接关系</param>
        /// <returns>最小目标节点顺序，如果没有连接则返回int.MaxValue</returns>
        private int GetMinTargetNodeOrder(ModuleNodeViewModel node, List<ModuleNodeViewModel> nextLayer, List<ConnectionViewModel> connections)
        {
            var minOrder = int.MaxValue;
            var targetNodes = new List<string>();

            for (int i = 0; i < nextLayer.Count; i++)
            {
                var targetNode = nextLayer[i];
                // 检查是否有从node到targetNode的连接
                if (connections.Any(c => c.Source?.Node == node && c.Target?.Node == targetNode))
                {
                    minOrder = Math.Min(minOrder, i);
                    targetNodes.Add($"{targetNode.Title}(位置{i})");
                }
            }

            if (targetNodes.Any())
            {
                System.Diagnostics.Debug.WriteLine($"          {node.Title} 的后层目标节点: [{string.Join(", ", targetNodes)}], 最小序号: {minOrder}");
            }

            return minOrder;
        }

        /// <summary>
        /// 获取节点的连接数量（输入+输出）
        /// </summary>
        /// <param name="node">节点</param>
        /// <param name="connections">连接关系</param>
        /// <returns>连接总数</returns>
        private int GetNodeConnectionCount(ModuleNodeViewModel node, List<ConnectionViewModel> connections)
        {
            var inputCount = connections.Count(c => c.Target?.Node == node);
            var outputCount = connections.Count(c => c.Source?.Node == node);
            return inputCount + outputCount;
        }

        /// <summary>
        /// 获取节点类型的布局优先级
        /// </summary>
        private int GetNodeTypePriority(NodeType nodeType)
        {
            return nodeType switch
            {
                NodeType.Input => 1,
                NodeType.Transform => 2,
                NodeType.Control => 3,
                NodeType.Output => 4,
                _ => 5
            };
        }
    }
}
