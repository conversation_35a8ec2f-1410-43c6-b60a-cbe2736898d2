<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 蓝色主题流动画刷 -->
    <LinearGradientBrush x:Key="BlueFlowGradientBrush"
                         StartPoint="0,0.5"
                         EndPoint="1,0.5">
        <GradientStop Color="Transparent"
                Offset="0"/>
        <GradientStop Color="Transparent"
                Offset="0.2"/>
        <GradientStop Color="#802196F3"
                Offset="0.4"/>
        <GradientStop Color="#FF2196F3"
                Offset="0.5"/>
        <GradientStop Color="#802196F3"
                Offset="0.6"/>
        <GradientStop Color="Transparent"
                Offset="0.8"/>
        <GradientStop Color="Transparent"
                Offset="1"/>
        <LinearGradientBrush.Transform>
            <TranslateTransform X="-200"/>
        </LinearGradientBrush.Transform>
    </LinearGradientBrush>

    <!-- 绿色主题流动画刷 -->
    <LinearGradientBrush x:Key="GreenFlowGradientBrush"
                         StartPoint="0,0"
                         EndPoint="1,0">
        <GradientStop Color="Transparent"
                      Offset="0"/>
        <GradientStop Color="#804CAF50"
                      Offset="0.3"/>
        <GradientStop Color="#FF4CAF50"
                      Offset="0.5"/>
        <GradientStop Color="#804CAF50"
                      Offset="0.7"/>
        <GradientStop Color="Transparent"
                      Offset="1"/>
        <LinearGradientBrush.Transform>
            <TranslateTransform X="-100"/>
        </LinearGradientBrush.Transform>
    </LinearGradientBrush>

    <!-- 慢速流动动画 -->
    <Storyboard x:Key="SlowFlowAnimation"
                RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(LinearGradientBrush.Transform).(TranslateTransform.X)"
                         From="-100"
                         To="200"
                         Duration="0:0:4">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 正常流动动画 -->
    <Storyboard x:Key="NormalFlowAnimation"
                RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(LinearGradientBrush.Transform).(TranslateTransform.X)"
                         From="-100"
                         To="200"
                         Duration="0:0:2">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 快速流动动画 -->
    <Storyboard x:Key="FastFlowAnimation"
                RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="(LinearGradientBrush.Transform).(TranslateTransform.X)"
                         From="-100"
                         To="200"
                         Duration="0:0:1">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 脉冲动画 -->
    <Storyboard x:Key="PulseFlowAnimation"
                RepeatBehavior="Forever">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0.3"
                         To="1.0"
                         Duration="0:0:0.8"
                         AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 流动光效连接线样式 -->
    <Style x:Key="FlowConnectionStyle"
           TargetType="Path">
        <Setter Property="StrokeThickness"
                Value="3"/>
        <Setter Property="Opacity"
                Value="0.8"/>
        <Setter Property="IsHitTestVisible"
                Value="False"/>
        <Style.Triggers>
            <!-- 慢速流动状态 -->
            <DataTrigger Binding="{Binding FlowState}"
                         Value="Slow">
                <Setter Property="Stroke"
                        Value="{StaticResource BlueFlowGradientBrush}"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource SlowFlowAnimation}"/>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 正常流动状态 -->
            <DataTrigger Binding="{Binding FlowState}"
                         Value="Normal">
                <Setter Property="Stroke"
                        Value="{StaticResource BlueFlowGradientBrush}"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource NormalFlowAnimation}"/>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 快速流动状态 -->
            <DataTrigger Binding="{Binding FlowState}"
                         Value="Fast">
                <Setter Property="Stroke"
                        Value="{StaticResource GreenFlowGradientBrush}"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FastFlowAnimation}"/>
                </DataTrigger.EnterActions>
            </DataTrigger>

            <!-- 脉冲状态 -->
            <DataTrigger Binding="{Binding FlowState}"
                         Value="Pulse">
                <Setter Property="Stroke"
                        Value="{StaticResource BlueFlowGradientBrush}"/>
                <DataTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PulseFlowAnimation}"/>
                </DataTrigger.EnterActions>
            </DataTrigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>
