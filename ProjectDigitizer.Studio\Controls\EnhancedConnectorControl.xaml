<UserControl x:Class="ProjectDigitizer.Studio.Controls.EnhancedConnectorControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:ProjectDigitizer.Studio.Controls"
             xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
             xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
             mc:Ignorable="d"
             d:DesignHeight="18"
             d:DesignWidth="18"
             d:DataContext="{d:DesignInstance Type=viewmodels:ConnectorViewModel}">

    <UserControl.Resources>
        <converters:ConnectorDataTypeToColorConverter x:Key="DataTypeToColorConverter"/>
        <converters:ConnectorDataTypeToColorSimpleConverter x:Key="DataTypeToColorSimpleConverter"/>
    </UserControl.Resources>

    <Grid Width="18"
          Height="18"
          Cursor="Hand">
        <!-- 发光效果层 -->
        <Ellipse x:Name="GlowEffect"
                 Width="18"
                 Height="18"
                 Opacity="0"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.Fill>
                <SolidColorBrush>
                    <SolidColorBrush.Color>
                        <MultiBinding Converter="{StaticResource DataTypeToColorConverter}">
                            <Binding Path="ConnectorDataType"/>
                            <Binding Path="IsInput"/>
                        </MultiBinding>
                    </SolidColorBrush.Color>
                </SolidColorBrush>
            </Ellipse.Fill>
            <Ellipse.Effect>
                <DropShadowEffect BlurRadius="8"
                                  ShadowDepth="0"
                                  Opacity="0.6">
                    <DropShadowEffect.Color>
                        <MultiBinding Converter="{StaticResource DataTypeToColorConverter}">
                            <Binding Path="ConnectorDataType"/>
                            <Binding Path="IsInput"/>
                        </MultiBinding>
                    </DropShadowEffect.Color>
                </DropShadowEffect>
            </Ellipse.Effect>
        </Ellipse>

        <!-- 主连接器 -->
        <Ellipse x:Name="MainConnector"
                 Width="14"
                 Height="14"
                 Stroke="White"
                 StrokeThickness="2"
                 Opacity="0.8"
                 RenderTransformOrigin="0.5,0.5">
            <Ellipse.Fill>
                <SolidColorBrush>
                    <SolidColorBrush.Color>
                        <MultiBinding Converter="{StaticResource DataTypeToColorConverter}">
                            <Binding Path="ConnectorDataType"/>
                            <Binding Path="IsInput"/>
                        </MultiBinding>
                    </SolidColorBrush.Color>
                </SolidColorBrush>
            </Ellipse.Fill>
            <Ellipse.RenderTransform>
                <ScaleTransform ScaleX="1"
                                ScaleY="1"/>
            </Ellipse.RenderTransform>
        </Ellipse>

        <!-- 连接状态指示器 -->
        <Ellipse x:Name="ConnectionIndicator"
                 Width="4"
                 Height="4"
                 Fill="White"
                 Opacity="0"
                 RenderTransformOrigin="0.5,0.5"/>

        <!-- 触发器 -->
        <Grid.Triggers>
            <!-- 鼠标悬停状态 -->
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="(RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1.2"
                                         Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="(RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1.2"
                                         Duration="0:0:0.2">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetName="GlowEffect"
                                         Storyboard.TargetProperty="Opacity"
                                         To="1.0"
                                         Duration="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="Opacity"
                                         To="1.0"
                                         Duration="0:0:0.2"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

            <!-- 鼠标离开状态 -->
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="(RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1.0"
                                         Duration="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="(RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1.0"
                                         Duration="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetName="GlowEffect"
                                         Storyboard.TargetProperty="Opacity"
                                         To="0.0"
                                         Duration="0:0:0.2"/>
                        <DoubleAnimation Storyboard.TargetName="MainConnector"
                                         Storyboard.TargetProperty="Opacity"
                                         To="0.8"
                                         Duration="0:0:0.2"/>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>

            <!-- 已连接状态 -->
            <DataTrigger Binding="{Binding IsConnected}"
                         Value="True">
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="ConnectionIndicator"
                                             Storyboard.TargetProperty="Opacity"
                                             To="1.0"
                                             Duration="0:0:0.3">
                                <DoubleAnimation.EasingFunction>
                                    <ElasticEase EasingMode="EaseOut"
                                                 Oscillations="2"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetName="MainConnector"
                                             Storyboard.TargetProperty="Opacity"
                                             To="1.0"
                                             Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
                <DataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="ConnectionIndicator"
                                             Storyboard.TargetProperty="Opacity"
                                             To="0.0"
                                             Duration="0:0:0.2"/>
                            <DoubleAnimation Storyboard.TargetName="MainConnector"
                                             Storyboard.TargetProperty="Opacity"
                                             To="0.8"
                                             Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.ExitActions>
            </DataTrigger>

            <!-- 兼容性高亮状态 -->
            <DataTrigger Binding="{Binding IsHighlighted}"
                         Value="True">
                <DataTrigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard RepeatBehavior="Forever">
                            <DoubleAnimation Storyboard.TargetName="MainConnector"
                                             Storyboard.TargetProperty="Opacity"
                                             From="0.6"
                                             To="1.0"
                                             Duration="0:0:1"
                                             AutoReverse="True">
                                <DoubleAnimation.EasingFunction>
                                    <SineEase EasingMode="EaseInOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.EnterActions>
                <DataTrigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetName="MainConnector"
                                             Storyboard.TargetProperty="Opacity"
                                             To="0.8"
                                             Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </DataTrigger.ExitActions>
            </DataTrigger>
        </Grid.Triggers>
    </Grid>
</UserControl>
