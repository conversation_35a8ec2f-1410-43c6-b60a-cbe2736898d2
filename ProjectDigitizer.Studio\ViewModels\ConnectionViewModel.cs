using System;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 连接线视图模型
    /// </summary>
    public class ConnectionViewModel : ViewModelBase
    {
        private ConnectorViewModel? _source;
        private ConnectorViewModel? _target;
        private string _id;
        private bool _isEnabled = true;
        private int _connectionIndex = 1;
        private string _connectionColor = "#2196F3";
        private bool _isFieldReferenced = false;
        private bool _isFlowAnimationEnabled = false;
        private double _flowAnimationSpeed = 1.0;
        private ConnectionFlowState _flowState = ConnectionFlowState.None;

        /// <summary>
        /// 源连接器
        /// </summary>
        public ConnectorViewModel? Source
        {
            get => _source;
            set
            {
                if (_source != value)
                {
                    if (_source != null)
                    {
                        _source.RemoveConnection(this);
                    }

                    _source = value;

                    if (_source != null)
                    {
                        _source.AddConnection(this);
                    }

                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 目标连接器
        /// </summary>
        public ConnectorViewModel? Target
        {
            get => _target;
            set
            {
                if (_target != value)
                {
                    if (_target != null)
                    {
                        _target.RemoveConnection(this);
                    }

                    _target = value;

                    if (_target != null)
                    {
                        _target.AddConnection(this);
                    }

                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// 连接线唯一标识
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 连接线是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (SetProperty(ref _isEnabled, value))
                {
                    // 触发启用状态变化事件
                    EnabledChanged?.Invoke(this, value);
                    System.Diagnostics.Debug.WriteLine($"连接线 {GetConnectionDescription()} 启用状态变更为: {value}");
                }
            }
        }

        /// <summary>
        /// 连接序号（在多连接中的索引）
        /// </summary>
        public int ConnectionIndex
        {
            get => _connectionIndex;
            set
            {
                if (SetProperty(ref _connectionIndex, value))
                {
                    // 根据连接序号生成颜色
                    ConnectionColor = GenerateConnectionColor(value);
                }
            }
        }

        /// <summary>
        /// 连接线颜色
        /// </summary>
        public string ConnectionColor
        {
            get => _connectionColor;
            set => SetProperty(ref _connectionColor, value);
        }

        /// <summary>
        /// 字段是否被表达式引用（用于控制连接线样式）
        /// </summary>
        public bool IsFieldReferenced
        {
            get => _isFieldReferenced;
            set => SetProperty(ref _isFieldReferenced, value);
        }

        /// <summary>
        /// 是否启用流动光效动画
        /// </summary>
        public bool IsFlowAnimationEnabled
        {
            get => _isFlowAnimationEnabled;
            set => SetProperty(ref _isFlowAnimationEnabled, value);
        }

        /// <summary>
        /// 流动动画速度（1.0为正常速度）
        /// </summary>
        public double FlowAnimationSpeed
        {
            get => _flowAnimationSpeed;
            set => SetProperty(ref _flowAnimationSpeed, Math.Max(0.1, Math.Min(5.0, value)));
        }

        /// <summary>
        /// 连接线流动状态
        /// </summary>
        public ConnectionFlowState FlowState
        {
            get => _flowState;
            set => SetProperty(ref _flowState, value);
        }

        /// <summary>
        /// 连接线启用状态变化事件
        /// </summary>
        public event EventHandler<bool>? EnabledChanged;

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public ConnectionViewModel()
        {
            _id = Guid.NewGuid().ToString();
        }

        /// <summary>
        /// 带源和目标的构造函数
        /// </summary>
        public ConnectionViewModel(ConnectorViewModel source, ConnectorViewModel target)
        {
            _id = Guid.NewGuid().ToString();

            // 使用属性设置器来触发连接器的AddConnection方法
            Source = source;
            Target = target;
        }

        /// <summary>
        /// 获取连接描述信息
        /// </summary>
        /// <returns>连接描述字符串</returns>
        public string GetConnectionDescription()
        {
            var sourceNodeName = (Source?.Node as ModuleNodeViewModel)?.Title ?? "未知节点";
            var targetNodeName = (Target?.Node as ModuleNodeViewModel)?.Title ?? "未知节点";
            return $"{sourceNodeName} → {targetNodeName}";
        }

        /// <summary>
        /// 检查连接是否应该根据节点状态自动禁用
        /// </summary>
        /// <returns>如果应该禁用则返回true</returns>
        public bool ShouldBeDisabledBasedOnNodes()
        {
            var sourceNode = Source?.Node as ModuleNodeViewModel;
            var targetNode = Target?.Node as ModuleNodeViewModel;

            // 如果源节点或目标节点被禁用，连接线应该被禁用
            return (sourceNode != null && !sourceNode.IsEnabled) ||
                   (targetNode != null && !targetNode.IsEnabled);
        }

        /// <summary>
        /// 检查连接是否可以根据节点状态自动启用
        /// </summary>
        /// <returns>如果可以启用则返回true</returns>
        public bool CanBeEnabledBasedOnNodes()
        {
            var sourceNode = Source?.Node as ModuleNodeViewModel;
            var targetNode = Target?.Node as ModuleNodeViewModel;

            // 只有当源节点和目标节点都启用时，连接线才能启用
            return (sourceNode == null || sourceNode.IsEnabled) &&
                   (targetNode == null || targetNode.IsEnabled);
        }

        /// <summary>
        /// 根据连接序号生成颜色
        /// </summary>
        /// <param name="index">连接序号</param>
        /// <returns>十六进制颜色字符串</returns>
        private string GenerateConnectionColor(int index)
        {
            // 预定义的颜色数组，确保视觉区分度
            var colors = new[]
            {
                "#2196F3", // 蓝色 - 默认
                "#4CAF50", // 绿色
                "#FF9800", // 橙色
                "#9C27B0", // 紫色
                "#F44336", // 红色
                "#00BCD4", // 青色
                "#FFEB3B", // 黄色
                "#795548", // 棕色
                "#607D8B", // 蓝灰色
                "#E91E63"  // 粉色
            };

            // 使用模运算循环使用颜色
            return colors[(index - 1) % colors.Length];
        }
    }

    /// <summary>
    /// 连接线流动状态枚举
    /// </summary>
    public enum ConnectionFlowState
    {
        /// <summary>无流动效果</summary>
        None,
        /// <summary>慢速流动（字段可用但未引用）</summary>
        Slow,
        /// <summary>正常流动（字段被引用）</summary>
        Normal,
        /// <summary>快速流动（字段活跃使用）</summary>
        Fast,
        /// <summary>脉冲效果（特殊状态）</summary>
        Pulse
    }
}
