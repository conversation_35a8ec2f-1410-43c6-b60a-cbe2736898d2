using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 连接器兼容性服务
    /// 负责管理连接器之间的兼容性检查和高亮显示
    /// </summary>
    public class ConnectorCompatibilityService
    {
        private readonly Dictionary<ConnectorDataType, List<ConnectorDataType>> _compatibilityMatrix;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ConnectorCompatibilityService()
        {
            _compatibilityMatrix = InitializeCompatibilityMatrix();
        }

        /// <summary>
        /// 检查两个连接器是否兼容
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        /// <returns>是否兼容</returns>
        public bool AreCompatible(ConnectorViewModel source, ConnectorViewModel target)
        {
            if (source == null || target == null) return false;

            // 基本规则检查
            if (!source.CanConnectTo(target)) return false;

            // 数据类型兼容性检查
            return IsDataTypeCompatible(source.ConnectorDataType, target.ConnectorDataType);
        }

        /// <summary>
        /// 检查数据类型是否兼容
        /// </summary>
        /// <param name="sourceType">源数据类型</param>
        /// <param name="targetType">目标数据类型</param>
        /// <returns>是否兼容</returns>
        public bool IsDataTypeCompatible(ConnectorDataType sourceType, ConnectorDataType targetType)
        {
            // Any类型可以连接任何类型
            if (sourceType == ConnectorDataType.Any || targetType == ConnectorDataType.Any)
                return true;

            // 检查兼容性矩阵
            if (_compatibilityMatrix.TryGetValue(sourceType, out var compatibleTypes))
            {
                return compatibleTypes.Contains(targetType);
            }

            return false;
        }

        /// <summary>
        /// 获取与指定连接器兼容的所有连接器
        /// </summary>
        /// <param name="connector">指定连接器</param>
        /// <param name="allConnectors">所有连接器列表</param>
        /// <returns>兼容的连接器列表</returns>
        public IEnumerable<ConnectorViewModel> GetCompatibleConnectors(
            ConnectorViewModel connector, 
            IEnumerable<ConnectorViewModel> allConnectors)
        {
            if (connector == null || allConnectors == null) 
                return Enumerable.Empty<ConnectorViewModel>();

            return allConnectors.Where(c => AreCompatible(connector, c));
        }

        /// <summary>
        /// 高亮显示兼容的连接器
        /// </summary>
        /// <param name="sourceConnector">源连接器</param>
        /// <param name="allConnectors">所有连接器列表</param>
        public void HighlightCompatibleConnectors(
            ConnectorViewModel sourceConnector, 
            IEnumerable<ConnectorViewModel> allConnectors)
        {
            if (sourceConnector == null || allConnectors == null) return;

            foreach (var connector in allConnectors)
            {
                if (connector == sourceConnector) continue;

                var isCompatible = AreCompatible(sourceConnector, connector);
                connector.IsCompatible = isCompatible;
                connector.IsHighlighted = isCompatible;
            }
        }

        /// <summary>
        /// 清除所有连接器的高亮状态
        /// </summary>
        /// <param name="allConnectors">所有连接器列表</param>
        public void ClearHighlights(IEnumerable<ConnectorViewModel> allConnectors)
        {
            if (allConnectors == null) return;

            foreach (var connector in allConnectors)
            {
                connector.IsCompatible = true;
                connector.IsHighlighted = false;
            }
        }

        /// <summary>
        /// 获取连接器的兼容性评分
        /// </summary>
        /// <param name="source">源连接器</param>
        /// <param name="target">目标连接器</param>
        /// <returns>兼容性评分 (0-100)</returns>
        public int GetCompatibilityScore(ConnectorViewModel source, ConnectorViewModel target)
        {
            if (!AreCompatible(source, target)) return 0;

            // 完全匹配的类型得分最高
            if (source.ConnectorDataType == target.ConnectorDataType) return 100;

            // Any类型的兼容性得分较低
            if (source.ConnectorDataType == ConnectorDataType.Any || 
                target.ConnectorDataType == ConnectorDataType.Any) return 60;

            // 其他兼容类型得分中等
            return 80;
        }

        /// <summary>
        /// 初始化兼容性矩阵
        /// </summary>
        /// <returns>兼容性矩阵</returns>
        private Dictionary<ConnectorDataType, List<ConnectorDataType>> InitializeCompatibilityMatrix()
        {
            return new Dictionary<ConnectorDataType, List<ConnectorDataType>>
            {
                {
                    ConnectorDataType.Any,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Number,
                        ConnectorDataType.Text,
                        ConnectorDataType.Boolean,
                        ConnectorDataType.File,
                        ConnectorDataType.Geometry,
                        ConnectorDataType.Control
                    }
                },
                {
                    ConnectorDataType.Number,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Number,
                        ConnectorDataType.Text,  // 数值可以转换为文本
                        ConnectorDataType.Boolean  // 数值可以转换为布尔值
                    }
                },
                {
                    ConnectorDataType.Text,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Text,
                        ConnectorDataType.Number,  // 文本可以尝试转换为数值
                        ConnectorDataType.File  // 文本可以作为文件路径
                    }
                },
                {
                    ConnectorDataType.Boolean,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Boolean,
                        ConnectorDataType.Number,  // 布尔值可以转换为数值
                        ConnectorDataType.Text,  // 布尔值可以转换为文本
                        ConnectorDataType.Control  // 布尔值可以用于控制流
                    }
                },
                {
                    ConnectorDataType.File,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.File,
                        ConnectorDataType.Text,  // 文件路径是文本
                        ConnectorDataType.Geometry  // 文件可能包含几何数据
                    }
                },
                {
                    ConnectorDataType.Geometry,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Geometry,
                        ConnectorDataType.File  // 几何数据可以保存到文件
                    }
                },
                {
                    ConnectorDataType.Control,
                    new List<ConnectorDataType>
                    {
                        ConnectorDataType.Any,
                        ConnectorDataType.Control,
                        ConnectorDataType.Boolean  // 控制流可以基于布尔值
                    }
                }
            };
        }

        /// <summary>
        /// 获取数据类型的描述信息
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>描述信息</returns>
        public string GetDataTypeDescription(ConnectorDataType dataType)
        {
            return dataType switch
            {
                ConnectorDataType.Any => "通用类型 - 可连接任何数据类型",
                ConnectorDataType.Number => "数值类型 - 整数、小数等数值数据",
                ConnectorDataType.Text => "文本类型 - 字符串、文本数据",
                ConnectorDataType.Boolean => "布尔类型 - 真/假逻辑值",
                ConnectorDataType.File => "文件类型 - 文件路径或文件数据",
                ConnectorDataType.Geometry => "几何类型 - 几何图形、坐标数据",
                ConnectorDataType.Control => "控制流 - 程序执行控制信号",
                _ => "未知类型"
            };
        }

        /// <summary>
        /// 获取兼容的数据类型列表
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>兼容的数据类型列表</returns>
        public IEnumerable<ConnectorDataType> GetCompatibleDataTypes(ConnectorDataType dataType)
        {
            if (_compatibilityMatrix.TryGetValue(dataType, out var compatibleTypes))
            {
                return compatibleTypes;
            }

            return new[] { dataType };
        }
    }
}
