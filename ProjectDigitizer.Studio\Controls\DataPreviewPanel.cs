using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 数据预览面板控件
    /// 用于显示筛选后的数据，支持分页、搜索和导出
    /// </summary>
    public class DataPreviewPanel : UserControl
    {
        private readonly DataPreviewViewModel _viewModel;
        private readonly Grid _mainGrid;
        private TextBox _searchBox = null!;
        private DataGrid _dataGrid = null!;
        private StackPanel _paginationPanel = null!;
        private TextBlock _statusText = null!;

        public DataPreviewPanel()
        {
            _viewModel = new DataPreviewViewModel();
            DataContext = _viewModel;

            // 创建主网格
            _mainGrid = new Grid();
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 搜索栏
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // 数据表格
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 分页栏
            _mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 状态栏

            CreateSearchBar();
            CreateDataGrid();
            CreatePaginationBar();
            CreateStatusBar();

            Content = _mainGrid;

            // 设置默认样式
            Background = Brushes.White;
            BorderBrush = Brushes.LightGray;
            BorderThickness = new Thickness(1, 1, 1, 1);
        }

        /// <summary>
        /// 设置预览数据
        /// </summary>
        public void SetData(IEnumerable<Dictionary<string, object?>> data)
        {
            _viewModel.SetData(data);
            UpdateDataGridColumns();
        }

        /// <summary>
        /// 清空数据
        /// </summary>
        public void ClearData()
        {
            _viewModel.ClearData();
            _dataGrid.Columns.Clear();
        }

        #region 私有方法

        /// <summary>
        /// 创建搜索栏
        /// </summary>
        private void CreateSearchBar()
        {
            var searchPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(8, 8, 8, 4),
                Height = 32
            };

            var searchLabel = new TextBlock
            {
                Text = "搜索:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            _searchBox = new TextBox
            {
                Width = 200,
                Height = 24,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            // 绑定搜索文本
            var binding = new Binding("SearchText")
            {
                Source = _viewModel,
                UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged
            };
            _searchBox.SetBinding(TextBox.TextProperty, binding);

            var refreshButton = new Button
            {
                Content = "刷新",
                Width = 60,
                Height = 24,
                VerticalAlignment = VerticalAlignment.Center,
                Command = _viewModel.RefreshCommand
            };

            var exportButton = new Button
            {
                Content = "导出",
                Width = 60,
                Height = 24,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(8, 0, 0, 0),
                Command = _viewModel.ExportCommand
            };

            searchPanel.Children.Add(searchLabel);
            searchPanel.Children.Add(_searchBox);
            searchPanel.Children.Add(refreshButton);
            searchPanel.Children.Add(exportButton);

            Grid.SetRow(searchPanel, 0);
            _mainGrid.Children.Add(searchPanel);
        }

        /// <summary>
        /// 创建数据表格
        /// </summary>
        private void CreateDataGrid()
        {
            _dataGrid = new DataGrid
            {
                Margin = new Thickness(8, 4, 8, 4),
                AutoGenerateColumns = false,
                IsReadOnly = true,
                CanUserAddRows = false,
                CanUserDeleteRows = false,
                CanUserReorderColumns = true,
                CanUserResizeColumns = true,
                CanUserSortColumns = true,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                SelectionMode = DataGridSelectionMode.Extended,
                AlternatingRowBackground = new SolidColorBrush(Color.FromArgb(20, 0, 0, 0))
            };

            // 绑定数据源
            var itemsBinding = new Binding("DisplayedRows")
            {
                Source = _viewModel
            };
            _dataGrid.SetBinding(DataGrid.ItemsSourceProperty, itemsBinding);

            Grid.SetRow(_dataGrid, 1);
            _mainGrid.Children.Add(_dataGrid);
        }

        /// <summary>
        /// 创建分页栏
        /// </summary>
        private void CreatePaginationBar()
        {
            _paginationPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(8, 4, 8, 4)
            };

            // 首页按钮
            var firstButton = new Button
            {
                Content = "首页",
                Width = 50,
                Height = 24,
                Margin = new Thickness(2, 0, 2, 0),
                Command = _viewModel.FirstPageCommand
            };

            // 上一页按钮
            var prevButton = new Button
            {
                Content = "上一页",
                Width = 60,
                Height = 24,
                Margin = new Thickness(2, 0, 2, 0),
                Command = _viewModel.PreviousPageCommand
            };

            // 页码信息
            var pageInfo = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(8, 0, 8, 0)
            };

            var pageBinding = new Binding("CurrentPage")
            {
                Source = _viewModel,
                StringFormat = "第 {0} 页"
            };
            pageInfo.SetBinding(TextBlock.TextProperty, pageBinding);

            var totalPagesInfo = new TextBlock
            {
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var totalPagesBinding = new Binding("TotalPages")
            {
                Source = _viewModel,
                StringFormat = "共 {0} 页"
            };
            totalPagesInfo.SetBinding(TextBlock.TextProperty, totalPagesBinding);

            // 下一页按钮
            var nextButton = new Button
            {
                Content = "下一页",
                Width = 60,
                Height = 24,
                Margin = new Thickness(2, 0, 2, 0),
                Command = _viewModel.NextPageCommand
            };

            // 末页按钮
            var lastButton = new Button
            {
                Content = "末页",
                Width = 50,
                Height = 24,
                Margin = new Thickness(2, 0, 2, 0),
                Command = _viewModel.LastPageCommand
            };

            // 页面大小选择
            var pageSizeLabel = new TextBlock
            {
                Text = "每页:",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(16, 0, 4, 0)
            };

            var pageSizeCombo = new ComboBox
            {
                Width = 60,
                Height = 24,
                VerticalAlignment = VerticalAlignment.Center
            };

            pageSizeCombo.Items.Add(25);
            pageSizeCombo.Items.Add(50);
            pageSizeCombo.Items.Add(100);
            pageSizeCombo.Items.Add(200);
            pageSizeCombo.SelectedItem = 50;

            var pageSizeBinding = new Binding("PageSize")
            {
                Source = _viewModel
            };
            pageSizeCombo.SetBinding(ComboBox.SelectedItemProperty, pageSizeBinding);

            _paginationPanel.Children.Add(firstButton);
            _paginationPanel.Children.Add(prevButton);
            _paginationPanel.Children.Add(pageInfo);
            _paginationPanel.Children.Add(totalPagesInfo);
            _paginationPanel.Children.Add(nextButton);
            _paginationPanel.Children.Add(lastButton);
            _paginationPanel.Children.Add(pageSizeLabel);
            _paginationPanel.Children.Add(pageSizeCombo);

            Grid.SetRow(_paginationPanel, 2);
            _mainGrid.Children.Add(_paginationPanel);
        }

        /// <summary>
        /// 创建状态栏
        /// </summary>
        private void CreateStatusBar()
        {
            _statusText = new TextBlock
            {
                Margin = new Thickness(8, 4, 8, 8),
                FontSize = 11,
                Foreground = Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Left
            };

            var statusBinding = new Binding("StatusText")
            {
                Source = _viewModel
            };
            _statusText.SetBinding(TextBlock.TextProperty, statusBinding);

            Grid.SetRow(_statusText, 3);
            _mainGrid.Children.Add(_statusText);
        }

        /// <summary>
        /// 更新数据表格列
        /// </summary>
        private void UpdateDataGridColumns()
        {
            _dataGrid.Columns.Clear();

            foreach (var columnName in _viewModel.ColumnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new Binding($"Data[{columnName}]"),
                    Width = DataGridLength.Auto,
                    MinWidth = 80
                };

                _dataGrid.Columns.Add(column);
            }
        }

        #endregion

        /// <summary>
        /// 获取视图模型
        /// </summary>
        public DataPreviewViewModel ViewModel => _viewModel;
    }
}
