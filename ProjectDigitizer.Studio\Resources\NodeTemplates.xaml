<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:nodify="https://miroiu.github.io/nodify"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                    xmlns:viewmodels="clr-namespace:ProjectDigitizer.Studio.ViewModels"
                    xmlns:converters="clr-namespace:ProjectDigitizer.Studio.Converters"
                    xmlns:models="clr-namespace:ProjectDigitizer.Studio.Models">

        <!-- 转换器定义 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:BooleanToAngleConverter x:Key="BooleanToAngleConverter"/>
        <converters:ConnectorDataTypeToColorConverter x:Key="ConnectorDataTypeToColorConverter"/>
        <converters:ConnectorDataTypeToColorSimpleConverter x:Key="ConnectorDataTypeToColorSimpleConverter"/>

        <!-- 现代化样式资源 -->

        <!-- 阴影效果 -->
        <DropShadowEffect x:Key="ModernDropShadowEffect"
                          ShadowDepth="4"
                          BlurRadius="8"
                          Color="#888"
                          Opacity="0.3"/>

        <!-- 端口光晕 -->
        <BlurEffect x:Key="PortGlowEffect"
                    Radius="4"/>

        <!-- 平面按钮样式 -->
        <Style x:Key="FlatButtonStyle"
               TargetType="Button">
                <Setter Property="Background"
                        Value="Transparent"/>
                <Setter Property="BorderBrush"
                        Value="Transparent"/>
                <Setter Property="Foreground"
                        Value="White"/>
                <Setter Property="Padding"
                        Value="0"/>
                <Setter Property="Cursor"
                        Value="Hand"/>
        </Style>


        <!-- 模块类型到图标的转换器 -->
        <converters:ModuleTypeToIconConverter x:Key="ModuleTypeToIconConverter"/>



        <!-- 现代化渐变画刷 -->
        <LinearGradientBrush x:Key="ModernHeaderGradientBlue"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#2196F3"
                              Offset="0"/>
                <GradientStop Color="#1976D2"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientGreen"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#4CAF50"
                              Offset="0"/>
                <GradientStop Color="#388E3C"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientOrange"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#FF9800"
                              Offset="0"/>
                <GradientStop Color="#F57C00"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientPurple"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#9C27B0"
                              Offset="0"/>
                <GradientStop Color="#7B1FA2"
                              Offset="1"/>
        </LinearGradientBrush>

        <!-- 示例图片样式的深色渐变 -->
        <LinearGradientBrush x:Key="ModernHeaderGradientGray"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#616161"
                              Offset="0"/>
                <GradientStop Color="#424242"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientDarkPurple"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#7B1FA2"
                              Offset="0"/>
                <GradientStop Color="#4A148C"
                              Offset="1"/>
        </LinearGradientBrush>

        <!-- 特殊节点类型渐变 - 基于草图设计 -->
        <LinearGradientBrush x:Key="ModernHeaderGradientCyan"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#00BCD4"
                              Offset="0"/>
                <GradientStop Color="#0097A7"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientIndigo"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#3F51B5"
                              Offset="0"/>
                <GradientStop Color="#303F9F"
                              Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="ModernHeaderGradientPink"
                             StartPoint="0,0"
                             EndPoint="0,1">
                <GradientStop Color="#E91E63"
                              Offset="0"/>
                <GradientStop Color="#C2185B"
                              Offset="1"/>
        </LinearGradientBrush>

        <!-- 现代化输入连接器样式 -->
        <Style x:Key="ModernInputConnectorStyle"
               TargetType="nodify:NodeInput">
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="nodify:NodeInput">
                                        <Ellipse Width="10"
                                                 Height="10"
                                                 Fill="#4A90E2"
                                                 Stroke="White"
                                                 StrokeThickness="1"
                                                 Cursor="Hand">
                                                <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                                <Style.Triggers>
                                                                        <Trigger Property="IsMouseOver"
                                                                                 Value="True">
                                                                                <Setter Property="Fill"
                                                                                        Value="#5BA0F2"/>
                                                                                <Setter Property="StrokeThickness"
                                                                                        Value="2"/>
                                                                        </Trigger>
                                                                </Style.Triggers>
                                                        </Style>
                                                </Ellipse.Style>
                                        </Ellipse>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

        <!-- 现代化输出连接器样式 -->
        <Style x:Key="ModernOutputConnectorStyle"
               TargetType="nodify:NodeOutput">
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="nodify:NodeOutput">
                                        <Ellipse Width="10"
                                                 Height="10"
                                                 Fill="#4A90E2"
                                                 Stroke="White"
                                                 StrokeThickness="1"
                                                 Cursor="Hand">
                                                <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                                <Style.Triggers>
                                                                        <Trigger Property="IsMouseOver"
                                                                                 Value="True">
                                                                                <Setter Property="Fill"
                                                                                        Value="#5BA0F2"/>
                                                                                <Setter Property="StrokeThickness"
                                                                                        Value="2"/>
                                                                        </Trigger>
                                                                </Style.Triggers>
                                                        </Style>
                                                </Ellipse.Style>
                                        </Ellipse>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

        <!-- 完全按照HTML原型的连接器样式 -->
        <Style x:Key="HtmlPrototypeInputConnectorStyle"
               TargetType="nodify:NodeInput">
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="nodify:NodeInput">
                                        <Ellipse Width="12"
                                                 Height="12"
                                                 Fill="#4285F4"
                                                 Stroke="White"
                                                 StrokeThickness="1"
                                                 Cursor="Hand"/>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

        <Style x:Key="HtmlPrototypeOutputConnectorStyle"
               TargetType="nodify:NodeOutput">
                <Setter Property="Template">
                        <Setter.Value>
                                <ControlTemplate TargetType="nodify:NodeOutput">
                                        <Ellipse Width="12"
                                                 Height="12"
                                                 Fill="#4285F4"
                                                 Stroke="White"
                                                 StrokeThickness="1"
                                                 Cursor="Hand"/>
                                </ControlTemplate>
                        </Setter.Value>
                </Setter>
        </Style>

        <!-- 现代化节点模板 - 优化宽度以适应完整标题 -->
        <DataTemplate x:Key="NodeTemplate"
                      DataType="{x:Type viewmodels:ModuleNodeViewModel}">
                <nodify:Node Input="{Binding Inputs}"
                             Output="{Binding Outputs}"
                             Width="280"
                             Height="120"
                             Background="Transparent"
                             BorderBrush="Transparent"
                             BorderThickness="0"
                             Padding="0"
                             Margin="0">

                        <!-- 覆盖Node的默认样式 -->
                        <nodify:Node.Style>
                                <Style TargetType="nodify:Node">
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                        <Setter Property="BorderBrush"
                                                Value="Transparent"/>
                                        <Setter Property="BorderThickness"
                                                Value="0"/>
                                        <Setter Property="Padding"
                                                Value="0"/>
                                        <Setter Property="Margin"
                                                Value="0"/>
                                        <Setter Property="Template">
                                                <Setter.Value>
                                                        <ControlTemplate TargetType="nodify:Node">
                                                                <Grid Background="Transparent">
                                                                        <!-- 节点内容 -->
                                                                        <ContentPresenter Content="{TemplateBinding Content}"
                                                                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>

                                                                        <!-- 输入连接器 - 调整到分层标题栏的中心位置 -->
                                                                        <ItemsControl x:Name="PART_Input"
                                                                                      ItemsSource="{TemplateBinding Input}"
                                                                                      ItemTemplate="{TemplateBinding InputConnectorTemplate}"
                                                                                      HorizontalAlignment="Left"
                                                                                      VerticalAlignment="Top"
                                                                                      Margin="-9,28,0,0"
                                                                                      Focusable="False"/>

                                                                        <!-- 输出连接器 - 调整到分层标题栏的中心位置 -->
                                                                        <ItemsControl x:Name="PART_Output"
                                                                                      ItemsSource="{TemplateBinding Output}"
                                                                                      ItemTemplate="{TemplateBinding OutputConnectorTemplate}"
                                                                                      HorizontalAlignment="Right"
                                                                                      VerticalAlignment="Top"
                                                                                      Margin="0,28,-9,0"
                                                                                      Focusable="False"/>
                                                                </Grid>
                                                        </ControlTemplate>
                                                </Setter.Value>
                                        </Setter>
                                </Style>
                        </nodify:Node.Style>

                        <!-- 简化的输入连接器模板 -->
                        <nodify:Node.InputConnectorTemplate>
                                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                                        <nodify:NodeInput Header=""
                                                          IsConnected="{Binding IsConnected}"
                                                          Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                          ToolTip="{Binding Title}">
                                                <nodify:NodeInput.Template>
                                                        <ControlTemplate TargetType="nodify:NodeInput">
                                                                <Grid Width="20"
                                                                      Height="20"
                                                                      Cursor="Hand">
                                                                        <!-- 主连接器 - 更大更明显 -->
                                                                        <Ellipse x:Name="MainConnector"
                                                                                 Width="16"
                                                                                 Height="16"
                                                                                 Fill="#4CAF50"
                                                                                 Stroke="White"
                                                                                 StrokeThickness="2.5"
                                                                                 RenderTransformOrigin="0.5,0.5">
                                                                                <Ellipse.RenderTransform>
                                                                                        <ScaleTransform ScaleX="1"
                                                                                                        ScaleY="1"/>
                                                                                </Ellipse.RenderTransform>
                                                                        </Ellipse>

                                                                        <!-- 连接状态指示器 -->
                                                                        <Ellipse x:Name="ConnectionIndicator"
                                                                                 Width="4"
                                                                                 Height="4"
                                                                                 Fill="White"
                                                                                 Opacity="0"/>

                                                                        <!-- 多连接数量徽章 - Material Design风格 (类似Gmail/Android通知) -->
                                                                        <Border x:Name="ConnectionCountBadge"
                                                                                CornerRadius="10"
                                                                                MinWidth="20"
                                                                                Height="20"
                                                                                HorizontalAlignment="Right"
                                                                                VerticalAlignment="Top"
                                                                                Margin="0,-10,-10,0"
                                                                                Background="#FF4444"
                                                                                Visibility="Collapsed">
                                                                                <Border.Effect>
                                                                                        <DropShadowEffect Color="#000000"
                                                                                                          Opacity="0.25"
                                                                                                          ShadowDepth="2"
                                                                                                          BlurRadius="4"/>
                                                                                </Border.Effect>
                                                                                <TextBlock Text="{Binding ConnectionCount, FallbackValue=0}"
                                                                                           Foreground="White"
                                                                                           FontSize="10"
                                                                                           FontWeight="Bold"
                                                                                           HorizontalAlignment="Center"
                                                                                           VerticalAlignment="Center"
                                                                                           Margin="6,2"/>
                                                                        </Border>
                                                                </Grid>

                                                                <ControlTemplate.Triggers>
                                                                        <!-- 鼠标悬停效果 -->
                                                                        <Trigger Property="IsMouseOver"
                                                                                 Value="True">
                                                                                <Setter TargetName="MainConnector"
                                                                                        Property="RenderTransform">
                                                                                        <Setter.Value>
                                                                                                <ScaleTransform ScaleX="1.2"
                                                                                                                ScaleY="1.2"/>
                                                                                        </Setter.Value>
                                                                                </Setter>
                                                                        </Trigger>

                                                                        <!-- 已连接状态 -->
                                                                        <DataTrigger Binding="{Binding IsConnected}"
                                                                                     Value="True">
                                                                                <Setter TargetName="ConnectionIndicator"
                                                                                        Property="Opacity"
                                                                                        Value="1.0"/>
                                                                        </DataTrigger>

                                                                        <!-- 多连接徽章触发器 -->
                                                                        <DataTrigger Binding="{Binding HasMultipleConnections}"
                                                                                     Value="True">
                                                                                <Setter TargetName="ConnectionCountBadge"
                                                                                        Property="Visibility"
                                                                                        Value="Visible"/>
                                                                        </DataTrigger>
                                                                </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                </nodify:NodeInput.Template>
                                        </nodify:NodeInput>
                                </DataTemplate>
                        </nodify:Node.InputConnectorTemplate>

                        <!-- 简化的输出连接器模板 -->
                        <nodify:Node.OutputConnectorTemplate>
                                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                                        <nodify:NodeOutput Header=""
                                                           IsConnected="{Binding IsConnected}"
                                                           Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                           ToolTip="{Binding Title}">
                                                <nodify:NodeOutput.Template>
                                                        <ControlTemplate TargetType="nodify:NodeOutput">
                                                                <Grid Width="20"
                                                                      Height="20"
                                                                      Cursor="Hand">
                                                                        <!-- 主连接器 - 更大更明显 -->
                                                                        <Ellipse x:Name="MainConnector"
                                                                                 Width="16"
                                                                                 Height="16"
                                                                                 Fill="#2196F3"
                                                                                 Stroke="White"
                                                                                 StrokeThickness="2.5"
                                                                                 RenderTransformOrigin="0.5,0.5">
                                                                                <Ellipse.RenderTransform>
                                                                                        <ScaleTransform ScaleX="1"
                                                                                                        ScaleY="1"/>
                                                                                </Ellipse.RenderTransform>
                                                                        </Ellipse>

                                                                        <!-- 连接状态指示器 -->
                                                                        <Ellipse x:Name="ConnectionIndicator"
                                                                                 Width="4"
                                                                                 Height="4"
                                                                                 Fill="White"
                                                                                 Opacity="0"/>

                                                                        <!-- 多连接数量徽章 - Material Design风格 (类似Gmail/Android通知) -->
                                                                        <Border x:Name="ConnectionCountBadge"
                                                                                CornerRadius="10"
                                                                                MinWidth="20"
                                                                                Height="20"
                                                                                HorizontalAlignment="Right"
                                                                                VerticalAlignment="Top"
                                                                                Margin="0,-10,-10,0"
                                                                                Background="#FF4444"
                                                                                Visibility="Collapsed">
                                                                                <Border.Effect>
                                                                                        <DropShadowEffect Color="#000000"
                                                                                                          Opacity="0.25"
                                                                                                          ShadowDepth="2"
                                                                                                          BlurRadius="4"/>
                                                                                </Border.Effect>
                                                                                <TextBlock Text="{Binding ConnectionCount, FallbackValue=0}"
                                                                                           Foreground="White"
                                                                                           FontSize="10"
                                                                                           FontWeight="Bold"
                                                                                           HorizontalAlignment="Center"
                                                                                           VerticalAlignment="Center"
                                                                                           Margin="6,2"/>
                                                                        </Border>
                                                                </Grid>

                                                                <ControlTemplate.Triggers>
                                                                        <!-- 鼠标悬停效果 -->
                                                                        <Trigger Property="IsMouseOver"
                                                                                 Value="True">
                                                                                <Setter TargetName="MainConnector"
                                                                                        Property="RenderTransform">
                                                                                        <Setter.Value>
                                                                                                <ScaleTransform ScaleX="1.2"
                                                                                                                ScaleY="1.2"/>
                                                                                        </Setter.Value>
                                                                                </Setter>
                                                                        </Trigger>

                                                                        <!-- 已连接状态 -->
                                                                        <DataTrigger Binding="{Binding IsConnected}"
                                                                                     Value="True">
                                                                                <Setter TargetName="ConnectionIndicator"
                                                                                        Property="Opacity"
                                                                                        Value="1.0"/>
                                                                        </DataTrigger>

                                                                        <!-- 多连接徽章触发器 -->
                                                                        <DataTrigger Binding="{Binding HasMultipleConnections}"
                                                                                     Value="True">
                                                                                <Setter TargetName="ConnectionCountBadge"
                                                                                        Property="Visibility"
                                                                                        Value="Visible"/>
                                                                        </DataTrigger>
                                                                </ControlTemplate.Triggers>
                                                        </ControlTemplate>
                                                </nodify:NodeOutput.Template>
                                        </nodify:NodeOutput>
                                </DataTemplate>
                        </nodify:Node.OutputConnectorTemplate>

                        <!-- 现代化节点内容 - 完全基于手绘草图 -->
                        <Border Background="White"
                                CornerRadius="12"
                                BorderThickness="0">
                                <Border.Style>
                                        <Style TargetType="Border">
                                                <Setter Property="Effect">
                                                        <Setter.Value>
                                                                <DropShadowEffect Color="#000000"
                                                                                  Opacity="0.15"
                                                                                  ShadowDepth="4"
                                                                                  BlurRadius="12"/>
                                                        </Setter.Value>
                                                </Setter>
                                                <Style.Triggers>
                                                        <!-- 基于节点类型的边框颜色 -->
                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                     Value="{x:Static models:NodeType.Input}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                     Value="{x:Static models:NodeType.Transform}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#2196F3"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                     Value="{x:Static models:NodeType.Output}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#FF9800"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                     Value="{x:Static models:NodeType.Control}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#9C27B0"/>
                                                        </DataTrigger>

                                                        <!-- 特殊节点类型边框颜色 -->
                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                     Value="{x:Static models:ModuleType.ManualDataInput}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#00BCD4"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                     Value="{x:Static models:ModuleType.ArrayExpansion}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#3F51B5"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                     Value="{x:Static models:ModuleType.AIAgent}">
                                                                <Setter Property="BorderBrush"
                                                                        Value="#E91E63"/>
                                                        </DataTrigger>

                                                        <!-- 选中状态 -->
                                                        <DataTrigger Binding="{Binding IsSelected}"
                                                                     Value="True">
                                                                <Setter Property="BorderThickness"
                                                                        Value="3"/>
                                                                <Setter Property="Effect">
                                                                        <Setter.Value>
                                                                                <DropShadowEffect Color="#4A90E2"
                                                                                                  Opacity="0.6"
                                                                                                  ShadowDepth="0"
                                                                                                  BlurRadius="12"/>
                                                                        </Setter.Value>
                                                                </Setter>
                                                        </DataTrigger>
                                                        <!-- 锁定状态 -->
                                                        <DataTrigger Binding="{Binding IsLocked}"
                                                                     Value="True">
                                                                <Setter Property="Opacity"
                                                                        Value="0.8"/>
                                                                <Setter Property="Effect">
                                                                        <Setter.Value>
                                                                                <DropShadowEffect Color="Gray"
                                                                                                  Opacity="0.3"
                                                                                                  ShadowDepth="2"
                                                                                                  BlurRadius="4"/>
                                                                        </Setter.Value>
                                                                </Setter>
                                                        </DataTrigger>
                                                        <!-- 禁用状态 -->
                                                        <DataTrigger Binding="{Binding IsEnabled}"
                                                                     Value="False">
                                                                <Setter Property="Opacity"
                                                                        Value="0.5"/>
                                                                <Setter Property="BorderBrush"
                                                                        Value="#BDBDBD"/>
                                                        </DataTrigger>
                                                </Style.Triggers>
                                        </Style>
                                </Border.Style>

                                <Grid>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="56"/>
                                                <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>

                                        <!-- 分层标题栏 - 增加高度以支持两行布局 -->
                                        <Border Grid.Row="0"
                                                CornerRadius="12,12,0,0"
                                                Padding="8,4"
                                                Height="56">
                                                <Border.Style>
                                                        <Style TargetType="Border">
                                                                <Setter Property="Background"
                                                                        Value="{StaticResource ModernHeaderGradientBlue}"/>
                                                                <Style.Triggers>
                                                                        <!-- 基于节点类型的背景颜色 -->
                                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                                     Value="{x:Static models:NodeType.Input}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientGreen}"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                                     Value="{x:Static models:NodeType.Transform}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientBlue}"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                                     Value="{x:Static models:NodeType.Output}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientOrange}"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Module.NodeType}"
                                                                                     Value="{x:Static models:NodeType.Control}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientPurple}"/>
                                                                        </DataTrigger>

                                                                        <!-- 特殊节点类型背景颜色 -->
                                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                                     Value="{x:Static models:ModuleType.ManualDataInput}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientCyan}"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                                     Value="{x:Static models:ModuleType.ArrayExpansion}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientIndigo}"/>
                                                                        </DataTrigger>
                                                                        <DataTrigger Binding="{Binding Module.Type}"
                                                                                     Value="{x:Static models:ModuleType.AIAgent}">
                                                                                <Setter Property="Background"
                                                                                        Value="{StaticResource ModernHeaderGradientPink}"/>
                                                                        </DataTrigger>
                                                                </Style.Triggers>
                                                        </Style>
                                                </Border.Style>
                                                <Grid>
                                                        <Grid.RowDefinitions>
                                                                <RowDefinition Height="26"/>
                                                                <RowDefinition Height="26"/>
                                                        </Grid.RowDefinitions>

                                                        <!-- 第一行：主要信息 -->
                                                        <Grid Grid.Row="0">
                                                                <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                        <ColumnDefinition Width="*"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!-- 1. 折叠/展开按钮 - UX优化 -->
                                                                <Button Grid.Column="0"
                                                                        Width="24"
                                                                        Height="24"
                                                                        Background="Transparent"
                                                                        BorderBrush="Transparent"
                                                                        Margin="0,0,2,0"
                                                                        ToolTip="折叠/展开节点"
                                                                        Cursor="Hand">
                                                                        <materialDesign:PackIcon Kind="ChevronDown"
                                                                                                 Width="14"
                                                                                                 Height="14"
                                                                                                 Foreground="White"
                                                                                                 Opacity="0.9"/>
                                                                        <Button.Style>
                                                                                <Style TargetType="Button">
                                                                                        <Setter Property="Template">
                                                                                                <Setter.Value>
                                                                                                        <ControlTemplate TargetType="Button">
                                                                                                                <Border Background="{TemplateBinding Background}"
                                                                                                                        CornerRadius="3"
                                                                                                                        Padding="0">
                                                                                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                          VerticalAlignment="Center"/>
                                                                                                                </Border>
                                                                                                        </ControlTemplate>
                                                                                                </Setter.Value>
                                                                                        </Setter>
                                                                                        <Style.Triggers>
                                                                                                <Trigger Property="IsMouseOver"
                                                                                                         Value="True">
                                                                                                        <Setter Property="Background"
                                                                                                                Value="#33FFFFFF"/>
                                                                                                </Trigger>
                                                                                        </Style.Triggers>
                                                                                </Style>
                                                                        </Button.Style>
                                                                </Button>

                                                                <!-- 2. 节点logo (模块类型图标) -->
                                                                <materialDesign:PackIcon Grid.Column="1"
                                                                                         Kind="{Binding Module.Type, Converter={StaticResource ModuleTypeToIconConverter}}"
                                                                                         Width="18"
                                                                                         Height="18"
                                                                                         Foreground="White"
                                                                                         VerticalAlignment="Center"
                                                                                         Margin="0,0,4,0"/>

                                                                <!-- 3. 修改节点名称 (可编辑文本框) - UX优化 -->
                                                                <TextBox Grid.Column="2"
                                                                         Text="{Binding Module.Name, UpdateSourceTrigger=PropertyChanged}"
                                                                         Foreground="White"
                                                                         FontWeight="SemiBold"
                                                                         FontSize="12"
                                                                         VerticalAlignment="Center"
                                                                         Background="Transparent"
                                                                         BorderBrush="Transparent"
                                                                         BorderThickness="0"
                                                                         Padding="2"
                                                                         Margin="0,0,4,0"
                                                                         ToolTip="双击编辑节点名称">
                                                                        <TextBox.Style>
                                                                                <Style TargetType="TextBox">
                                                                                        <Setter Property="IsReadOnly"
                                                                                                Value="True"/>
                                                                                        <Setter Property="Cursor"
                                                                                                Value="Arrow"/>
                                                                                        <Style.Triggers>
                                                                                                <!-- 双击进入编辑模式 -->
                                                                                                <EventTrigger RoutedEvent="MouseDoubleClick">
                                                                                                        <BeginStoryboard>
                                                                                                                <Storyboard>
                                                                                                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetProperty="IsReadOnly">
                                                                                                                                <DiscreteBooleanKeyFrame KeyTime="0"
                                                                                                                                                         Value="False"/>
                                                                                                                        </BooleanAnimationUsingKeyFrames>
                                                                                                                </Storyboard>
                                                                                                        </BeginStoryboard>
                                                                                                </EventTrigger>
                                                                                                <!-- 失去焦点退出编辑模式 -->
                                                                                                <EventTrigger RoutedEvent="LostFocus">
                                                                                                        <BeginStoryboard>
                                                                                                                <Storyboard>
                                                                                                                        <BooleanAnimationUsingKeyFrames Storyboard.TargetProperty="IsReadOnly">
                                                                                                                                <DiscreteBooleanKeyFrame KeyTime="0"
                                                                                                                                                         Value="True"/>
                                                                                                                        </BooleanAnimationUsingKeyFrames>
                                                                                                                </Storyboard>
                                                                                                        </BeginStoryboard>
                                                                                                </EventTrigger>
                                                                                                <!-- 禁用状态显示删除线 -->
                                                                                                <DataTrigger Binding="{Binding IsEnabled}"
                                                                                                             Value="False">
                                                                                                        <Setter Property="TextDecorations"
                                                                                                                Value="Strikethrough"/>
                                                                                                </DataTrigger>
                                                                                                <!-- 编辑模式样式 -->
                                                                                                <Trigger Property="IsReadOnly"
                                                                                                         Value="False">
                                                                                                        <Setter Property="Background"
                                                                                                                Value="#33FFFFFF"/>
                                                                                                        <Setter Property="BorderBrush"
                                                                                                                Value="White"/>
                                                                                                        <Setter Property="BorderThickness"
                                                                                                                Value="1"/>
                                                                                                        <Setter Property="Cursor"
                                                                                                                Value="IBeam"/>
                                                                                                </Trigger>
                                                                                        </Style.Triggers>
                                                                                </Style>
                                                                        </TextBox.Style>
                                                                </TextBox>
                                                        </Grid>

                                                        <!-- 第二行：功能按钮组 -->
                                                        <Grid Grid.Row="1">
                                                                <Grid.ColumnDefinitions>
                                                                        <ColumnDefinition Width="*"/>
                                                                        <ColumnDefinition Width="Auto"/>
                                                                </Grid.ColumnDefinitions>

                                                                <!-- 功能按钮组 - 右对齐 -->
                                                                <StackPanel Grid.Column="1"
                                                                            Orientation="Horizontal"
                                                                            HorizontalAlignment="Right">

                                                                        <!-- 节点信息按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="节点信息"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Kind="Information"
                                                                                                         Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9"/>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 锁定位置按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="锁定/解锁位置"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9">
                                                                                        <materialDesign:PackIcon.Style>
                                                                                                <Style TargetType="materialDesign:PackIcon">
                                                                                                        <Setter Property="Kind"
                                                                                                                Value="LockOpen"/>
                                                                                                        <Style.Triggers>
                                                                                                                <DataTrigger Binding="{Binding IsLocked}"
                                                                                                                             Value="True">
                                                                                                                        <Setter Property="Kind"
                                                                                                                                Value="Lock"/>
                                                                                                                        <Setter Property="Opacity"
                                                                                                                                Value="1.0"/>
                                                                                                                </DataTrigger>
                                                                                                        </Style.Triggers>
                                                                                                </Style>
                                                                                        </materialDesign:PackIcon.Style>
                                                                                </materialDesign:PackIcon>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 锁定数据按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="锁定/解锁数据"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Kind="DatabaseLock"
                                                                                                         Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9"/>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 启用/禁用节点按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="启用/禁用节点"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9">
                                                                                        <materialDesign:PackIcon.Style>
                                                                                                <Style TargetType="materialDesign:PackIcon">
                                                                                                        <Setter Property="Kind"
                                                                                                                Value="Play"/>
                                                                                                        <Style.Triggers>
                                                                                                                <DataTrigger Binding="{Binding IsEnabled}"
                                                                                                                             Value="False">
                                                                                                                        <Setter Property="Kind"
                                                                                                                                Value="Pause"/>
                                                                                                                        <Setter Property="Foreground"
                                                                                                                                Value="#FF6B6B"/>
                                                                                                                </DataTrigger>
                                                                                                        </Style.Triggers>
                                                                                                </Style>
                                                                                        </materialDesign:PackIcon.Style>
                                                                                </materialDesign:PackIcon>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 执行状态信息 -->
                                                                        <Ellipse Width="12"
                                                                                 Height="12"
                                                                                 VerticalAlignment="Center"
                                                                                 Margin="3,0,3,0"
                                                                                 ToolTip="执行状态">
                                                                                <Ellipse.Style>
                                                                                        <Style TargetType="Ellipse">
                                                                                                <Setter Property="Fill"
                                                                                                        Value="#4CAF50"/>
                                                                                                <!-- 默认绿色：就绪 -->
                                                                                                <Style.Triggers>
                                                                                                        <!-- 根据执行状态改变颜色 -->
                                                                                                        <DataTrigger Binding="{Binding ExecutionStatus}"
                                                                                                                     Value="Running">
                                                                                                                <Setter Property="Fill"
                                                                                                                        Value="#2196F3"/>
                                                                                                                <!-- 蓝色：运行中 -->
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding ExecutionStatus}"
                                                                                                                     Value="Error">
                                                                                                                <Setter Property="Fill"
                                                                                                                        Value="#F44336"/>
                                                                                                                <!-- 红色：错误 -->
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding ExecutionStatus}"
                                                                                                                     Value="Warning">
                                                                                                                <Setter Property="Fill"
                                                                                                                        Value="#FF9800"/>
                                                                                                                <!-- 橙色：警告 -->
                                                                                                        </DataTrigger>
                                                                                                        <DataTrigger Binding="{Binding ExecutionStatus}"
                                                                                                                     Value="Completed">
                                                                                                                <Setter Property="Fill"
                                                                                                                        Value="#4CAF50"/>
                                                                                                                <!-- 绿色：完成 -->
                                                                                                        </DataTrigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Ellipse.Style>
                                                                        </Ellipse>

                                                                        <!-- 更新数据按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="更新数据"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Kind="Refresh"
                                                                                                         Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9"/>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 设置按钮 -->
                                                                        <Button Width="22"
                                                                                Height="22"
                                                                                Background="Transparent"
                                                                                BorderBrush="Transparent"
                                                                                Margin="2,0,2,0"
                                                                                ToolTip="节点设置"
                                                                                Cursor="Hand">
                                                                                <materialDesign:PackIcon Kind="Settings"
                                                                                                         Width="14"
                                                                                                         Height="14"
                                                                                                         Foreground="White"
                                                                                                         Opacity="0.9"/>
                                                                                <Button.Style>
                                                                                        <Style TargetType="Button">
                                                                                                <Setter Property="Template">
                                                                                                        <Setter.Value>
                                                                                                                <ControlTemplate TargetType="Button">
                                                                                                                        <Border Background="{TemplateBinding Background}"
                                                                                                                                CornerRadius="3"
                                                                                                                                Padding="0">
                                                                                                                                <ContentPresenter HorizontalAlignment="Center"
                                                                                                                                                  VerticalAlignment="Center"/>
                                                                                                                        </Border>
                                                                                                                </ControlTemplate>
                                                                                                        </Setter.Value>
                                                                                                </Setter>
                                                                                                <Style.Triggers>
                                                                                                        <Trigger Property="IsMouseOver"
                                                                                                                 Value="True">
                                                                                                                <Setter Property="Background"
                                                                                                                        Value="#33FFFFFF"/>
                                                                                                        </Trigger>
                                                                                                </Style.Triggers>
                                                                                        </Style>
                                                                                </Button.Style>
                                                                        </Button>

                                                                        <!-- 状态指示器 -->
                                                                        <Ellipse Width="8"
                                                                                 Height="8"
                                                                                 Fill="{Binding StatusBrush}"
                                                                                 VerticalAlignment="Center"
                                                                                 Margin="3,0,3,0"/>

                                                                </StackPanel>
                                                        </Grid>
                                                </Grid>
                                        </Border>

                                        <!-- 现代化内容区域 -->
                                        <Border Grid.Row="1"
                                                Background="#F8FAFC"
                                                CornerRadius="0,0,12,12"
                                                Padding="16,12">
                                                <TextBlock Text="{Binding Module.Description}"
                                                           FontSize="11"
                                                           Foreground="#64748B"
                                                           TextWrapping="Wrap"
                                                           VerticalAlignment="Center"
                                                           TextTrimming="CharacterEllipsis"
                                                           LineHeight="16"/>
                                        </Border>
                                </Grid>
                        </Border>
                </nodify:Node>
        </DataTemplate>

        <!-- 手动输入数据节点专用模板 -->
        <DataTemplate x:Key="ManualDataInputNodeTemplate"
                      DataType="{x:Type viewmodels:ModuleNodeViewModel}">
                <nodify:Node Input="{Binding Inputs}"
                             Output="{Binding Outputs}"
                             Width="200"
                             Height="120"
                             Background="Transparent"
                             BorderBrush="Transparent"
                             BorderThickness="0"
                             Padding="0"
                             Margin="0">

                        <!-- 使用与主模板相同的样式 -->
                        <nodify:Node.Style>
                                <Style TargetType="nodify:Node"
                                       BasedOn="{StaticResource {x:Type nodify:Node}}">
                                        <Setter Property="Background"
                                                Value="Transparent"/>
                                        <Setter Property="BorderBrush"
                                                Value="Transparent"/>
                                        <Setter Property="BorderThickness"
                                                Value="0"/>
                                        <Setter Property="Padding"
                                                Value="0"/>
                                        <Setter Property="Margin"
                                                Value="0"/>
                                        <Setter Property="Template">
                                                <Setter.Value>
                                                        <ControlTemplate TargetType="nodify:Node">
                                                                <Grid Background="Transparent">
                                                                        <ContentPresenter Content="{TemplateBinding Content}"
                                                                                          ContentTemplate="{TemplateBinding ContentTemplate}"/>
                                                                        <ItemsControl x:Name="PART_Input"
                                                                                      ItemsSource="{TemplateBinding Input}"
                                                                                      ItemTemplate="{TemplateBinding InputConnectorTemplate}"
                                                                                      HorizontalAlignment="Left"
                                                                                      VerticalAlignment="Top"
                                                                                      Margin="-9,59,0,0"
                                                                                      Focusable="False"/>
                                                                        <ItemsControl x:Name="PART_Output"
                                                                                      ItemsSource="{TemplateBinding Output}"
                                                                                      ItemTemplate="{TemplateBinding OutputConnectorTemplate}"
                                                                                      HorizontalAlignment="Right"
                                                                                      VerticalAlignment="Top"
                                                                                      Margin="0,59,-9,0"
                                                                                      Focusable="False"/>
                                                                </Grid>
                                                        </ControlTemplate>
                                                </Setter.Value>
                                        </Setter>
                                </Style>
                        </nodify:Node.Style>

                        <!-- 使用相同的连接器模板 -->
                        <nodify:Node.InputConnectorTemplate>
                                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                                        <nodify:NodeInput Header=""
                                                          IsConnected="{Binding IsConnected}"
                                                          Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                          ToolTip="{Binding Title}"
                                                          Style="{StaticResource HtmlPrototypeInputConnectorStyle}"/>
                                </DataTemplate>
                        </nodify:Node.InputConnectorTemplate>

                        <nodify:Node.OutputConnectorTemplate>
                                <DataTemplate DataType="{x:Type viewmodels:ConnectorViewModel}">
                                        <nodify:NodeOutput Header=""
                                                           IsConnected="{Binding IsConnected}"
                                                           Anchor="{Binding Anchor, Mode=OneWayToSource}"
                                                           ToolTip="{Binding Title}"
                                                           Style="{StaticResource HtmlPrototypeOutputConnectorStyle}"/>
                                </DataTemplate>
                        </nodify:Node.OutputConnectorTemplate>

                        <!-- 手动输入节点专用内容 -->
                        <Border Background="White"
                                CornerRadius="8"
                                BorderBrush="#00BCD4"
                                BorderThickness="2">
                                <Border.Effect>
                                        <DropShadowEffect Color="Black"
                                                          Opacity="0.1"
                                                          ShadowDepth="2"
                                                          BlurRadius="4"/>
                                </Border.Effect>

                                <Grid>
                                        <Grid.RowDefinitions>
                                                <RowDefinition Height="40"/>
                                                <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>

                                        <!-- Header -->
                                        <Border Grid.Row="0"
                                                Background="{StaticResource ModernHeaderGradientCyan}"
                                                CornerRadius="6,6,0,0"
                                                Padding="12,8"
                                                Height="40">
                                                <Grid>
                                                        <Grid.ColumnDefinitions>
                                                                <ColumnDefinition Width="Auto"/>
                                                                <ColumnDefinition Width="*"/>
                                                                <ColumnDefinition Width="Auto"/>
                                                        </Grid.ColumnDefinitions>

                                                        <!-- 键盘图标 -->
                                                        <materialDesign:PackIcon Grid.Column="0"
                                                                                 Kind="Keyboard"
                                                                                 Width="16"
                                                                                 Height="16"
                                                                                 Foreground="White"
                                                                                 VerticalAlignment="Center"
                                                                                 Margin="0,0,8,0"/>

                                                        <TextBlock Grid.Column="1"
                                                                   Text="手动输入"
                                                                   Foreground="White"
                                                                   FontWeight="Medium"
                                                                   FontSize="12"
                                                                   VerticalAlignment="Center"/>

                                                        <Ellipse Grid.Column="2"
                                                                 Width="6"
                                                                 Height="6"
                                                                 Fill="{Binding StatusBrush}"
                                                                 VerticalAlignment="Center"/>
                                                </Grid>
                                        </Border>

                                        <!-- Body -->
                                        <Border Grid.Row="1"
                                                Background="#F0F8FF"
                                                CornerRadius="0,0,6,6"
                                                Padding="12,8">
                                                <StackPanel>
                                                        <TextBlock Text="数据输入节点"
                                                                   FontSize="10"
                                                                   Foreground="#555"
                                                                   Margin="0,0,0,4"/>
                                                        <TextBlock Text="支持手动输入各种数据类型"
                                                                   FontSize="9"
                                                                   Foreground="#777"
                                                                   TextWrapping="Wrap"/>
                                                </StackPanel>
                                        </Border>
                                </Grid>
                        </Border>
                </nodify:Node>
        </DataTemplate>

</ResourceDictionary>