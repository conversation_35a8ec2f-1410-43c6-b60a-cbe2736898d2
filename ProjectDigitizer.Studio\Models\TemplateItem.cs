using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 模板项模型
    /// </summary>
    public class TemplateItem : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _description = string.Empty;
        private ModuleType _moduleType;
        private string _iconPath = string.Empty;
        private bool _isEnabled = true;

        /// <summary>
        /// 模板名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 模板描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        /// <summary>
        /// 对应的模块类型
        /// </summary>
        public ModuleType ModuleType
        {
            get => _moduleType;
            set => SetProperty(ref _moduleType, value);
        }

        /// <summary>
        /// 图标路径
        /// </summary>
        public string IconPath
        {
            get => _iconPath;
            set => SetProperty(ref _iconPath, value);
        }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
} 