using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 字段选择池交互逻辑
    /// </summary>
    public partial class FieldSelectionPool : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<FieldInfo> _selectedFields = new();
        private bool _isDragging = false;
        private FieldInfo? _draggedField = null;
        private Point _dragStartPoint;

        /// <summary>已选字段列表</summary>
        public ObservableCollection<FieldInfo> SelectedFields
        {
            get => _selectedFields;
            set
            {
                if (SetProperty(ref _selectedFields, value))
                {
                    UpdateStatistics();
                    SelectedFieldsChanged?.Invoke(this, _selectedFields);
                }
            }
        }

        /// <summary>已选字段数量</summary>
        public int SelectedFieldCount => _selectedFields.Count;

        /// <summary>是否有已选字段</summary>
        public bool HasSelectedFields => _selectedFields.Any();

        /// <summary>已连接字段数量</summary>
        public int ConnectedCount => _selectedFields.Count(f => f.IsConnected);

        /// <summary>已引用字段数量</summary>
        public int ReferencedCount => _selectedFields.Count(f => f.IsReferenced);

        /// <summary>激活字段数量</summary>
        public int ActiveCount => _selectedFields.Count(f => f.State == FieldReferenceState.Active);

        /// <summary>字段选择变化事件</summary>
        public event EventHandler<ObservableCollection<FieldInfo>>? SelectedFieldsChanged;

        /// <summary>字段移除事件</summary>
        public event EventHandler<FieldInfo>? FieldRemoved;

        /// <summary>字段顺序变化事件</summary>
        public event EventHandler<FieldOrderChangedEventArgs>? FieldOrderChanged;

        public FieldSelectionPool()
        {
            InitializeComponent();
            DataContext = this;
            InitializeEventHandlers();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            ClearAllButton.Click += OnClearAllClick;
            SortButton.Click += OnSortClick;
            ExportButton.Click += OnExportClick;

            // 监听字段集合变化
            _selectedFields.CollectionChanged += (s, e) =>
            {
                if (e.NewItems != null)
                {
                    foreach (FieldInfo field in e.NewItems)
                    {
                        field.PropertyChanged += OnFieldPropertyChanged;
                    }
                }
                if (e.OldItems != null)
                {
                    foreach (FieldInfo field in e.OldItems)
                    {
                        field.PropertyChanged -= OnFieldPropertyChanged;
                    }
                }
                UpdateStatistics();
            };

            // 支持从外部拖拽字段
            this.Drop += OnExternalDrop;
            this.DragOver += OnExternalDragOver;
        }

        /// <summary>
        /// 字段属性变化处理
        /// </summary>
        private void OnFieldPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(FieldInfo.IsConnected) ||
                e.PropertyName == nameof(FieldInfo.IsReferenced) ||
                e.PropertyName == nameof(FieldInfo.State))
            {
                UpdateStatistics();
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            OnPropertyChanged(nameof(SelectedFieldCount));
            OnPropertyChanged(nameof(HasSelectedFields));
            OnPropertyChanged(nameof(ConnectedCount));
            OnPropertyChanged(nameof(ReferencedCount));
            OnPropertyChanged(nameof(ActiveCount));
        }

        /// <summary>
        /// 添加字段
        /// </summary>
        /// <param name="field">要添加的字段</param>
        public void AddField(FieldInfo field)
        {
            if (field != null && !_selectedFields.Contains(field))
            {
                _selectedFields.Add(field);
            }
        }

        /// <summary>
        /// 移除字段
        /// </summary>
        /// <param name="field">要移除的字段</param>
        public void RemoveField(FieldInfo field)
        {
            if (field != null && _selectedFields.Contains(field))
            {
                _selectedFields.Remove(field);
                FieldRemoved?.Invoke(this, field);
            }
        }

        /// <summary>
        /// 清空所有字段
        /// </summary>
        public void ClearAll()
        {
            var removedFields = _selectedFields.ToList();
            _selectedFields.Clear();

            foreach (var field in removedFields)
            {
                FieldRemoved?.Invoke(this, field);
            }
        }

        /// <summary>
        /// 移动字段位置
        /// </summary>
        /// <param name="field">要移动的字段</param>
        /// <param name="newIndex">新位置索引</param>
        public void MoveField(FieldInfo field, int newIndex)
        {
            var oldIndex = _selectedFields.IndexOf(field);
            if (oldIndex >= 0 && newIndex >= 0 && newIndex < _selectedFields.Count && oldIndex != newIndex)
            {
                _selectedFields.Move(oldIndex, newIndex);
                FieldOrderChanged?.Invoke(this, new FieldOrderChangedEventArgs(field, oldIndex, newIndex));
            }
        }

        /// <summary>
        /// 字段鼠标按下处理
        /// </summary>
        private void OnFieldMouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && sender is FrameworkElement element)
            {
                _draggedField = element.DataContext as FieldInfo;
                _dragStartPoint = e.GetPosition(this);
                _isDragging = false;
                element.CaptureMouse();
            }
        }

        /// <summary>
        /// 字段鼠标移动处理
        /// </summary>
        private void OnFieldMouseMove(object sender, MouseEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed &&
                _draggedField != null &&
                sender is FrameworkElement element)
            {
                var currentPosition = e.GetPosition(this);
                var diff = _dragStartPoint - currentPosition;

                if (!_isDragging &&
                    (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                     Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance))
                {
                    _isDragging = true;

                    // 开始拖拽操作
                    var dragData = new DataObject("FieldInfo", _draggedField);
                    DragDrop.DoDragDrop(element, dragData, DragDropEffects.Move);

                    _isDragging = false;
                    _draggedField = null;
                    element.ReleaseMouseCapture();
                }
            }
        }

        /// <summary>
        /// 字段鼠标释放处理
        /// </summary>
        private void OnFieldMouseUp(object sender, MouseButtonEventArgs e)
        {
            if (sender is FrameworkElement element)
            {
                element.ReleaseMouseCapture();
                _isDragging = false;
                _draggedField = null;
            }
        }

        /// <summary>
        /// 字段拖拽放置处理
        /// </summary>
        private void OnFieldDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("FieldInfo") && sender is FrameworkElement element)
            {
                var droppedField = e.Data.GetData("FieldInfo") as FieldInfo;
                var targetField = element.DataContext as FieldInfo;

                if (droppedField != null && targetField != null && droppedField != targetField)
                {
                    var targetIndex = _selectedFields.IndexOf(targetField);
                    if (targetIndex >= 0)
                    {
                        MoveField(droppedField, targetIndex);
                    }
                }
            }

            HideDropIndicator();
        }

        /// <summary>
        /// 字段拖拽悬停处理
        /// </summary>
        private void OnFieldDragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("FieldInfo"))
            {
                e.Effects = DragDropEffects.Move;
                ShowDropIndicator();
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }

            e.Handled = true;
        }

        /// <summary>
        /// 外部拖拽放置处理
        /// </summary>
        private void OnExternalDrop(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("FieldInfo"))
            {
                var field = e.Data.GetData("FieldInfo") as FieldInfo;
                if (field != null)
                {
                    AddField(field);
                }
            }

            HideDropIndicator();
        }

        /// <summary>
        /// 外部拖拽悬停处理
        /// </summary>
        private void OnExternalDragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent("FieldInfo"))
            {
                e.Effects = DragDropEffects.Copy;
                ShowDropIndicator();
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }

            e.Handled = true;
        }

        /// <summary>
        /// 移除字段按钮点击处理
        /// </summary>
        private void OnRemoveFieldClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is FieldInfo field)
            {
                RemoveField(field);
            }
        }

        /// <summary>
        /// 清空所有按钮点击处理
        /// </summary>
        private void OnClearAllClick(object sender, RoutedEventArgs e)
        {
            if (MessageBox.Show("确定要清空所有已选字段吗？", "确认",
                MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
            {
                ClearAll();
            }
        }

        /// <summary>
        /// 排序按钮点击处理
        /// </summary>
        private void OnSortClick(object sender, RoutedEventArgs e)
        {
            // 创建排序菜单
            var contextMenu = new ContextMenu();

            contextMenu.Items.Add(new MenuItem
            {
                Header = "按名称排序",
                Icon = new MaterialDesignThemes.Wpf.PackIcon { Kind = MaterialDesignThemes.Wpf.PackIconKind.SortAlphabeticalAscending },
                Command = new RelayCommand(() => SortFields(f => f.DisplayName))
            });

            contextMenu.Items.Add(new MenuItem
            {
                Header = "按类型排序",
                Icon = new MaterialDesignThemes.Wpf.PackIcon { Kind = MaterialDesignThemes.Wpf.PackIconKind.SortVariant },
                Command = new RelayCommand(() => SortFields(f => f.DataType.ToString()))
            });

            contextMenu.Items.Add(new MenuItem
            {
                Header = "按状态排序",
                Icon = new MaterialDesignThemes.Wpf.PackIcon { Kind = MaterialDesignThemes.Wpf.PackIconKind.SortBoolAscending },
                Command = new RelayCommand(() => SortFields(f => f.State.ToString()))
            });

            contextMenu.PlacementTarget = SortButton;
            contextMenu.IsOpen = true;
        }

        /// <summary>
        /// 导出按钮点击处理
        /// </summary>
        private void OnExportClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现字段列表导出功能
            var fieldList = string.Join("\n", _selectedFields.Select(f =>
                $"{f.DisplayName} ({f.DataType}) - {f.State}"));

            Clipboard.SetText(fieldList);
            MessageBox.Show("字段列表已复制到剪贴板", "导出成功",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 排序字段
        /// </summary>
        private void SortFields<T>(Func<FieldInfo, T> keySelector) where T : IComparable<T>
        {
            var sortedFields = _selectedFields.OrderBy(keySelector).ToList();
            _selectedFields.Clear();
            foreach (var field in sortedFields)
            {
                _selectedFields.Add(field);
            }
        }

        /// <summary>
        /// 显示拖拽指示器
        /// </summary>
        private void ShowDropIndicator()
        {
            DropIndicator.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 隐藏拖拽指示器
        /// </summary>
        private void HideDropIndicator()
        {
            DropIndicator.Visibility = Visibility.Collapsed;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 字段顺序变化事件参数
    /// </summary>
    public class FieldOrderChangedEventArgs : EventArgs
    {
        public FieldInfo Field { get; }
        public int OldIndex { get; }
        public int NewIndex { get; }

        public FieldOrderChangedEventArgs(FieldInfo field, int oldIndex, int newIndex)
        {
            Field = field;
            OldIndex = oldIndex;
            NewIndex = newIndex;
        }
    }

    /// <summary>
    /// 简单的命令实现
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool>? _canExecute;

        public RelayCommand(Action execute, Func<bool>? canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object? parameter) => _execute();
    }
}
