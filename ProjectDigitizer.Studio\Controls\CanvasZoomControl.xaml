<UserControl x:Class="ProjectDigitizer.Studio.Controls.CanvasZoomControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="200">
    
    <UserControl.Resources>
        <!-- 缩放按钮样式 -->
        <Style x:Key="ZoomButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="FontFamily" Value="Segoe MDL2 Assets"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E0E0E0"/>
                                <Setter Property="BorderBrush" Value="#999999"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#D0D0D0"/>
                                <Setter Property="BorderBrush" Value="#666666"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 缩放百分比显示样式 -->
        <Style x:Key="ZoomPercentageStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="MinWidth" Value="50"/>
        </Style>
    </UserControl.Resources>

    <Border Background="#FFFFFF"
            BorderBrush="#CCCCCC"
            BorderThickness="1"
            CornerRadius="6"
            Padding="8,4">
        <StackPanel Orientation="Horizontal" 
                    VerticalAlignment="Center">
            
            <!-- 缩小按钮 -->
            <Button x:Name="ZoomOutButton"
                    Style="{StaticResource ZoomButtonStyle}"
                    Content="&#xE738;"
                    ToolTip="缩小 (Ctrl + -)"
                    Click="ZoomOutButton_Click"/>
            
            <!-- 缩放百分比显示 -->
            <Border Margin="8,0"
                    Background="#F8F9FA"
                    BorderBrush="#E0E0E0"
                    BorderThickness="1"
                    CornerRadius="3"
                    Padding="8,4"
                    MinWidth="60">
                <TextBlock x:Name="ZoomPercentageText"
                           Style="{StaticResource ZoomPercentageStyle}"
                           Text="{Binding ZoomPercentage, StringFormat={}{0:F0}%}"/>
            </Border>
            
            <!-- 放大按钮 -->
            <Button x:Name="ZoomInButton"
                    Style="{StaticResource ZoomButtonStyle}"
                    Content="&#xE710;"
                    ToolTip="放大 (Ctrl + +)"
                    Click="ZoomInButton_Click"/>
            
            <!-- 分隔线 -->
            <Rectangle Width="1"
                       Height="24"
                       Fill="#E0E0E0"
                       Margin="8,0"/>
            
            <!-- 重置缩放按钮 -->
            <Button x:Name="ResetZoomButton"
                    Style="{StaticResource ZoomButtonStyle}"
                    Content="&#xE8A9;"
                    ToolTip="重置缩放 (Ctrl + 0)"
                    Click="ResetZoomButton_Click"/>
            
            <!-- 适应画布按钮 -->
            <Button x:Name="FitToCanvasButton"
                    Style="{StaticResource ZoomButtonStyle}"
                    Content="&#xE9A6;"
                    ToolTip="适应画布 (Ctrl + F)"
                    Margin="4,0,0,0"
                    Click="FitToCanvasButton_Click"/>
            
        </StackPanel>
    </Border>
</UserControl>
