using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Processors;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 节点处理器注册表
    /// 管理所有节点类型的处理器
    /// </summary>
    public class NodeProcessorRegistry
    {
        private static readonly Lazy<NodeProcessorRegistry> _instance = new(() => new NodeProcessorRegistry());
        private readonly Dictionary<ModuleType, INodeProcessor> _processors;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static NodeProcessorRegistry Instance => _instance.Value;

        private NodeProcessorRegistry()
        {
            _processors = new Dictionary<ModuleType, INodeProcessor>();
            RegisterDefaultProcessors();
        }

        /// <summary>
        /// 注册节点处理器
        /// </summary>
        /// <param name="processor">处理器实例</param>
        public void RegisterProcessor(INodeProcessor processor)
        {
            if (processor == null)
                throw new ArgumentNullException(nameof(processor));

            _processors[processor.SupportedModuleType] = processor;
            System.Diagnostics.Debug.WriteLine($"已注册节点处理器: {processor.SupportedModuleType}");
        }

        /// <summary>
        /// 获取节点处理器
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>处理器实例，如果未找到则返回null</returns>
        public INodeProcessor? GetProcessor(ModuleType moduleType)
        {
            return _processors.TryGetValue(moduleType, out var processor) ? processor : null;
        }

        /// <summary>
        /// 检查是否支持指定的模块类型
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>是否支持</returns>
        public bool IsSupported(ModuleType moduleType)
        {
            return _processors.ContainsKey(moduleType);
        }

        /// <summary>
        /// 获取所有已注册的模块类型
        /// </summary>
        /// <returns>模块类型列表</returns>
        public IEnumerable<ModuleType> GetSupportedModuleTypes()
        {
            return _processors.Keys;
        }

        /// <summary>
        /// 执行节点
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>执行结果</returns>
        public Interfaces.NodeExecutionResult ExecuteNode(INode node, Dictionary<string, object> inputData)
        {
            if (node == null)
                throw new ArgumentNullException(nameof(node));

            var processor = GetProcessor(node.ModuleType);
            if (processor == null)
            {
                return new Interfaces.NodeExecutionResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"未找到模块类型 {node.ModuleType} 的处理器"
                };
            }

            try
            {
                return processor.Execute(node, inputData);
            }
            catch (Exception ex)
            {
                return new Interfaces.NodeExecutionResult
                {
                    IsSuccess = false,
                    ErrorMessage = $"节点执行异常: {ex.Message}",
                    ExecutionTime = TimeSpan.Zero
                };
            }
        }

        /// <summary>
        /// 注册默认处理器
        /// </summary>
        private void RegisterDefaultProcessors()
        {
            // 注册 DataFilter 处理器
            RegisterProcessor(new DataFilterNodeProcessor());

            // 这里可以注册其他节点的处理器
            // RegisterProcessor(new DataCalculationNodeProcessor());
            // RegisterProcessor(new DataTransformNodeProcessor());
            // 等等...

            System.Diagnostics.Debug.WriteLine($"已注册 {_processors.Count} 个节点处理器");
        }

        /// <summary>
        /// 获取处理器统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ProcessorRegistryStats GetStats()
        {
            return new ProcessorRegistryStats
            {
                TotalProcessors = _processors.Count,
                SupportedModuleTypes = _processors.Keys.ToList(),
                ProcessorTypes = _processors.Values.Select(p => p.GetType().Name).ToList()
            };
        }
    }

    /// <summary>
    /// 处理器注册表统计信息
    /// </summary>
    public class ProcessorRegistryStats
    {
        /// <summary>
        /// 总处理器数量
        /// </summary>
        public int TotalProcessors { get; set; }

        /// <summary>
        /// 支持的模块类型
        /// </summary>
        public List<ModuleType> SupportedModuleTypes { get; set; } = new();

        /// <summary>
        /// 处理器类型名称
        /// </summary>
        public List<string> ProcessorTypes { get; set; } = new();

        /// <summary>
        /// 获取摘要信息
        /// </summary>
        /// <returns>摘要字符串</returns>
        public string GetSummary()
        {
            return $"共注册 {TotalProcessors} 个处理器，支持 {SupportedModuleTypes.Count} 种模块类型";
        }
    }

    /// <summary>
    /// 节点执行服务
    /// 提供高级的节点执行功能
    /// </summary>
    public class NodeExecutionService
    {
        private readonly NodeProcessorRegistry _registry;

        public NodeExecutionService()
        {
            _registry = NodeProcessorRegistry.Instance;
        }

        /// <summary>
        /// 执行单个节点
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>执行结果</returns>
        public Interfaces.NodeExecutionResult ExecuteNode(INode node, Dictionary<string, object> inputData)
        {
            return _registry.ExecuteNode(node, inputData);
        }

        /// <summary>
        /// 批量执行节点
        /// </summary>
        /// <param name="nodes">节点列表</param>
        /// <param name="inputData">输入数据</param>
        /// <returns>执行结果列表</returns>
        public List<Interfaces.NodeExecutionResult> ExecuteNodes(IEnumerable<INode> nodes, Dictionary<string, object> inputData)
        {
            var results = new List<Interfaces.NodeExecutionResult>();

            foreach (var node in nodes)
            {
                if (node.IsEnabled)
                {
                    var result = ExecuteNode(node, inputData);
                    results.Add(result);

                    // 如果节点执行失败，可以选择是否继续执行后续节点
                    if (!result.IsSuccess)
                    {
                        System.Diagnostics.Debug.WriteLine($"节点 {node.Name} 执行失败: {result.ErrorMessage}");
                    }
                }
            }

            return results;
        }

        /// <summary>
        /// 检查节点是否可执行
        /// </summary>
        /// <param name="node">节点实例</param>
        /// <returns>是否可执行</returns>
        public bool CanExecuteNode(INode node)
        {
            return node.IsEnabled && _registry.IsSupported(node.ModuleType);
        }

        /// <summary>
        /// 获取节点的输入端口定义
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>输入端口定义列表</returns>
        public IEnumerable<IPortDefinition> GetInputPorts(ModuleType moduleType)
        {
            var processor = _registry.GetProcessor(moduleType);
            return processor?.GetInputPorts() ?? Enumerable.Empty<IPortDefinition>();
        }

        /// <summary>
        /// 获取节点的输出端口定义
        /// </summary>
        /// <param name="moduleType">模块类型</param>
        /// <returns>输出端口定义列表</returns>
        public IEnumerable<IPortDefinition> GetOutputPorts(ModuleType moduleType)
        {
            var processor = _registry.GetProcessor(moduleType);
            return processor?.GetOutputPorts() ?? Enumerable.Empty<IPortDefinition>();
        }
    }
}
