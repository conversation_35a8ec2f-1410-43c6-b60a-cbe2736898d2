using System;
using System.Diagnostics;

namespace ProjectDigitizer.Studio.Tests
{
    /// <summary>
    /// 测试运行器 - 用于运行函数节点的各种测试
    /// </summary>
    public static class TestRunner
    {
        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("ProjectDigitizer 函数节点测试套件");
            Console.WriteLine("=====================================");
            Console.WriteLine($"测试开始时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine();

            var stopwatch = Stopwatch.StartNew();

            try
            {
                // 运行功能测试
                var functionTests = new FunctionNodeTests();
                functionTests.RunAllTests();

                // 运行性能测试
                functionTests.RunPerformanceTests();

                stopwatch.Stop();

                Console.WriteLine();
                Console.WriteLine("=====================================");
                Console.WriteLine($"测试完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine($"总耗时: {stopwatch.ElapsedMilliseconds} ms");
                Console.WriteLine("所有测试执行完毕！");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                Console.WriteLine();
                Console.WriteLine("=====================================");
                Console.WriteLine("测试执行失败！");
                Console.WriteLine($"错误信息: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 运行快速测试（仅基础功能）
        /// </summary>
        public static void RunQuickTests()
        {
            Console.WriteLine("ProjectDigitizer 函数节点快速测试");
            Console.WriteLine("=================================");

            var functionTests = new FunctionNodeTests();
            
            try
            {
                functionTests.TestBasicMathFunctions();
                functionTests.TestFunctionRegistry();
                Console.WriteLine("\n快速测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n快速测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        public static void RunPerformanceTests()
        {
            Console.WriteLine("ProjectDigitizer 函数节点性能测试");
            Console.WriteLine("=================================");

            var functionTests = new FunctionNodeTests();
            
            try
            {
                functionTests.RunPerformanceTests();
                Console.WriteLine("\n性能测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n性能测试失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示测试菜单
        /// </summary>
        public static void ShowTestMenu()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("ProjectDigitizer 函数节点测试菜单");
                Console.WriteLine("=================================");
                Console.WriteLine("1. 运行所有测试");
                Console.WriteLine("2. 运行快速测试");
                Console.WriteLine("3. 运行性能测试");
                Console.WriteLine("4. 退出");
                Console.WriteLine();
                Console.Write("请选择 (1-4): ");

                var choice = Console.ReadLine();
                Console.WriteLine();

                switch (choice)
                {
                    case "1":
                        RunAllTests();
                        break;
                    case "2":
                        RunQuickTests();
                        break;
                    case "3":
                        RunPerformanceTests();
                        break;
                    case "4":
                        return;
                    default:
                        Console.WriteLine("无效选择，请重试。");
                        break;
                }

                Console.WriteLine();
                Console.WriteLine("按任意键继续...");
                Console.ReadKey();
            }
        }
    }
}
