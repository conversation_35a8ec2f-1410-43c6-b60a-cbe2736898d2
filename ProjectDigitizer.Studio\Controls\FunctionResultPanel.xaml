<UserControl x:Class="ProjectDigitizer.Studio.Controls.FunctionResultPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="600">

    <UserControl.Resources>
        <!-- 工具按钮样式 -->
        <Style x:Key="ToolButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignIconButton}">
            <Setter Property="Width" Value="28"/>
            <Setter Property="Height" Value="28"/>
            <Setter Property="Margin" Value="2"/>
        </Style>

        <!-- 结果状态样式 -->
        <Style x:Key="ResultStatusStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsSuccess}" Value="True">
                    <Setter Property="Kind" Value="CheckCircle"/>
                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignValidationSuccessBrush}"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsSuccess}" Value="False">
                    <Setter Property="Kind" Value="AlertCircle"/>
                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 数据网格样式 -->
        <Style x:Key="ResultDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="CanUserReorderColumns" Value="True"/>
            <Setter Property="CanUserResizeColumns" Value="True"/>
            <Setter Property="CanUserSortColumns" Value="True"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>

        <!-- 结果值样式 -->
        <Style x:Key="ResultValueStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding DataContext.IsSuccess, RelativeSource={RelativeSource AncestorType=DataGridRow}}" Value="False">
                    <Setter Property="Foreground" Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                    <Setter Property="FontStyle" Value="Italic"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <DockPanel>
        <!-- 顶部工具栏 -->
        <StackPanel DockPanel.Dock="Top" Orientation="Horizontal" Margin="0,0,0,8">
            <TextBlock Text="执行结果" 
                       FontSize="14" 
                       FontWeight="Medium" 
                       Foreground="{DynamicResource MaterialDesignBody}"
                       VerticalAlignment="Center"/>
            
            <TextBlock Text="{Binding ResultCount, StringFormat=({0})}" 
                       FontSize="12" 
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       Margin="4,0,0,0"
                       VerticalAlignment="Center"/>
            
            <Button x:Name="ExecuteAllButton" 
                    Style="{StaticResource ToolButtonStyle}"
                    Content="{materialDesign:PackIcon Kind=Play}"
                    ToolTip="执行所有函数"
                    Margin="8,0,0,0"/>
            
            <Button x:Name="RefreshButton" 
                    Style="{StaticResource ToolButtonStyle}"
                    Content="{materialDesign:PackIcon Kind=Refresh}"
                    ToolTip="刷新结果"/>
            
            <Button x:Name="ExportButton" 
                    Style="{StaticResource ToolButtonStyle}"
                    Content="{materialDesign:PackIcon Kind=Export}"
                    ToolTip="导出结果"/>
            
            <Button x:Name="FormatButton" 
                    Style="{StaticResource ToolButtonStyle}"
                    Content="{materialDesign:PackIcon Kind=FormatListBulleted}"
                    ToolTip="格式设置"/>
            
            <Button x:Name="MaterialLibraryButton" 
                    Style="{StaticResource ToolButtonStyle}"
                    Content="{materialDesign:PackIcon Kind=Library}"
                    ToolTip="材料库绑定"/>
        </StackPanel>

        <!-- 统计信息栏 -->
        <Border DockPanel.Dock="Top" 
                Background="{DynamicResource MaterialDesignChipBackground}"
                CornerRadius="4"
                Padding="8,4"
                Margin="0,0,0,8"
                Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="成功:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="{Binding SuccessCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="失败:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="4,0,0,0"/>
                <TextBlock Text="{Binding ErrorCount}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
                <TextBlock Text="/" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                <TextBlock Text="执行时间:" FontSize="11" Foreground="{DynamicResource MaterialDesignBodyLight}" Margin="8,0,0,0"/>
                <TextBlock Text="{Binding TotalExecutionTime, StringFormat={}{0}ms}" FontSize="11" FontWeight="Medium" Margin="4,0"/>
            </StackPanel>
        </Border>

        <!-- 结果数据网格 -->
        <DataGrid x:Name="ResultDataGrid" 
                  ItemsSource="{Binding FunctionResults}"
                  Style="{StaticResource ResultDataGridStyle}"
                  SelectionMode="Extended"
                  SelectionUnit="FullRow">
            
            <DataGrid.Columns>
                <!-- 状态列 -->
                <DataGridTemplateColumn Header="" Width="30" CanUserResize="False" CanUserSort="False">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <materialDesign:PackIcon Style="{StaticResource ResultStatusStyle}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 函数名称列 -->
                <DataGridTextColumn Header="函数名称" 
                                    Binding="{Binding FunctionName}" 
                                    Width="120"
                                    FontWeight="Medium"/>
                
                <!-- 表达式列 -->
                <DataGridTextColumn Header="表达式" 
                                    Binding="{Binding Expression}" 
                                    Width="200"
                                    FontFamily="Consolas"
                                    FontSize="11"/>
                
                <!-- 结果值列 -->
                <DataGridTemplateColumn Header="结果值" Width="120">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding FormattedResult}" 
                                       Style="{StaticResource ResultValueStyle}"
                                       ToolTip="{Binding RawResult}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 数据类型列 -->
                <DataGridTextColumn Header="类型" 
                                    Binding="{Binding ResultType}" 
                                    Width="80"
                                    FontSize="11"/>
                
                <!-- 单位列 -->
                <DataGridTextColumn Header="单位" 
                                    Binding="{Binding Unit}" 
                                    Width="60"
                                    FontSize="11"/>
                
                <!-- 执行时间列 -->
                <DataGridTextColumn Header="执行时间(ms)" 
                                    Binding="{Binding ExecutionTime}" 
                                    Width="100"
                                    FontSize="11"/>
                
                <!-- 错误信息列 -->
                <DataGridTemplateColumn Header="错误信息" Width="*" MinWidth="150">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding ErrorMessage}" 
                                       Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"
                                       FontSize="11"
                                       TextWrapping="Wrap"
                                       Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <!-- 操作列 -->
                <DataGridTemplateColumn Header="操作" Width="80" CanUserResize="False">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignToolButton}"
                                        Width="20" Height="20"
                                        Padding="0"
                                        Content="{materialDesign:PackIcon Kind=ContentCopy, Size=12}"
                                        ToolTip="复制结果"
                                        Click="OnCopyResultClick"/>
                                
                                <Button Style="{StaticResource MaterialDesignToolButton}"
                                        Width="20" Height="20"
                                        Padding="0" Margin="2,0,0,0"
                                        Content="{materialDesign:PackIcon Kind=Cog, Size=12}"
                                        ToolTip="格式设置"
                                        Click="OnFormatSettingsClick"/>
                                
                                <Button Style="{StaticResource MaterialDesignToolButton}"
                                        Width="20" Height="20"
                                        Padding="0" Margin="2,0,0,0"
                                        Content="{materialDesign:PackIcon Kind=Link, Size=12}"
                                        ToolTip="材料库绑定"
                                        Click="OnMaterialLibraryBindClick"
                                        Visibility="{Binding CanBindToMaterialLibrary, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
            
            <!-- 右键菜单 -->
            <DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="复制结果值" 
                              Icon="{materialDesign:PackIcon Kind=ContentCopy}"
                              Click="OnCopyResultClick"/>
                    <MenuItem Header="复制整行" 
                              Icon="{materialDesign:PackIcon Kind=ContentCopy}"
                              Click="OnCopyRowClick"/>
                    <Separator/>
                    <MenuItem Header="导出选中项" 
                              Icon="{materialDesign:PackIcon Kind=Export}"
                              Click="OnExportSelectedClick"/>
                    <MenuItem Header="导出全部" 
                              Icon="{materialDesign:PackIcon Kind=ExportVariant}"
                              Click="OnExportAllClick"/>
                    <Separator/>
                    <MenuItem Header="格式设置" 
                              Icon="{materialDesign:PackIcon Kind=FormatListBulleted}"
                              Click="OnFormatSettingsClick"/>
                    <MenuItem Header="材料库绑定" 
                              Icon="{materialDesign:PackIcon Kind=Library}"
                              Click="OnMaterialLibraryBindClick"/>
                </ContextMenu>
            </DataGrid.ContextMenu>
        </DataGrid>

        <!-- 空状态提示 -->
        <StackPanel x:Name="EmptyStatePanel"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Visibility="{Binding HasResults, Converter={StaticResource BooleanToVisibilityConverter}, ConverterParameter=True}">
            <materialDesign:PackIcon Kind="Calculator"
                                     Width="48"
                                     Height="48"
                                     Foreground="{DynamicResource MaterialDesignBodyLight}"
                                     HorizontalAlignment="Center"
                                     Margin="0,0,0,16"/>
            <TextBlock Text="暂无执行结果"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       FontSize="14"
                       TextAlignment="Center"
                       Margin="0,0,0,4"/>
            <TextBlock Text="点击执行按钮运行函数表达式"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       FontSize="12"
                       TextAlignment="Center"/>
        </StackPanel>

        <!-- 格式设置弹出框 -->
        <Popup x:Name="FormatSettingsPopup"
               PlacementTarget="{Binding ElementName=FormatButton}"
               Placement="Bottom"
               AllowsTransparency="True"
               StaysOpen="False">
            <Border Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="16">
                <StackPanel Width="250">
                    <TextBlock Text="格式设置" FontWeight="Medium" Margin="0,0,0,12"/>
                    
                    <!-- 数值格式 -->
                    <TextBlock Text="数值格式:" FontSize="12" Margin="0,0,0,4"/>
                    <ComboBox x:Name="NumberFormatComboBox" 
                              materialDesign:HintAssist.Hint="选择格式"
                              Margin="0,0,0,8">
                        <ComboBoxItem Content="默认" Tag="default"/>
                        <ComboBoxItem Content="整数" Tag="integer"/>
                        <ComboBoxItem Content="小数(2位)" Tag="decimal2"/>
                        <ComboBoxItem Content="小数(4位)" Tag="decimal4"/>
                        <ComboBoxItem Content="百分比" Tag="percentage"/>
                        <ComboBoxItem Content="科学计数法" Tag="scientific"/>
                    </ComboBox>
                    
                    <!-- 单位设置 -->
                    <TextBlock Text="单位:" FontSize="12" Margin="0,0,0,4"/>
                    <TextBox x:Name="UnitTextBox" 
                             materialDesign:HintAssist.Hint="输入单位"
                             Margin="0,0,0,8"/>
                    
                    <!-- 标签设置 -->
                    <TextBlock Text="标签:" FontSize="12" Margin="0,0,0,4"/>
                    <TextBox x:Name="LabelTextBox" 
                             materialDesign:HintAssist.Hint="输入标签"
                             Margin="0,0,0,12"/>
                    
                    <!-- 按钮 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="取消" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,8,0"
                                Click="OnFormatCancelClick"/>
                        <Button Content="应用" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Click="OnFormatApplyClick"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Popup>

        <!-- 材料库绑定弹出框 -->
        <Popup x:Name="MaterialLibraryPopup"
               PlacementTarget="{Binding ElementName=MaterialLibraryButton}"
               Placement="Bottom"
               AllowsTransparency="True"
               StaysOpen="False">
            <Border Background="{DynamicResource MaterialDesignPaper}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="16">
                <StackPanel Width="300">
                    <TextBlock Text="材料库绑定" FontWeight="Medium" Margin="0,0,0,12"/>
                    
                    <!-- 材料库选择 -->
                    <TextBlock Text="材料库:" FontSize="12" Margin="0,0,0,4"/>
                    <ComboBox x:Name="MaterialLibraryComboBox" 
                              materialDesign:HintAssist.Hint="选择材料库"
                              Margin="0,0,0,8"/>
                    
                    <!-- 字段映射 -->
                    <TextBlock Text="字段映射:" FontSize="12" Margin="0,0,0,4"/>
                    <DataGrid x:Name="FieldMappingGrid"
                              Height="120"
                              Margin="0,0,0,12">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="函数结果" Binding="{Binding FunctionField}" Width="*"/>
                            <DataGridComboBoxColumn Header="材料库字段" 
                                                    SelectedItemBinding="{Binding MaterialField}" 
                                                    Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                    
                    <!-- 按钮 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <Button Content="取消" 
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Margin="0,0,8,0"
                                Click="OnMaterialBindCancelClick"/>
                        <Button Content="绑定" 
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Click="OnMaterialBindApplyClick"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Popup>
    </DockPanel>
</UserControl>
