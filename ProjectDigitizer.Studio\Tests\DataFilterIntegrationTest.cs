using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Processors;
using ProjectDigitizer.Studio.Services;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Tests
{
    /// <summary>
    /// DataFilter 功能集成测试
    /// 验证筛选、统计和数据预览的完整流程
    /// </summary>
    public static class DataFilterIntegrationTest
    {
        /// <summary>
        /// 运行完整的集成测试
        /// </summary>
        public static void RunIntegrationTests()
        {
            Console.WriteLine("=== DataFilter 功能集成测试 ===");
            
            try
            {
                TestBasicFiltering();
                TestAggregationFunctions();
                TestDataPreviewPanel();
                TestCompleteWorkflow();
                TestPerformanceWithLargeData();
                
                Console.WriteLine("\n✅ 所有集成测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 集成测试失败: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试基础筛选功能
        /// </summary>
        private static void TestBasicFiltering()
        {
            Console.WriteLine("\n--- 测试基础筛选功能 ---");
            
            var processor = new DataFilterProcessor();
            var testData = CreateTestData();
            
            // 测试简单筛选
            var properties = new Dictionary<string, object?>
            {
                ["expressionMode"] = "simple",
                ["filterCondition"] = "score > 85",
                ["caseSensitive"] = false,
                ["filterType"] = "include",
                ["aggregationEnabled"] = false,
                ["displayMode"] = "none"
            };
            
            var result = processor.ProcessData(testData, properties);
            
            Console.WriteLine($"原始数据: {result.OriginalRecordCount} 条");
            Console.WriteLine($"筛选结果: {result.FilteredRecordCount} 条");
            Console.WriteLine($"执行状态: {(result.IsSuccess ? "成功" : "失败")}");
            Console.WriteLine($"执行时间: {result.ExecutionTime.TotalMilliseconds:F2}ms");
            
            if (!result.IsSuccess)
            {
                Console.WriteLine($"错误信息: {result.ErrorMessage}");
            }
            
            // 验证结果
            var expectedCount = testData.Count(row => 
                row.TryGetValue("score", out var score) && 
                score is int intScore && intScore > 85);
            
            if (result.FilteredRecordCount == expectedCount)
            {
                Console.WriteLine("✅ 筛选结果正确");
            }
            else
            {
                Console.WriteLine($"❌ 筛选结果错误，期望 {expectedCount}，实际 {result.FilteredRecordCount}");
            }
        }

        /// <summary>
        /// 测试统计函数
        /// </summary>
        private static void TestAggregationFunctions()
        {
            Console.WriteLine("\n--- 测试统计函数 ---");
            
            var processor = new DataFilterProcessor();
            var testData = CreateTestData();
            
            // 测试带统计的筛选
            var properties = new Dictionary<string, object?>
            {
                ["expressionMode"] = "advanced",
                ["filterCondition"] = "score >= 80",
                ["caseSensitive"] = false,
                ["filterType"] = "include",
                ["aggregationEnabled"] = true,
                ["aggregationFunction"] = "avg",
                ["aggregationField"] = "score",
                ["displayMode"] = "statistics"
            };
            
            var result = processor.ProcessData(testData, properties);
            
            Console.WriteLine($"筛选结果: {result.FilteredRecordCount} 条");
            Console.WriteLine($"统计结果: {(result.AggregationResult?.IsAllSuccess == true ? "成功" : "失败")}");
            
            if (result.AggregationResult != null)
            {
                foreach (var aggResult in result.AggregationResult.Results)
                {
                    Console.WriteLine($"  {aggResult.Description}");
                }
            }
        }

        /// <summary>
        /// 测试数据预览面板
        /// </summary>
        private static void TestDataPreviewPanel()
        {
            Console.WriteLine("\n--- 测试数据预览面板 ---");
            
            var viewModel = new DataPreviewViewModel();
            var testData = CreateTestData();
            
            // 设置数据
            viewModel.SetData(testData);
            
            Console.WriteLine($"总记录数: {viewModel.OriginalRecords}");
            Console.WriteLine($"当前页: {viewModel.CurrentPage}/{viewModel.TotalPages}");
            Console.WriteLine($"每页大小: {viewModel.PageSize}");
            Console.WriteLine($"显示行数: {viewModel.DisplayedRows.Count}");
            Console.WriteLine($"列数: {viewModel.ColumnNames.Count}");
            
            // 测试搜索
            viewModel.SearchText = "张";
            Console.WriteLine($"搜索 '张' 后记录数: {viewModel.TotalRecords}");
            
            // 测试分页
            if (viewModel.TotalPages > 1)
            {
                viewModel.CurrentPage = 2;
                Console.WriteLine($"切换到第2页，显示行数: {viewModel.DisplayedRows.Count}");
            }
            
            Console.WriteLine("✅ 数据预览面板测试通过");
        }

        /// <summary>
        /// 测试完整工作流程
        /// </summary>
        private static void TestCompleteWorkflow()
        {
            Console.WriteLine("\n--- 测试完整工作流程 ---");
            
            var processor = new DataFilterProcessor();
            var testData = CreateTestData();
            
            // 复杂筛选 + 统计 + 预览
            var properties = new Dictionary<string, object?>
            {
                ["expressionMode"] = "advanced",
                ["filterCondition"] = "(score > 80 && class == \"A\") || status == \"VIP\"",
                ["caseSensitive"] = false,
                ["filterType"] = "include",
                ["aggregationEnabled"] = true,
                ["aggregationFunction"] = "count",
                ["aggregationField"] = "",
                ["displayMode"] = "both",
                ["previewEnabled"] = true,
                ["maxPreviewRows"] = 50
            };
            
            var result = processor.ProcessData(testData, properties);
            
            Console.WriteLine($"处理摘要: {result.GetSummary()}");
            
            if (result.IsSuccess)
            {
                // 创建预览面板并设置数据
                var previewViewModel = new DataPreviewViewModel();
                previewViewModel.SetData(result.FilteredData);
                
                Console.WriteLine($"预览数据: {previewViewModel.OriginalRecords} 条记录");
                
                // 创建统计显示控件
                var statsWidget = new StatisticsDisplayWidget();
                if (result.AggregationResult != null)
                {
                    statsWidget.SetResults(result.AggregationResult);
                    Console.WriteLine("统计结果已设置到显示控件");
                }
                
                Console.WriteLine("✅ 完整工作流程测试通过");
            }
            else
            {
                Console.WriteLine($"❌ 工作流程失败: {result.ErrorMessage}");
            }
        }

        /// <summary>
        /// 测试大数据性能
        /// </summary>
        private static void TestPerformanceWithLargeData()
        {
            Console.WriteLine("\n--- 测试大数据性能 ---");
            
            var processor = new DataFilterProcessor();
            var largeData = GenerateLargeTestData(10000);
            
            var properties = new Dictionary<string, object?>
            {
                ["expressionMode"] = "advanced",
                ["filterCondition"] = "score > 75 && age >= 18",
                ["caseSensitive"] = false,
                ["filterType"] = "include",
                ["aggregationEnabled"] = true,
                ["aggregationFunction"] = "avg",
                ["aggregationField"] = "score",
                ["displayMode"] = "both"
            };
            
            var startTime = DateTime.Now;
            var result = processor.ProcessData(largeData, properties);
            var endTime = DateTime.Now;
            
            var totalTime = endTime - startTime;
            
            Console.WriteLine($"数据量: {largeData.Count} 条记录");
            Console.WriteLine($"处理时间: {totalTime.TotalMilliseconds:F2}ms");
            Console.WriteLine($"平均每条记录: {totalTime.TotalMilliseconds / largeData.Count:F4}ms");
            Console.WriteLine($"每秒处理记录数: {largeData.Count / totalTime.TotalSeconds:F0}");
            Console.WriteLine($"筛选结果: {result.FilteredRecordCount} 条");
            
            if (result.AggregationResult?.Results.Count > 0)
            {
                var avgResult = result.AggregationResult.Results[0];
                Console.WriteLine($"平均分数: {avgResult.FormattedValue}");
            }
            
            Console.WriteLine("✅ 大数据性能测试通过");
        }

        /// <summary>
        /// 创建测试数据
        /// </summary>
        private static List<Dictionary<string, object?>> CreateTestData()
        {
            return new List<Dictionary<string, object?>>
            {
                new() { ["id"] = 1, ["name"] = "张三", ["score"] = 95, ["class"] = "A", ["age"] = 20, ["status"] = "VIP" },
                new() { ["id"] = 2, ["name"] = "李四", ["score"] = 87, ["class"] = "B", ["age"] = 19, ["status"] = "Normal" },
                new() { ["id"] = 3, ["name"] = "王五", ["score"] = 92, ["class"] = "A", ["age"] = 21, ["status"] = "Normal" },
                new() { ["id"] = 4, ["name"] = "赵六", ["score"] = 78, ["class"] = "C", ["age"] = 18, ["status"] = "Normal" },
                new() { ["id"] = 5, ["name"] = "钱七", ["score"] = 88, ["class"] = "B", ["age"] = 20, ["status"] = "VIP" },
                new() { ["id"] = 6, ["name"] = "孙八", ["score"] = 82, ["class"] = "A", ["age"] = 19, ["status"] = "Normal" },
                new() { ["id"] = 7, ["name"] = "周九", ["score"] = 96, ["class"] = "A", ["age"] = 22, ["status"] = "VIP" },
                new() { ["id"] = 8, ["name"] = "吴十", ["score"] = 75, ["class"] = "C", ["age"] = 20, ["status"] = "Normal" }
            };
        }

        /// <summary>
        /// 生成大量测试数据
        /// </summary>
        private static List<Dictionary<string, object?>> GenerateLargeTestData(int count)
        {
            var random = new Random();
            var data = new List<Dictionary<string, object?>>();
            var names = new[] { "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十" };
            var classes = new[] { "A", "B", "C", "D" };
            var statuses = new[] { "VIP", "Normal", "Bronze" };
            
            for (int i = 0; i < count; i++)
            {
                data.Add(new Dictionary<string, object?>
                {
                    ["id"] = i + 1,
                    ["name"] = names[random.Next(names.Length)],
                    ["score"] = random.Next(0, 101),
                    ["class"] = classes[random.Next(classes.Length)],
                    ["age"] = random.Next(16, 26),
                    ["status"] = statuses[random.Next(statuses.Length)]
                });
            }
            
            return data;
        }
    }
}
