using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 数值属性编辑器
    /// </summary>
    public class NumberPropertyWidget : IPropertyWidget
    {
        private readonly TextBox _textBox;
        private PropertyDefinition _propertyDefinition = new();
        private object? _value;

        public NumberPropertyWidget()
        {
            _textBox = new TextBox
            {
                Margin = new Thickness(0, 8, 0, 16),
                FontSize = 14,
                Padding = new Thickness(8),
                BorderThickness = new Thickness(1),
                BorderBrush = System.Windows.Media.Brushes.LightGray,
                HorizontalContentAlignment = HorizontalAlignment.Right // 数字右对齐
            };

            // 安全地尝试应用MaterialDesign样式
            try
            {
                if (Application.Current.Resources.Contains("MaterialDesignFloatingHintTextBox"))
                {
                    _textBox.Style = Application.Current.Resources["MaterialDesignFloatingHintTextBox"] as Style;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to apply MaterialDesign style: {ex.Message}");
            }

            // 限制只能输入数字
            _textBox.PreviewTextInput += OnPreviewTextInput;
            _textBox.TextChanged += OnTextChanged;

            // 处理粘贴事件
            DataObject.AddPastingHandler(_textBox, OnPasting);
        }

        public PropertyDefinition PropertyDefinition
        {
            get => _propertyDefinition;
            set
            {
                _propertyDefinition = value;
                UpdateUI();
            }
        }

        public object? Value
        {
            get => _value;
            set
            {
                if (_value != value)
                {
                    var oldValue = _value;
                    _value = value;
                    _textBox.Text = FormatValue(value);
                    ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                        PropertyDefinition.Name, oldValue, value));
                }
            }
        }

        public bool IsEnabled
        {
            get => _textBox.IsEnabled;
            set => _textBox.IsEnabled = value;
        }

        public event EventHandler<PropertyValueChangedEventArgs>? ValueChanged;

        public Models.ValidationResult Validate()
        {
            var result = new Models.ValidationResult();
            var text = _textBox.Text.Trim();

            // 必填验证
            if (PropertyDefinition.Required && string.IsNullOrWhiteSpace(text))
            {
                result.AddError($"{PropertyDefinition.Title} 是必填项");
                return result;
            }

            // 如果不是必填且为空，则通过验证
            if (string.IsNullOrWhiteSpace(text))
            {
                return result;
            }

            // 数值格式验证
            if (!double.TryParse(text, out double numValue))
            {
                result.AddError($"{PropertyDefinition.Title} 必须是有效的数值");
                return result;
            }

            // 范围验证
            if (PropertyDefinition.Minimum.HasValue && numValue < PropertyDefinition.Minimum.Value)
            {
                result.AddError($"{PropertyDefinition.Title} 不能小于 {PropertyDefinition.Minimum.Value}");
                return result;
            }

            if (PropertyDefinition.Maximum.HasValue && numValue > PropertyDefinition.Maximum.Value)
            {
                result.AddError($"{PropertyDefinition.Title} 不能大于 {PropertyDefinition.Maximum.Value}");
                return result;
            }

            return result;
        }

        public FrameworkElement GetElement()
        {
            return _textBox;
        }

        private void UpdateUI()
        {
            _textBox.Text = FormatValue(PropertyDefinition.DefaultValue);

            // 安全地设置MaterialDesign的浮动提示
            try
            {
                if (!string.IsNullOrEmpty(PropertyDefinition.Title))
                {
                    _textBox.SetValue(MaterialDesignThemes.Wpf.HintAssist.HintProperty, PropertyDefinition.Title);
                }

                // 设置帮助文本，包含范围信息
                var helpText = PropertyDefinition.Description ?? "";
                if (PropertyDefinition.Minimum.HasValue || PropertyDefinition.Maximum.HasValue)
                {
                    var rangeText = "";
                    if (PropertyDefinition.Minimum.HasValue && PropertyDefinition.Maximum.HasValue)
                    {
                        rangeText = $"范围: {PropertyDefinition.Minimum.Value} - {PropertyDefinition.Maximum.Value}";
                    }
                    else if (PropertyDefinition.Minimum.HasValue)
                    {
                        rangeText = $"最小值: {PropertyDefinition.Minimum.Value}";
                    }
                    else if (PropertyDefinition.Maximum.HasValue)
                    {
                        rangeText = $"最大值: {PropertyDefinition.Maximum.Value}";
                    }

                    helpText = string.IsNullOrEmpty(helpText) ? rangeText : $"{helpText} ({rangeText})";
                }

                if (!string.IsNullOrEmpty(helpText))
                {
                    _textBox.SetValue(MaterialDesignThemes.Wpf.HintAssist.HelperTextProperty, helpText);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to set MaterialDesign properties: {ex.Message}");
                // 回退到基本的ToolTip
                if (!string.IsNullOrEmpty(PropertyDefinition.Description))
                {
                    _textBox.ToolTip = PropertyDefinition.Description;
                }
            }
        }

        private string FormatValue(object? value)
        {
            if (value == null) return string.Empty;

            if (value is double d)
                return d.ToString(CultureInfo.InvariantCulture);
            if (value is float f)
                return f.ToString(CultureInfo.InvariantCulture);
            if (value is int i)
                return i.ToString();
            if (value is long l)
                return l.ToString();

            return value.ToString() ?? string.Empty;
        }

        private void OnPreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            // 允许数字、小数点、负号
            var text = e.Text;
            var currentText = _textBox.Text;
            var selectionStart = _textBox.SelectionStart;

            // 检查输入的字符
            foreach (char c in text)
            {
                if (!char.IsDigit(c) && c != '.' && c != '-')
                {
                    e.Handled = true;
                    return;
                }

                // 负号只能在开头
                if (c == '-' && selectionStart != 0)
                {
                    e.Handled = true;
                    return;
                }

                // 只能有一个小数点
                if (c == '.' && currentText.Contains('.'))
                {
                    e.Handled = true;
                    return;
                }
            }
        }

        private void OnPasting(object sender, DataObjectPastingEventArgs e)
        {
            if (e.DataObject.GetDataPresent(typeof(string)))
            {
                var text = (string)e.DataObject.GetData(typeof(string));
                if (!double.TryParse(text, out _))
                {
                    e.CancelCommand();
                }
            }
            else
            {
                e.CancelCommand();
            }
        }

        private void OnTextChanged(object sender, TextChangedEventArgs e)
        {
            var text = _textBox.Text;
            var oldValue = _value;

            if (string.IsNullOrWhiteSpace(text))
            {
                _value = null;
            }
            else if (double.TryParse(text, out double numValue))
            {
                _value = numValue;
            }
            else
            {
                // 如果解析失败，保持原值不变
                return;
            }

            ValueChanged?.Invoke(this, new PropertyValueChangedEventArgs(
                PropertyDefinition.Name, oldValue, _value));
        }
    }
}
