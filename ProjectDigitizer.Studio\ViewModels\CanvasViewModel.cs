using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Threading.Tasks;
using Nodify; // 确保Nodify命名空间被引用
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Services;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ProjectDigitizer.Studio.ViewModels
{
  /// <summary>
  /// 画布视图模型 - 包含性能优化功能
  /// </summary>
  public class CanvasViewModel : INotifyPropertyChanged
  {
    private readonly CanvasVirtualizationManager _virtualizationManager;
    private readonly PerformanceOptimizedConnectorManager _connectorManager;
    private readonly NodeLayoutService _layoutService;
    private readonly ConnectionStyleSyncService _connectionStyleSyncService;

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
      PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
      if (EqualityComparer<T>.Default.Equals(field, value)) return false;
      field = value;
      OnPropertyChanged(propertyName);
      return true;
    }

    private ModuleNodeViewModel? _selectedNode;
    private bool _isPropertyPanelVisible;
    private Point _viewportCenter = new Point(400, 300); // 默认视口中心
    private double _viewportZoom = 1.0; // 默认缩放级别
    private Size _actualCanvasSize = new Size(1200, 800); // 实际画布尺寸
    private Rect _actualViewport = new Rect(0, 0, 1200, 800); // 实际可见视口

    /// <summary>
    /// 节点集合
    /// </summary>
    public ObservableCollection<ModuleNodeViewModel> Nodes { get; }

    /// <summary>
    /// 连接线集合
    /// </summary>
    public ObservableCollection<ConnectionViewModel> Connections { get; }

    /// <summary>
    /// 选中的节点集合（支持多选）
    /// </summary>
    public ObservableCollection<object> SelectedItems { get; }

    /// <summary>
    /// 属性面板是否可见
    /// </summary>
    public bool IsPropertyPanelVisible
    {
      get => _isPropertyPanelVisible;
      set => SetProperty(ref _isPropertyPanelVisible, value);
    }

    /// <summary>
    /// 选中的节点（单选）
    /// </summary>
    public ModuleNodeViewModel? SelectedNode
    {
      get => _selectedNode;
      set
      {
        if (_selectedNode != value)
        {
          _selectedNode = value;
          OnPropertyChanged();
        }
      }
    }

    /// <summary>
    /// 视口中心位置
    /// </summary>
    public Point ViewportCenter
    {
      get => _viewportCenter;
      set => SetProperty(ref _viewportCenter, value);
    }

    /// <summary>
    /// 视口缩放级别
    /// </summary>
    public double ViewportZoom
    {
      get => _viewportZoom;
      set => SetProperty(ref _viewportZoom, value);
    }

    /// <summary>
    /// 实际画布尺寸
    /// </summary>
    public Size ActualCanvasSize
    {
      get => _actualCanvasSize;
      set => SetProperty(ref _actualCanvasSize, value);
    }

    /// <summary>
    /// 实际可见视口
    /// </summary>
    public Rect ActualViewport
    {
      get => _actualViewport;
      set => SetProperty(ref _actualViewport, value);
    }

    /// <summary>
    /// 连接线样式同步服务
    /// </summary>
    public ConnectionStyleSyncService ConnectionStyleSyncService => _connectionStyleSyncService;

    public CanvasViewModel()
    {
      // 初始化性能优化组件
      _virtualizationManager = new CanvasVirtualizationManager();
      _connectorManager = new PerformanceOptimizedConnectorManager();
      _layoutService = new NodeLayoutService();
      _connectionStyleSyncService = new ConnectionStyleSyncService();

      Nodes = new ObservableCollection<ModuleNodeViewModel>();
      Connections = new ObservableCollection<ConnectionViewModel>();
      SelectedItems = new ObservableCollection<object>();
      AddModuleCommand = new DelegateCommand<TemplateItem>(ExecuteAddModule, CanExecuteAddModule);
      PendingConnection = new PendingConnectionViewModel();
      DisconnectConnectorCommand = new DelegateCommand<ConnectorViewModel>(ExecuteDisconnectConnector);
      CreateConnectionCommand = new DelegateCommand<object>(
        target =>
        {
          var targetConnector = target as ConnectorViewModel;
          PendingConnection.Target = targetConnector;
          CreateConnection(PendingConnection.Source, targetConnector);
        },
        target => CanCreateConnection(PendingConnection.Source, target as ConnectorViewModel));
      StartConnectionCommand = new DelegateCommand<ConnectorViewModel>(
        connector =>
        {
          PendingConnection.Source = connector;
          PendingConnection.IsVisible = true;
        },
        c => c != null && (c.SupportsMultipleConnections || !c.IsConnected || !c.IsInput));
      RemoveConnectionCommand = new DelegateCommand<ConnectionViewModel>(ExecuteRemoveConnection);

      // 设置PendingConnection的命令
      PendingConnection.StartCommand = StartConnectionCommand;
      PendingConnection.FinishCommand = CreateConnectionCommand;

      InitializeTemplateCategories();

      // 启用多选集合变化监听
      SelectedItems.CollectionChanged += SelectedItems_CollectionChanged;

      // 启用虚拟化 - 监听节点集合变化
      Nodes.CollectionChanged += (s, e) =>
      {
        if (e.NewItems != null)
        {
          foreach (ModuleNodeViewModel node in e.NewItems)
          {
            _virtualizationManager.AddNode(node);
          }
        }

        if (e.OldItems != null)
        {
          foreach (ModuleNodeViewModel node in e.OldItems)
          {
            _virtualizationManager.RemoveNode(node);
          }
        }
      };
    }

    /// <summary>
    /// 添加一个新模块到画布
    /// </summary>
    /// <param name="moduleType">模块类型</param>
    /// <param name="position">位置</param>
    /// <returns>添加的节点</returns>
    public ModuleNodeViewModel AddModule(ModuleType moduleType, Point position)
    {
      // 确保位置在可见区域内，避免被UI元素遮挡
      var adjustedPosition = EnsureNodeVisibility(position);

      // 创建模块模型
      var module = new ModuleModel
      {
        Type = moduleType,
        NodeType = NodeTypeRegistry.GetNodeType(moduleType),
        Name = GetModuleTypeName(moduleType),
        Description = $"{GetModuleTypeName(moduleType)}模块"
      };

      // 创建节点视图模型
      var nodeViewModel = new ModuleNodeViewModel
      {
        Module = module,
        Location = adjustedPosition
      };

      // 订阅节点启用状态变化事件
      nodeViewModel.EnabledChanged += OnNodeEnabledChanged;

      // 添加到集合
      Nodes.Add(nodeViewModel);

      // 直接设置选中状态，不调用任何方法
      SelectedNode = nodeViewModel;
      IsPropertyPanelVisible = true;

      System.Diagnostics.Debug.WriteLine($"新节点已添加: {nodeViewModel.Title} 位置: ({adjustedPosition.X:F1}, {adjustedPosition.Y:F1})");

      return nodeViewModel;
    }

    /// <summary>
    /// 移除节点
    /// </summary>
    /// <param name="node">要移除的节点</param>
    public void RemoveNode(ModuleNodeViewModel node)
    {
      // 取消订阅事件
      node.EnabledChanged -= OnNodeEnabledChanged;

      // 移除与该节点相关的所有连接
      var connectionsToRemove = Connections
          .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
          .ToList();

      foreach (var connection in connectionsToRemove)
      {
        Connections.Remove(connection);
      }

      // 移除节点
      Nodes.Remove(node);

      // 从选择集合中移除
      if (SelectedItems.Contains(node))
      {
        SelectedItems.Remove(node);
      }

      // 如果移除的是当前选中的节点，清除选中
      if (SelectedNode == node)
      {
        SelectedNode = null;
      }

      // 更新属性面板可见性
      IsPropertyPanelVisible = SelectedNode != null;
    }

    /// <summary>
    /// 节点启用状态变化事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="isEnabled">是否启用</param>
    private void OnNodeEnabledChanged(object? sender, bool isEnabled)
    {
      if (sender is ModuleNodeViewModel node)
      {
        if (!isEnabled)
        {
          // 节点被禁用时，禁用所有连接到该节点的连接线
          DisableNodeConnections(node);
          System.Diagnostics.Debug.WriteLine($"节点 {node.Title} 已禁用，禁用相关连接线");
        }
        else
        {
          // 节点被启用时，检查并可能重新启用相关连接线
          EnableNodeConnections(node);
          System.Diagnostics.Debug.WriteLine($"节点 {node.Title} 已启用，检查相关连接线状态");
        }
      }
    }

    /// <summary>
    /// 禁用节点的所有连接线
    /// </summary>
    /// <param name="node">节点</param>
    private void DisableNodeConnections(ModuleNodeViewModel node)
    {
      var nodeConnections = Connections
          .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
          .ToList();

      foreach (var connection in nodeConnections)
      {
        connection.IsEnabled = false;
        System.Diagnostics.Debug.WriteLine($"  禁用连接线: {connection.GetConnectionDescription()}");
      }
    }

    /// <summary>
    /// 启用节点的相关连接线（仅当两端节点都启用时）
    /// </summary>
    /// <param name="node">节点</param>
    private void EnableNodeConnections(ModuleNodeViewModel node)
    {
      var nodeConnections = Connections
          .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
          .ToList();

      foreach (var connection in nodeConnections)
      {
        // 只有当连接的两端节点都启用时，才启用连接线
        if (connection.CanBeEnabledBasedOnNodes())
        {
          connection.IsEnabled = true;
          System.Diagnostics.Debug.WriteLine($"  启用连接线: {connection.GetConnectionDescription()}");
        }
      }
    }

    /// <summary>
    /// 移除节点的所有连接（用于删除节点时）
    /// </summary>
    /// <param name="node">节点</param>
    private void RemoveNodeConnections(ModuleNodeViewModel node)
    {
      var connectionsToRemove = Connections
          .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
          .ToList();

      foreach (var connection in connectionsToRemove)
      {
        // 更新连接器状态
        if (connection.Source != null)
        {
          connection.Source.IsConnected = false;
        }
        if (connection.Target != null)
        {
          connection.Target.IsConnected = false;
        }

        // 移除连接
        Connections.Remove(connection);
      }
    }

    /// <summary>
    /// 批量移除选中的节点
    /// </summary>
    public void RemoveSelectedNodes()
    {
      var selectedNodes = SelectedItems.OfType<ModuleNodeViewModel>().ToList();

      if (selectedNodes.Count == 0)
        return;

      // 批量移除节点
      foreach (var node in selectedNodes)
      {
        // 移除与该节点相关的所有连接
        var connectionsToRemove = Connections
            .Where(c => (c.Source?.Node as ModuleNodeViewModel) == node || (c.Target?.Node as ModuleNodeViewModel) == node)
            .ToList();

        foreach (var connection in connectionsToRemove)
        {
          Connections.Remove(connection);
        }

        // 移除节点
        Nodes.Remove(node);
      }

      // 清除选择状态
      ClearSelection();
    }

    /// <summary>
    /// 添加连接
    /// </summary>
    /// <param name="source">源连接点</param>
    /// <param name="target">目标连接点</param>
    /// <returns>成功返回true，否则返回false</returns>
    public bool AddConnection(ConnectorViewModel source, ConnectorViewModel target)
    {
      // 检查连接是否已存在
      if (Connections.Any(c => c.Source == source && c.Target == target))
      {
        return false;
      }

      // 创建连接
      var connection = new ConnectionViewModel(source, target);

      // 添加到集合
      Connections.Add(connection);
      return true;
    }



    /// <summary>
    /// 切换连接线的启用状态
    /// </summary>
    /// <param name="connection">连接线</param>
    public void ToggleConnectionEnabled(ConnectionViewModel connection)
    {
      if (connection.IsEnabled)
      {
        // 禁用连接线
        connection.IsEnabled = false;
        System.Diagnostics.Debug.WriteLine($"手动禁用连接线: {connection.GetConnectionDescription()}");
      }
      else
      {
        // 启用连接线（需要检查节点状态）
        if (connection.CanBeEnabledBasedOnNodes())
        {
          connection.IsEnabled = true;
          System.Diagnostics.Debug.WriteLine($"手动启用连接线: {connection.GetConnectionDescription()}");
        }
        else
        {
          System.Diagnostics.Debug.WriteLine($"无法启用连接线（节点被禁用）: {connection.GetConnectionDescription()}");
        }
      }
    }

    /// <summary>
    /// 创建连接（按照Nodify官方示例模式）
    /// </summary>
    /// <param name="source">源连接器</param>
    /// <param name="target">目标连接器</param>
    internal void CreateConnection(ConnectorViewModel? source, ConnectorViewModel? target)
    {
      System.Diagnostics.Debug.WriteLine($"CreateConnection called: source={source?.Title}, target={target?.Title}");

      if (source == null || target == null)
      {
        System.Diagnostics.Debug.WriteLine("Source or target is null, keeping pending connection visible");
        PendingConnection.IsVisible = true;
        return;
      }

      // 确保source是输出点，target是输入点
      if (source.IsInput && !target.IsInput)
      {
        var temp = source;
        source = target;
        target = temp;
      }

      // 验证连接的有效性
      if (!CanCreateConnection(source, target))
      {
        System.Diagnostics.Debug.WriteLine($"无效的连接尝试: {source.Title} -> {target.Title}");
        return;
      }

      // 检查是否已存在相同的连接
      if (Connections.Any(c => (c.Source == source && c.Target == target) ||
                             (c.Source == target && c.Target == source)))
      {
        System.Diagnostics.Debug.WriteLine($"连接已存在: {source.Title} -> {target.Title}");
        return;
      }

      // 检查目标连接器是否支持多连接
      if (!target.CanAddConnection())
      {
        System.Diagnostics.Debug.WriteLine($"目标连接器不支持多连接，断开现有连接: {target.Title}");
        DisconnectConnector(target);
      }

      try
      {
        // 创建新连接
        var connection = new ConnectionViewModel(source, target)
        {
          Id = Guid.NewGuid().ToString()
        };

        // 添加到集合（连接状态由ConnectorViewModel自动管理）
        Connections.Add(connection);

        System.Diagnostics.Debug.WriteLine($"连接创建成功: {source.Title} -> {target.Title}");
      }
      catch (Exception ex)
      {
        System.Diagnostics.Debug.WriteLine($"创建连接时发生错误: {ex.Message}");
      }
      finally
      {
        // 隐藏待定连接
        PendingConnection.IsVisible = false;
      }
    }

    /// <summary>
    /// 检查是否可以创建连接
    /// </summary>
    internal bool CanCreateConnection(ConnectorViewModel? source, ConnectorViewModel? target)
    {
      if (source == null || target == null)
      {
        return false;
      }

      // 基本验证
      if (source == target || source.Node == target.Node)
      {
        return false;
      }

      // 确保一个是输入，一个是输出
      if (source.IsInput == target.IsInput)
      {
        return false;
      }

      // 检查数据类型兼容性（如果已设置）
      if (!string.IsNullOrEmpty(source.DataType) &&
          !string.IsNullOrEmpty(target.DataType) &&
          source.DataType != target.DataType)
      {
        return false;
      }

      // 检查是否会形成循环
      if (WouldCreateCycle(source, target))
      {
        return false;
      }

      return true;
    }

    /// <summary>
    /// 检查添加连接是否会形成循环
    /// </summary>
    private bool WouldCreateCycle(ConnectorViewModel source, ConnectorViewModel target)
    {
      // 获取起点和终点节点
      var sourceNode = source.Node as ModuleNodeViewModel;
      var targetNode = target.Node as ModuleNodeViewModel;

      if (sourceNode == null || targetNode == null)
      {
        return false;
      }

      // 如果source是输入点，交换起点和终点
      if (source.IsInput)
      {
        var temp = sourceNode;
        sourceNode = targetNode;
        targetNode = temp;
      }

      // 使用HashSet来跟踪已访问的节点
      var visited = new HashSet<ModuleNodeViewModel>();
      return HasCycle(sourceNode, targetNode, visited);
    }

    /// <summary>
    /// 递归检查是否存在循环
    /// </summary>
    private bool HasCycle(ModuleNodeViewModel current, ModuleNodeViewModel target, HashSet<ModuleNodeViewModel> visited)
    {
      if (current == target)
      {
        return true;
      }

      if (!visited.Add(current))
      {
        return false;
      }

      // 获取当前节点的所有输出连接
      var connections = Connections.Where(c =>
          (c.Source?.Node as ModuleNodeViewModel) == current);

      foreach (var conn in connections)
      {
        var nextNode = conn.Target?.Node as ModuleNodeViewModel;
        if (nextNode != null && HasCycle(nextNode, target, visited))
        {
          return true;
        }
      }

      return false;
    }

    /// <summary>
    /// 断开连接器的所有连接
    /// </summary>
    public void DisconnectConnector(ConnectorViewModel connector)
    {
      var connectionsToRemove = Connections.Where(c =>
          c.Source == connector || c.Target == connector).ToList();

      foreach (var connection in connectionsToRemove)
      {
        Connections.Remove(connection);
      }

      // 清除连接器的连接记录（连接状态会自动更新）
      connector.ClearConnections();
    }

    /// <summary>
    /// 移除单个连接
    /// </summary>
    /// <param name="connection">要移除的连接</param>
    public void RemoveConnection(ConnectionViewModel connection)
    {
      if (connection != null && Connections.Contains(connection))
      {
        // 从集合中移除
        Connections.Remove(connection);

        // 从连接器中移除连接记录（连接状态会自动更新）
        connection.Source?.RemoveConnection(connection);
        connection.Target?.RemoveConnection(connection);
      }
    }

    /// <summary>
    /// 清空画布
    /// </summary>
    public void Clear()
    {
      Connections.Clear();
      Nodes.Clear();
      SelectedNode = null;
      SelectedItems.Clear();

      // 更新属性面板可见性
      IsPropertyPanelVisible = false;
    }

    /// <summary>
    /// 设置所有模块的启用状态
    /// </summary>
    /// <param name="isEnabled">是否启用</param>
    public void SetAllModulesEnabled(bool isEnabled)
    {
      foreach (var node in Nodes)
      {
        if (node.Module != null)
        {
          node.Module.IsEnabled = isEnabled;
        }
      }
    }

    /// <summary>
    /// 隐藏所有关闭的模块
    /// </summary>
    public void HideClosedModules()
    {
      foreach (var node in Nodes)
      {
        if (node.Module != null && !node.Module.IsEnabled)
        {
          node.IsLightBulbOn = false; // IsLightBulbOn 是 ModuleNodeViewModel 的属性
        }
      }
    }

    /// <summary>
    /// 模板分类集合
    /// </summary>
    public ObservableCollection<TemplateCategory> TemplateCategories { get; } = new ObservableCollection<TemplateCategory>();

    /// <summary>
    /// 添加模块命令
    /// </summary>
    public ICommand AddModuleCommand { get; }

    /// <summary>
    /// 挂起连接视图模型
    /// </summary>
    public PendingConnectionViewModel PendingConnection { get; }

    /// <summary>
    /// 断开连接器命令
    /// </summary>
    public ICommand DisconnectConnectorCommand { get; }

    /// <summary>
    /// 执行断开连接器命令
    /// </summary>
    /// <param name="connector">要断开的连接器</param>
    private void ExecuteDisconnectConnector(ConnectorViewModel connector)
    {
      if (connector != null)
      {
        DisconnectConnector(connector);
        System.Diagnostics.Debug.WriteLine($"已断开连接器: {connector.Title}");
      }
    }

    /// <summary>
    /// 执行移除连接命令
    /// </summary>
    /// <param name="connection">要移除的连接</param>
    private void ExecuteRemoveConnection(ConnectionViewModel connection)
    {
      if (connection != null)
      {
        RemoveConnection(connection);
        System.Diagnostics.Debug.WriteLine($"已移除连接: {connection.Source?.Title} -> {connection.Target?.Title}");
      }
    }

    /// <summary>
    /// 创建连接命令
    /// </summary>
    public ICommand CreateConnectionCommand { get; }

    /// <summary>
    /// 开始连接命令
    /// </summary>
    public ICommand StartConnectionCommand { get; }

    /// <summary>
    /// 移除连接命令
    /// </summary>
    public ICommand RemoveConnectionCommand { get; }

    /// <summary>
    /// 清除选择
    /// </summary>
    public void ClearSelection()
    {
      // 只清除ViewModel的状态，不手动操作IsSelected
      // 让Nodify的内置机制处理IsSelected属性
      SelectedNode = null;
      SelectedItems.Clear();
      IsPropertyPanelVisible = false;
    }

    /// <summary>
    /// 选择单个节点
    /// </summary>
    /// <param name="node">要选择的节点</param>
    public void SelectSingleNode(ModuleNodeViewModel node)
    {
      // 清除所有选择
      ClearSelection();

      // 选中指定节点
      node.IsSelected = true;
      SelectedNode = node;
      SelectedItems.Add(node);
      IsPropertyPanelVisible = true;
    }

    /// <summary>
    /// 切换节点的多选状态
    /// </summary>
    /// <param name="node">要切换的节点</param>
    public void ToggleNodeSelection(ModuleNodeViewModel node)
    {
      if (SelectedItems.Contains(node))
      {
        // 取消选择
        SelectedItems.Remove(node);
        node.IsSelected = false;
      }
      else
      {
        // 添加到选择
        SelectedItems.Add(node);
        node.IsSelected = true;
      }

      // 手动更新选择状态
      UpdateSelectionState();
    }

    /// <summary>
    /// 多选集合变化事件处理
    /// </summary>
    private void SelectedItems_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
    {
      // 安全地更新选择状态
      try
      {
        UpdateSelectionState();
      }
      catch (Exception ex)
      {
        System.Diagnostics.Debug.WriteLine($"Error in SelectedItems_CollectionChanged: {ex.Message}");
      }
    }

    /// <summary>
    /// 更新选择状态和属性面板可见性
    /// </summary>
    public void UpdateSelectionState()
    {
      if (SelectedItems.Count == 1)
      {
        // 单选：显示属性面板
        var selectedNode = SelectedItems.Cast<ModuleNodeViewModel>().First();
        SelectedNode = selectedNode;
        IsPropertyPanelVisible = true;
      }
      else
      {
        // 多选或无选择：隐藏属性面板
        SelectedNode = null;
        IsPropertyPanelVisible = false;
      }
    }

    /// <summary>
    /// 执行添加模块命令
    /// </summary>
    /// <param name="templateItem">模板项</param>
    private void ExecuteAddModule(TemplateItem templateItem)
    {
      if (templateItem != null)
      {
        // 获取智能位置，确保新节点在可见区域内
        var position = GetSmartNodePosition();
        AddModule(templateItem.ModuleType, position);
      }
    }

    /// <summary>
    /// 判断是否可以执行添加模块命令
    /// </summary>
    /// <param name="templateItem">模板项</param>
    /// <returns>是否可以执行</returns>
    private bool CanExecuteAddModule(TemplateItem templateItem)
    {
      return templateItem != null;
    }

    /// <summary>
    /// 获取智能节点位置，确保新节点在可见区域内且不重叠
    /// </summary>
    /// <returns>新节点的位置</returns>
    private Point GetSmartNodePosition()
    {
      // 基础位置：视口中心偏移一些距离，避免正好在中心
      var baseX = ViewportCenter.X - 125; // 稍微偏左（节点宽度的一半）
      var baseY = ViewportCenter.Y - 60;  // 稍微偏上（节点高度的一半）

      // 节点的大小估算（用于避免重叠）
      const double nodeWidth = 250;
      const double nodeHeight = 120;
      const double horizontalSpacing = 50; // 水平间距增加
      const double verticalSpacing = 40;   // 垂直间距增加

      // 如果没有现有节点，直接返回基础位置
      if (Nodes.Count == 0)
      {
        return new Point(baseX, baseY);
      }

      // 尝试在视口可见区域内找到不重叠的位置
      var attempts = 0;
      const int maxAttempts = 30; // 减少尝试次数，使用更清晰的网格
      const int columnsPerRow = 3; // 每行3列，更整齐

      while (attempts < maxAttempts)
      {
        // 计算候选位置 - 使用更清晰的网格布局
        var column = attempts % columnsPerRow;
        var row = attempts / columnsPerRow;

        var candidateX = baseX + column * (nodeWidth + horizontalSpacing);
        var candidateY = baseY + row * (nodeHeight + verticalSpacing);

        var candidatePosition = new Point(candidateX, candidateY);

        // 使用矩形重叠检测而不是圆形距离检测
        bool hasOverlap = false;
        foreach (var existingNode in Nodes)
        {
          if (IsRectangleOverlap(
              candidatePosition, nodeWidth, nodeHeight,
              existingNode.Location, nodeWidth, nodeHeight,
              horizontalSpacing / 2)) // 使用间距的一半作为最小距离
          {
            hasOverlap = true;
            break;
          }
        }

        if (!hasOverlap)
        {
          System.Diagnostics.Debug.WriteLine($"找到不重叠位置: 第{attempts + 1}次尝试, 位置({candidateX:F1}, {candidateY:F1})");
          return candidatePosition;
        }

        attempts++;
      }

      // 如果找不到不重叠的位置，使用强制偏移策略
      var fallbackX = baseX + (Nodes.Count % 8) * 40;
      var fallbackY = baseY + (Nodes.Count / 8) * 50;

      System.Diagnostics.Debug.WriteLine($"使用后备位置: ({fallbackX:F1}, {fallbackY:F1})");
      return new Point(fallbackX, fallbackY);
    }

    /// <summary>
    /// 检查两个矩形是否重叠（考虑最小间距）
    /// </summary>
    /// <param name="pos1">第一个矩形位置</param>
    /// <param name="width1">第一个矩形宽度</param>
    /// <param name="height1">第一个矩形高度</param>
    /// <param name="pos2">第二个矩形位置</param>
    /// <param name="width2">第二个矩形宽度</param>
    /// <param name="height2">第二个矩形高度</param>
    /// <param name="minDistance">最小间距</param>
    /// <returns>是否重叠</returns>
    private static bool IsRectangleOverlap(Point pos1, double width1, double height1,
                                         Point pos2, double width2, double height2,
                                         double minDistance)
    {
      // 扩展矩形边界以包含最小间距
      var left1 = pos1.X - minDistance;
      var right1 = pos1.X + width1 + minDistance;
      var top1 = pos1.Y - minDistance;
      var bottom1 = pos1.Y + height1 + minDistance;

      var left2 = pos2.X;
      var right2 = pos2.X + width2;
      var top2 = pos2.Y;
      var bottom2 = pos2.Y + height2;

      // 检查是否重叠
      return !(right1 <= left2 || left1 >= right2 || bottom1 <= top2 || top1 >= bottom2);
    }

    /// <summary>
    /// 更新视口信息（由UI层调用）
    /// </summary>
    /// <param name="center">视口中心</param>
    /// <param name="zoom">缩放级别</param>
    /// <param name="canvasSize">画布实际尺寸</param>
    /// <param name="viewport">实际可见视口</param>
    public void UpdateViewportInfo(Point center, double zoom, Size? canvasSize = null, Rect? viewport = null)
    {
      ViewportCenter = center;
      ViewportZoom = zoom;

      // 更新实际画布尺寸
      if (canvasSize.HasValue)
      {
        ActualCanvasSize = canvasSize.Value;
      }

      // 更新实际可见视口
      if (viewport.HasValue)
      {
        ActualViewport = viewport.Value;
        // 更新虚拟化管理器的视口信息
        _virtualizationManager.UpdateViewport(viewport.Value, zoom);
      }
      else
      {
        // 如果没有提供视口信息，使用默认计算方式
        var defaultViewport = new Rect(center.X - 400, center.Y - 300, 800, 600);
        ActualViewport = defaultViewport;
        _virtualizationManager.UpdateViewport(defaultViewport, zoom);
      }
    }

    /// <summary>
    /// 计算所有节点的边界矩形
    /// </summary>
    /// <returns>包含所有节点的边界矩形</returns>
    public Rect CalculateNodesBounds()
    {
      if (!Nodes.Any())
        return new Rect(0, 0, 0, 0);

      var minX = double.MaxValue;
      var minY = double.MaxValue;
      var maxX = double.MinValue;
      var maxY = double.MinValue;

      const double nodeWidth = 250;
      const double nodeHeight = 120;

      foreach (var node in Nodes)
      {
        var x = node.Location.X;
        var y = node.Location.Y;

        minX = Math.Min(minX, x);
        minY = Math.Min(minY, y);
        maxX = Math.Max(maxX, x + nodeWidth);
        maxY = Math.Max(maxY, y + nodeHeight);
      }

      // 添加一些边距
      const double margin = 50;
      return new Rect(
        minX - margin,
        minY - margin,
        maxX - minX + 2 * margin,
        maxY - minY + 2 * margin
      );
    }

    /// <summary>
    /// 自动调整视图以适应所有节点
    /// </summary>
    public event Action<Rect>? FitToNodesRequested;

    /// <summary>
    /// 触发适应所有节点的视图调整
    /// </summary>
    public void FitToNodes()
    {
      var bounds = CalculateNodesBounds();
      if (bounds.Width > 0 && bounds.Height > 0)
      {
        FitToNodesRequested?.Invoke(bounds);
      }
    }

    /// <summary>
    /// 确保节点位置在可见区域内，避免被UI元素遮挡
    /// </summary>
    /// <param name="originalPosition">原始位置</param>
    /// <returns>调整后的位置</returns>
    private Point EnsureNodeVisibility(Point originalPosition)
    {
      var adjustedX = originalPosition.X;
      var adjustedY = originalPosition.Y;

      // 节点大小估算
      const double nodeWidth = 250;
      const double nodeHeight = 120;
      const double margin = 20; // 边距

      // 确保节点不会超出视口边界（考虑缩放）
      var viewportWidth = 800; // 默认视口宽度
      var viewportHeight = 600; // 默认视口高度

      // 左边界检查（避免被左侧模板面板遮挡）
      var leftPanelWidth = 250; // 左侧面板宽度
      if (adjustedX < leftPanelWidth + margin)
      {
        adjustedX = leftPanelWidth + margin;
      }

      // 右边界检查（避免被右侧属性面板遮挡）
      var rightPanelWidth = IsPropertyPanelVisible ? 300 : 0; // 右侧面板宽度
      var maxX = viewportWidth - rightPanelWidth - nodeWidth - margin;
      if (adjustedX > maxX)
      {
        adjustedX = maxX;
      }

      // 上边界检查
      if (adjustedY < margin)
      {
        adjustedY = margin;
      }

      // 下边界检查
      var maxY = viewportHeight - nodeHeight - margin;
      if (adjustedY > maxY)
      {
        adjustedY = maxY;
      }

      return new Point(adjustedX, adjustedY);
    }

    /// <summary>
    /// 初始化模板分类
    /// </summary>
    private void InitializeTemplateCategories()
    {
      // 输入类 - 数据源
      var inputCategory = new TemplateCategory
      {
        Name = "输入节点",
        Color = Color.FromRgb(76, 175, 80) // 绿色
      };
      inputCategory.Items.Add(new TemplateItem { Name = "文件输入", Description = "从文件读取数据", ModuleType = ModuleType.FileInput });
      inputCategory.Items.Add(new TemplateItem { Name = "数据库输入", Description = "从数据库读取数据", ModuleType = ModuleType.DatabaseInput });
      inputCategory.Items.Add(new TemplateItem { Name = "API输入", Description = "从API接口读取数据", ModuleType = ModuleType.APIInput });
      inputCategory.Items.Add(new TemplateItem { Name = "CAD输入", Description = "从CAD文件读取数据", ModuleType = ModuleType.CADInput });
      inputCategory.Items.Add(new TemplateItem { Name = "Excel输入", Description = "从Excel文件读取数据", ModuleType = ModuleType.ExcelInput });
      inputCategory.Items.Add(new TemplateItem { Name = "CSV输入", Description = "从CSV文件读取数据", ModuleType = ModuleType.CSVInput });
      inputCategory.Items.Add(new TemplateItem { Name = "XML输入", Description = "从XML文件读取数据", ModuleType = ModuleType.XMLInput });
      inputCategory.Items.Add(new TemplateItem { Name = "JSON输入", Description = "从JSON文件读取数据", ModuleType = ModuleType.JSONInput });
      inputCategory.Items.Add(new TemplateItem { Name = "手动输入数据", Description = "手动输入数据节点", ModuleType = ModuleType.ManualDataInput });
      TemplateCategories.Add(inputCategory);

      // 常规数据类
      var dataCategory = new TemplateCategory
      {
        Name = "常规数据类",
        Color = Color.FromRgb(66, 133, 244) // 蓝色
      };
      dataCategory.Items.Add(new TemplateItem { Name = "平面管线", Description = "平面管线类模块", ModuleType = ModuleType.PipeLine });
      dataCategory.Items.Add(new TemplateItem { Name = "立管", Description = "立管类模块", ModuleType = ModuleType.RiserPipe });
      dataCategory.Items.Add(new TemplateItem { Name = "调压箱调压柜", Description = "调压箱调压柜类模块", ModuleType = ModuleType.PressureBox });
      dataCategory.Items.Add(new TemplateItem { Name = "开挖回填", Description = "开挖回填模块", ModuleType = ModuleType.Excavation });
      dataCategory.Items.Add(new TemplateItem { Name = "破除恢复", Description = "破除恢复模块", ModuleType = ModuleType.Demolition });
      dataCategory.Items.Add(new TemplateItem { Name = "防腐", Description = "防腐模块", ModuleType = ModuleType.AntiCorrosion });
      dataCategory.Items.Add(new TemplateItem { Name = "防雷防静电", Description = "防雷防静电模块", ModuleType = ModuleType.LightningProtection });
      TemplateCategories.Add(dataCategory);

      // 数据衍生关联类
      var relationCategory = new TemplateCategory
      {
        Name = "数据衍生关联类",
        Color = Color.FromRgb(15, 157, 88) // 绿色
      };
      relationCategory.Items.Add(new TemplateItem { Name = "警示带示踪线", Description = "警示带示踪线模块", ModuleType = ModuleType.WarningBand });
      relationCategory.Items.Add(new TemplateItem { Name = "焊口探伤", Description = "焊口探伤模块", ModuleType = ModuleType.WeldInspection });
      relationCategory.Items.Add(new TemplateItem { Name = "安装台班", Description = "安装台班模块", ModuleType = ModuleType.InstallationTeam });
      relationCategory.Items.Add(new TemplateItem { Name = "措施", Description = "措施模块", ModuleType = ModuleType.Measures });
      TemplateCategories.Add(relationCategory);

      // 触发器类
      var triggerCategory = new TemplateCategory
      {
        Name = "触发器类",
        Color = Color.FromRgb(255, 152, 0) // 橙色
      };
      triggerCategory.Items.Add(new TemplateItem { Name = "点击触发器", Description = "点击触发器模块", ModuleType = ModuleType.ClickTrigger });
      triggerCategory.Items.Add(new TemplateItem { Name = "关联触发器", Description = "关联触发器模块", ModuleType = ModuleType.AssociationTrigger });
      triggerCategory.Items.Add(new TemplateItem { Name = "定时触发器", Description = "定时触发器模块", ModuleType = ModuleType.TimedTrigger });
      triggerCategory.Items.Add(new TemplateItem { Name = "文件变化触发器", Description = "文件变化触发器模块", ModuleType = ModuleType.FileChangeTrigger });
      triggerCategory.Items.Add(new TemplateItem { Name = "环境触发器", Description = "环境触发器模块", ModuleType = ModuleType.EnvironmentTrigger });
      TemplateCategories.Add(triggerCategory);

      // 处理类
      var processCategory = new TemplateCategory
      {
        Name = "处理类",
        Color = Color.FromRgb(156, 39, 176) // 紫色
      };
      processCategory.Items.Add(new TemplateItem { Name = "数据过滤", Description = "数据过滤模块", ModuleType = ModuleType.DataFilter });
      processCategory.Items.Add(new TemplateItem { Name = "标签搜索", Description = "标签搜索模块", ModuleType = ModuleType.TagSearch });
      processCategory.Items.Add(new TemplateItem { Name = "数据计算", Description = "数据计算模块", ModuleType = ModuleType.DataCalculation });
      processCategory.Items.Add(new TemplateItem { Name = "数据验证", Description = "数据验证模块", ModuleType = ModuleType.DataValidation });
      processCategory.Items.Add(new TemplateItem { Name = "数据转换", Description = "数据转换模块", ModuleType = ModuleType.DataTransform });
      processCategory.Items.Add(new TemplateItem { Name = "数据条件", Description = "数据条件模块", ModuleType = ModuleType.DataCondition });
      processCategory.Items.Add(new TemplateItem { Name = "数组展开", Description = "数组展开节点", ModuleType = ModuleType.ArrayExpansion });
      processCategory.Items.Add(new TemplateItem { Name = "其他", Description = "其他处理模块", ModuleType = ModuleType.Other });
      TemplateCategories.Add(processCategory);

      // 整理类
      var organizeCategory = new TemplateCategory
      {
        Name = "整理类",
        Color = Color.FromRgb(139, 92, 246) // 中紫色
      };
      organizeCategory.Items.Add(new TemplateItem { Name = "表格管理", Description = "表格管理模块", ModuleType = ModuleType.TableManager });
      organizeCategory.Items.Add(new TemplateItem { Name = "图形API", Description = "图形API模块", ModuleType = ModuleType.GraphicsAPI });
      organizeCategory.Items.Add(new TemplateItem { Name = "Excel/CSV", Description = "Excel/CSV处理模块", ModuleType = ModuleType.ExcelCSV });
      organizeCategory.Items.Add(new TemplateItem { Name = "Word处理", Description = "Word处理模块", ModuleType = ModuleType.WordProcessor });
      TemplateCategories.Add(organizeCategory);

      // 输出类
      var outputCategory = new TemplateCategory
      {
        Name = "输出类",
        Color = Color.FromRgb(168, 85, 247) // 浅紫色
      };
      outputCategory.Items.Add(new TemplateItem { Name = "文件生成", Description = "文件生成模块", ModuleType = ModuleType.FileGeneration });
      outputCategory.Items.Add(new TemplateItem { Name = "手动定位", Description = "手动定位模块", ModuleType = ModuleType.ManualLocation });
      outputCategory.Items.Add(new TemplateItem { Name = "指定路径", Description = "指定路径模块", ModuleType = ModuleType.SpecifiedPath });
      outputCategory.Items.Add(new TemplateItem { Name = "第三方API", Description = "第三方API模块", ModuleType = ModuleType.ThirdPartyAPI });
      outputCategory.Items.Add(new TemplateItem { Name = "CAD导出", Description = "CAD导出模块", ModuleType = ModuleType.CADExport });
      outputCategory.Items.Add(new TemplateItem { Name = "Excel导出", Description = "Excel导出模块", ModuleType = ModuleType.ExcelExport });
      outputCategory.Items.Add(new TemplateItem { Name = "CSV导出", Description = "CSV导出模块", ModuleType = ModuleType.CSVExport });
      outputCategory.Items.Add(new TemplateItem { Name = "Word导出", Description = "Word导出模块", ModuleType = ModuleType.WordExport });
      outputCategory.Items.Add(new TemplateItem { Name = "PPT导出", Description = "PPT导出模块", ModuleType = ModuleType.PPTExport });
      outputCategory.Items.Add(new TemplateItem { Name = "图片导出", Description = "图片导出模块", ModuleType = ModuleType.ImageExport });
      outputCategory.Items.Add(new TemplateItem { Name = "发布释放", Description = "发布释放模块", ModuleType = ModuleType.PublishRelease });
      outputCategory.Items.Add(new TemplateItem { Name = "通知警报", Description = "通知警报模块", ModuleType = ModuleType.NotificationAlert });
      outputCategory.Items.Add(new TemplateItem { Name = "对话聊天", Description = "对话聊天模块", ModuleType = ModuleType.DialogChat });
      outputCategory.Items.Add(new TemplateItem { Name = "其他输出", Description = "其他输出模块", ModuleType = ModuleType.OtherOutput });
      TemplateCategories.Add(outputCategory);

      // 控制类
      var controlCategory = new TemplateCategory
      {
        Name = "控制节点",
        Color = Color.FromRgb(156, 39, 176) // 紫色
      };
      controlCategory.Items.Add(new TemplateItem { Name = "条件分支", Description = "条件分支控制", ModuleType = ModuleType.ConditionalBranch });
      controlCategory.Items.Add(new TemplateItem { Name = "循环处理", Description = "循环处理控制", ModuleType = ModuleType.LoopProcessor });
      controlCategory.Items.Add(new TemplateItem { Name = "错误处理", Description = "错误处理控制", ModuleType = ModuleType.ErrorHandler });
      controlCategory.Items.Add(new TemplateItem { Name = "流程控制", Description = "流程控制节点", ModuleType = ModuleType.FlowControl });
      controlCategory.Items.Add(new TemplateItem { Name = "脚本执行", Description = "脚本执行节点", ModuleType = ModuleType.ScriptExecutor });
      controlCategory.Items.Add(new TemplateItem { Name = "变量管理", Description = "变量管理节点", ModuleType = ModuleType.VariableManager });
      controlCategory.Items.Add(new TemplateItem { Name = "状态管理", Description = "状态管理节点", ModuleType = ModuleType.StateManager });
      controlCategory.Items.Add(new TemplateItem { Name = "智能体", Description = "AI智能体节点", ModuleType = ModuleType.AIAgent });
      TemplateCategories.Add(controlCategory);
    }

    /// <summary>
    /// 获取模块类型的名称
    /// </summary>
    /// <param name="type">模块类型</param>
    /// <returns>名称</returns>
    private static string GetModuleTypeName(ModuleType type)
    {
      return type switch
      {
        // 输入类
        ModuleType.FileInput => "文件输入",
        ModuleType.DatabaseInput => "数据库输入",
        ModuleType.APIInput => "API输入",
        ModuleType.CADInput => "CAD输入",
        ModuleType.ExcelInput => "Excel输入",
        ModuleType.CSVInput => "CSV输入",
        ModuleType.XMLInput => "XML输入",
        ModuleType.JSONInput => "JSON输入",
        ModuleType.ManualDataInput => "手动输入数据",

        // 常规数据类
        ModuleType.PipeLine => "平面管线",
        ModuleType.RiserPipe => "立管",
        ModuleType.PressureBox => "调压箱调压柜",
        ModuleType.Excavation => "开挖回填",
        ModuleType.Demolition => "破除恢复",
        ModuleType.AntiCorrosion => "防腐",
        ModuleType.LightningProtection => "防雷防静电",
        ModuleType.WarningBand => "警示带示踪线",
        ModuleType.WeldInspection => "焊口探伤",
        ModuleType.InstallationTeam => "安装台班",
        ModuleType.Measures => "措施",

        // 触发器类
        ModuleType.ClickTrigger => "点击触发器",
        ModuleType.AssociationTrigger => "关联触发器",
        ModuleType.TimedTrigger => "定时触发器",
        ModuleType.FileChangeTrigger => "文件变化触发器",
        ModuleType.EnvironmentTrigger => "环境触发器",

        // 处理类
        ModuleType.DataFilter => "数据过滤",
        ModuleType.TagSearch => "标签搜索",
        ModuleType.DataCalculation => "数据计算",
        ModuleType.DataValidation => "数据验证",
        ModuleType.DataTransform => "数据转换",
        ModuleType.DataCondition => "数据条件",
        ModuleType.ArrayExpansion => "数组展开",
        ModuleType.Other => "其他",

        // 整理类
        ModuleType.TableManager => "表格管理",
        ModuleType.GraphicsAPI => "图形API",
        ModuleType.ExcelCSV => "Excel/CSV",
        ModuleType.WordProcessor => "Word处理",

        // 输出类
        ModuleType.FileGeneration => "文件生成",
        ModuleType.ManualLocation => "手动定位",
        ModuleType.SpecifiedPath => "指定路径",
        ModuleType.ThirdPartyAPI => "第三方API",
        ModuleType.CADExport => "CAD导出",
        ModuleType.ExcelExport => "Excel导出",
        ModuleType.CSVExport => "CSV导出",
        ModuleType.WordExport => "Word导出",
        ModuleType.PPTExport => "PPT导出",
        ModuleType.ImageExport => "图片导出",
        ModuleType.PublishRelease => "发布释放",
        ModuleType.NotificationAlert => "通知警报",
        ModuleType.DialogChat => "对话聊天",
        ModuleType.OtherOutput => "其他输出",

        // 控制类
        ModuleType.ConditionalBranch => "条件分支",
        ModuleType.LoopProcessor => "循环处理",
        ModuleType.ErrorHandler => "错误处理",
        ModuleType.FlowControl => "流程控制",
        ModuleType.ScriptExecutor => "脚本执行",
        ModuleType.VariableManager => "变量管理",
        ModuleType.StateManager => "状态管理",
        ModuleType.AIAgent => "智能体",

        _ => "未知模块"
      };
    }

    #region 自动布局功能

    /// <summary>
    /// 执行层次布局
    /// </summary>
    public async Task ApplyHierarchicalLayoutAsync()
    {
      if (!Nodes.Any())
      {
        System.Diagnostics.Debug.WriteLine("层次布局：没有节点可以布局");
        return;
      }

      // 统计锁定和未锁定的节点
      var lockedNodes = Nodes.Where(n => n.IsLocked).ToList();
      var unlockedNodes = Nodes.Where(n => !n.IsLocked).ToList();

      System.Diagnostics.Debug.WriteLine($"开始层次布局：总节点数量 {Nodes.Count}, 锁定节点 {lockedNodes.Count}, 未锁定节点 {unlockedNodes.Count}, 连接数量 {Connections.Count}");

      // 如果没有未锁定的节点，无需布局
      if (!unlockedNodes.Any())
      {
        System.Diagnostics.Debug.WriteLine("层次布局：所有节点都已锁定，无需布局");
        return;
      }

      // 使用实际画布可见区域大小，确保布局在可见范围内
      var canvasWidth = Math.Max(800, ActualCanvasSize.Width); // 使用实际画布宽度
      var canvasHeight = Math.Max(600, ActualCanvasSize.Height); // 使用实际画布高度

      // 考虑左侧面板占用的空间，调整可用宽度
      var leftPanelWidth = 250; // 左侧模板面板宽度
      var availableWidth = Math.Max(600, canvasWidth - leftPanelWidth);

      // 确保起始位置在可见区域内
      var startX = Math.Max(leftPanelWidth + 50, ActualViewport.X + 50);
      var startY = Math.Max(50, ActualViewport.Y + 50);

      var options = new LayoutOptions
      {
        HorizontalSpacing = 280,
        VerticalSpacing = 140,
        StartPosition = new Point(startX, startY), // 从可见区域开始布局
        EnableAnimation = true,
        AnimationDuration = 800,
        CanvasWidth = availableWidth, // 使用可用宽度进行换行计算
        CanvasHeight = canvasHeight,
        NodeWidth = 250,
        NodeHeight = 120,
        RowSpacing = 180
      };

      System.Diagnostics.Debug.WriteLine($"实际画布尺寸: {canvasWidth} x {canvasHeight}");
      System.Diagnostics.Debug.WriteLine($"可用布局宽度: {availableWidth}");
      System.Diagnostics.Debug.WriteLine($"布局起始位置: ({startX}, {startY})");

      await ApplyLayoutAsync(LayoutAlgorithm.Hierarchical, options);

      // 布局完成后，自动调整视图以确保所有节点可见
      await Task.Delay(options.AnimationDuration + 100); // 等待动画完成
      FitToNodes();

      // 如果有锁定节点，显示用户反馈
      if (lockedNodes.Any())
      {
        System.Diagnostics.Debug.WriteLine($"布局完成：{lockedNodes.Count} 个节点因锁定而未参与布局");
        // 这里可以添加用户通知，比如状态栏消息或临时提示
        ShowLayoutCompletionMessage(lockedNodes.Count, unlockedNodes.Count);
      }
    }

    /// <summary>
    /// 显示布局完成消息
    /// </summary>
    /// <param name="lockedCount">锁定节点数量</param>
    /// <param name="layoutedCount">参与布局的节点数量</param>
    private void ShowLayoutCompletionMessage(int lockedCount, int layoutedCount)
    {
      // 这里可以通过事件或属性来通知UI显示消息
      // 暂时使用Debug输出，后续可以扩展为状态栏消息或通知
      var message = $"自动布局完成：{layoutedCount} 个节点已重新排列，{lockedCount} 个节点因锁定而保持原位置";
      System.Diagnostics.Debug.WriteLine(message);

      // TODO: 可以添加一个事件来通知UI显示这个消息
      // LayoutCompletionMessage?.Invoke(message);
    }

    /// <summary>
    /// 应用层次布局算法
    /// </summary>
    private async Task ApplyLayoutAsync(LayoutAlgorithm algorithm, LayoutOptions options)
    {
      try
      {
        System.Diagnostics.Debug.WriteLine($"开始计算布局位置: {algorithm}");

        // 只支持层次布局
        if (algorithm != LayoutAlgorithm.Hierarchical)
        {
          throw new ArgumentException($"不支持的布局算法: {algorithm}");
        }

        // 计算新的布局位置
        var newPositions = _layoutService.CalculateLayout(Nodes, Connections, algorithm, options);

        System.Diagnostics.Debug.WriteLine($"布局计算完成，获得 {newPositions.Count} 个位置");

        if (newPositions.Any())
        {
          // 过滤出需要移动的节点（排除锁定节点）
          var nodesToMove = newPositions.Where(kvp => !kvp.Key.IsLocked).ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

          if (nodesToMove.Any())
          {
            if (options.EnableAnimation)
            {
              System.Diagnostics.Debug.WriteLine($"开始动画过渡，移动 {nodesToMove.Count} 个节点");
              // 使用动画过渡到新位置（只对未锁定的节点）
              await AnimateNodesToPositionsAsync(nodesToMove, options.AnimationDuration);
              System.Diagnostics.Debug.WriteLine("动画过渡完成");
            }
            else
            {
              System.Diagnostics.Debug.WriteLine($"直接设置位置，移动 {nodesToMove.Count} 个节点");
              // 直接设置新位置（只对未锁定的节点）
              foreach (var kvp in nodesToMove)
              {
                kvp.Key.Location = kvp.Value;
              }
            }
          }
          else
          {
            System.Diagnostics.Debug.WriteLine("所有节点都已锁定，无需移动");
          }
        }

        System.Diagnostics.Debug.WriteLine($"自动布局完成: {algorithm}, 节点数量: {newPositions.Count}");
      }
      catch (Exception ex)
      {
        System.Diagnostics.Debug.WriteLine($"自动布局失败: {ex.Message}");
        System.Diagnostics.Debug.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        throw; // 重新抛出异常以便UI层处理
      }
    }

    /// <summary>
    /// 动画过渡节点到新位置
    /// </summary>
    private async Task AnimateNodesToPositionsAsync(Dictionary<ModuleNodeViewModel, Point> targetPositions, int duration)
    {
      if (!targetPositions.Any())
        return;

      // 使用单个计时器统一管理所有节点的动画
      var tcs = new TaskCompletionSource<bool>();
      var startPositions = new Dictionary<ModuleNodeViewModel, Point>();

      // 记录起始位置
      foreach (var kvp in targetPositions)
      {
        startPositions[kvp.Key] = kvp.Key.Location;
      }

      await Application.Current.Dispatcher.InvokeAsync(() =>
      {
        var timer = new System.Windows.Threading.DispatcherTimer
        {
          Interval = TimeSpan.FromMilliseconds(16) // ~60 FPS
        };

        var startTime = DateTime.Now;
        bool isCompleted = false;

        timer.Tick += (s, e) =>
        {
          if (isCompleted) return;

          var elapsed = DateTime.Now - startTime;
          var progress = Math.Min(1.0, elapsed.TotalMilliseconds / duration);
          var easedProgress = EaseInOut(progress);

          // 更新所有节点位置
          foreach (var kvp in targetPositions)
          {
            var node = kvp.Key;
            var targetPos = kvp.Value;
            var startPos = startPositions[node];

            var currentX = startPos.X + (targetPos.X - startPos.X) * easedProgress;
            var currentY = startPos.Y + (targetPos.Y - startPos.Y) * easedProgress;

            node.Location = new Point(currentX, currentY);
          }

          if (progress >= 1.0)
          {
            timer.Stop();

            // 确保所有节点都到达精确位置
            foreach (var kvp in targetPositions)
            {
              kvp.Key.Location = kvp.Value;
            }

            isCompleted = true;
            tcs.SetResult(true);
          }
        };

        timer.Start();
      });

      await tcs.Task;
    }

    /// <summary>
    /// 缓动函数 - EaseInOut
    /// </summary>
    private double EaseInOut(double t)
    {
      return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
    }

    #endregion
  }
}