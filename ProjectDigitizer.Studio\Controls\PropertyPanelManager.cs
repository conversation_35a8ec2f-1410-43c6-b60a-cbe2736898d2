using System;
using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;
using ProjectDigitizer.Studio.Controls;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 属性面板管理器 - 统一管理不同类型的属性面板
    /// </summary>
    public class PropertyPanelManager
    {
        private readonly ContentControl _container;
        private ModernPropertyPanel? _modernPanel;
        private DynamicPropertyPanel? _dynamicPanel;
        private FunctionPropertyPanel? _functionPanel;
        private ModuleNodeViewModel? _currentNode;

        /// <summary>
        /// 属性值变化事件
        /// </summary>
        public event EventHandler<PropertyValueChangedEventArgs>? PropertyValueChanged;

        public PropertyPanelManager(ContentControl container)
        {
            _container = container ?? throw new ArgumentNullException(nameof(container));
        }

        /// <summary>
        /// 设置当前节点并显示对应的属性面板
        /// </summary>
        /// <param name="node">节点视图模型</param>
        public void SetNode(ModuleNodeViewModel node)
        {
            _currentNode = node;

            if (node == null)
            {
                System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 节点为空，清空面板");
                ClearPanel();
                return;
            }

            // 根据节点类型选择合适的属性面板
            var moduleType = node.Module?.Type;
            System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 设置节点 '{node.Title}', 类型: {moduleType}");

            switch (moduleType)
            {
                case ModuleType.DataFilter:
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 显示现代化面板 (DataFilter)");
                    ShowModernPanel(node);
                    break;

                // 数据计算节点 - 直接使用函数属性面板
                case ModuleType.DataCalculation:
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 显示函数属性面板 (DataCalculation)");
                    ShowFunctionPanel(node);
                    break;

                case ModuleType.DataTransform:
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 显示现代化面板 (DataTransform)");
                    ShowModernPanel(node); // 暂时也使用现代化面板
                    break;

                default:
                    System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 显示动态面板 (默认: {moduleType})");
                    ShowDynamicPanel(node);
                    break;
            }
        }

        /// <summary>
        /// 显示现代化属性面板（用于 DataFilter 等节点）
        /// </summary>
        private void ShowModernPanel(ModuleNodeViewModel node)
        {
            try
            {
                // 创建或重用现代化面板
                if (_modernPanel == null)
                {
                    _modernPanel = new ModernPropertyPanel();
                    _modernPanel.PropertyValueChanged += OnPropertyValueChanged;
                    _modernPanel.ApplyClicked += OnApplyClicked;
                    _modernPanel.ResetClicked += OnResetClicked;
                }

                // 设置节点数据
                _modernPanel.SetNode(node);

                // 显示面板
                _container.Content = _modernPanel;

                System.Diagnostics.Debug.WriteLine($"已显示现代化属性面板: {node.Module?.Type}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示现代化属性面板失败: {ex.Message}");
                // 回退到动态面板
                ShowDynamicPanel(node);
            }
        }

        /// <summary>
        /// 显示函数属性面板（用于 DataCalculation 节点的函数配置）
        /// </summary>
        private void ShowFunctionPanel(ModuleNodeViewModel node)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 开始创建函数属性面板");

                // 创建或重用函数面板
                if (_functionPanel == null)
                {
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 创建新的函数属性面板实例");
                    _functionPanel = new FunctionPropertyPanel();
                    _functionPanel.PropertyValueChanged += OnPropertyValueChanged;
                    _functionPanel.ApplyClicked += OnApplyClicked;
                    _functionPanel.ResetClicked += OnResetClicked;
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 函数属性面板实例创建成功");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 重用现有函数属性面板实例");
                }

                // 设置节点数据
                System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 设置节点数据到函数面板: {node.Title}");
                _functionPanel.SetNode(node);

                // 显示面板
                System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 将函数面板设置到容器");
                _container.Content = _functionPanel;

                System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 函数属性面板显示成功: {node.Module?.Type}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 显示函数属性面板失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"PropertyPanelManager: 异常堆栈: {ex.StackTrace}");
                // 回退到现代化面板
                System.Diagnostics.Debug.WriteLine("PropertyPanelManager: 回退到现代化面板");
                ShowModernPanel(node);
            }
        }

        /// <summary>
        /// 显示动态属性面板（用于其他节点）
        /// </summary>
        private void ShowDynamicPanel(ModuleNodeViewModel node)
        {
            try
            {
                // 创建或重用动态面板
                if (_dynamicPanel == null)
                {
                    _dynamicPanel = new DynamicPropertyPanel();
                }

                // 设置节点数据
                _dynamicPanel.SetCurrentNode(node);

                // 显示面板
                _container.Content = _dynamicPanel;

                System.Diagnostics.Debug.WriteLine($"已显示动态属性面板: {node.Module?.Type}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示动态属性面板失败: {ex.Message}");
                ShowErrorPanel($"无法加载属性面板: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示错误面板
        /// </summary>
        private void ShowErrorPanel(string errorMessage)
        {
            var errorPanel = new StackPanel
            {
                Margin = new Thickness(16),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var icon = new TextBlock
            {
                Text = "⚠️",
                FontSize = 48,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16)
            };

            var message = new TextBlock
            {
                Text = errorMessage,
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = System.Windows.Media.Brushes.Gray
            };

            errorPanel.Children.Add(icon);
            errorPanel.Children.Add(message);

            _container.Content = errorPanel;
        }

        /// <summary>
        /// 清空面板
        /// </summary>
        private void ClearPanel()
        {
            _container.Content = new TextBlock
            {
                Text = "请选择一个节点以查看其属性",
                FontSize = 14,
                Foreground = System.Windows.Media.Brushes.Gray,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(16)
            };
        }

        /// <summary>
        /// 现代化面板属性值变化处理
        /// </summary>
        private void OnPropertyValueChanged(object? sender, PropertyValueChangedEventArgs e)
        {
            PropertyValueChanged?.Invoke(sender, e);
        }



        /// <summary>
        /// 应用按钮点击处理
        /// </summary>
        private void OnApplyClicked(object? sender, EventArgs e)
        {
            try
            {
                if (_currentNode != null && _modernPanel != null)
                {
                    // 验证配置
                    var validation = _modernPanel.ValidateConfiguration();
                    if (!validation.IsValid)
                    {
                        var errors = string.Join("\n", validation.Errors);
                        MessageBox.Show($"配置验证失败:\n{errors}", "验证错误",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    // 获取配置并应用
                    var config = _modernPanel.GetCurrentConfiguration();
                    ApplyConfiguration(config);

                    System.Diagnostics.Debug.WriteLine("属性配置已应用");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用配置时发生错误: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 重置按钮点击处理
        /// </summary>
        private void OnResetClicked(object? sender, EventArgs e)
        {
            try
            {
                if (_currentNode != null)
                {
                    // 确认重置
                    var result = MessageBox.Show("确定要重置所有配置吗？", "确认重置",
                                                MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        // 重置节点属性
                        var allKeys = _currentNode.PropertyValues.GetAllValues().Keys.ToList();
                        foreach (var key in allKeys)
                        {
                            _currentNode.PropertyValues.SetValue(key, null);
                        }

                        // 重新加载面板
                        SetNode(_currentNode);

                        System.Diagnostics.Debug.WriteLine("属性配置已重置");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置配置时发生错误: {ex.Message}", "错误",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 应用配置到节点
        /// </summary>
        private void ApplyConfiguration(System.Collections.Generic.Dictionary<string, object?> config)
        {
            if (_currentNode?.PropertyValues == null) return;

            foreach (var kvp in config)
            {
                _currentNode.PropertyValues.SetValue(kvp.Key, kvp.Value);
            }

            // 触发节点更新事件（如果需要）
            // _currentNode.NotifyPropertyChanged();
        }



        /// <summary>
        /// 强制显示函数属性面板
        /// </summary>
        public void ShowFunctionPanel()
        {
            if (_currentNode != null)
            {
                ShowFunctionPanel(_currentNode);
            }
        }

        /// <summary>
        /// 强制显示现代化属性面板
        /// </summary>
        public void ShowModernPanel()
        {
            if (_currentNode != null)
            {
                ShowModernPanel(_currentNode);
            }
        }

        /// <summary>
        /// 获取当前面板类型
        /// </summary>
        public string GetCurrentPanelType()
        {
            return _container.Content switch
            {
                FunctionPropertyPanel => "Function",
                ModernPropertyPanel => "Modern",
                DynamicPropertyPanel => "Dynamic",
                _ => "None"
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_modernPanel != null)
            {
                _modernPanel.PropertyValueChanged -= OnPropertyValueChanged;
                _modernPanel.ApplyClicked -= OnApplyClicked;
                _modernPanel.ResetClicked -= OnResetClicked;
            }



            _container.Content = null;
        }
    }
}
