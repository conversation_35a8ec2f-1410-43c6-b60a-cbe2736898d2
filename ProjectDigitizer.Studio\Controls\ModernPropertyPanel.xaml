<UserControl x:Class="ProjectDigitizer.Studio.Controls.ModernPropertyPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600"
        d:DesignWidth="450"
             Background="#F5F5F5">

    <UserControl.Resources>
        <!-- 现代化样式定义 -->
        <Style x:Key="CardStyle"
                TargetType="Border">
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="CornerRadius"
                    Value="8"/>
            <Setter Property="Margin"
                    Value="12,8"/>
            <Setter Property="Padding"
                    Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0"
                            Direction="270"
                            ShadowDepth="2"
                            BlurRadius="8"
                            Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="GroupHeaderStyle"
                TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="16"/>
            <Setter Property="FontWeight"
                    Value="SemiBold"/>
            <Setter Property="Foreground"
                    Value="#1976D2"/>
            <Setter Property="Margin"
                    Value="0,0,0,12"/>
        </Style>

        <Style x:Key="LabelStyle"
                TargetType="TextBlock">
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontWeight"
                    Value="Medium"/>
            <Setter Property="Foreground"
                    Value="#424242"/>
            <Setter Property="Margin"
                    Value="0,0,0,6"/>
            <Setter Property="VerticalAlignment"
                    Value="Center"/>
        </Style>

        <Style x:Key="InputStyle"
                TargetType="Control">
            <Setter Property="Height"
                    Value="36"/>
            <Setter Property="Margin"
                    Value="0,0,0,16"/>
            <Setter Property="FontSize"
                    Value="14"/>
        </Style>

        <Style x:Key="TextBoxStyle"
                TargetType="TextBox"
                BasedOn="{StaticResource InputStyle}">
            <Setter Property="Padding"
                    Value="12,8"/>
            <Setter Property="BorderBrush"
                    Value="#E0E0E0"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused"
                                    Value="True">
                                <Setter Property="BorderBrush"
                                        Value="#1976D2"/>
                                <Setter Property="BorderThickness"
                                        Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ComboBoxStyle"
                TargetType="ComboBox"
                BasedOn="{StaticResource InputStyle}">
            <Setter Property="Padding"
                    Value="12,8"/>
            <Setter Property="BorderBrush"
                    Value="#E0E0E0"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Background"
                    Value="White"/>
        </Style>

        <Style x:Key="CheckBoxStyle"
                TargetType="CheckBox">
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="Foreground"
                    Value="#424242"/>
            <Setter Property="Margin"
                    Value="0,0,0,16"/>
            <Setter Property="VerticalAlignment"
                    Value="Center"/>
        </Style>

        <Style x:Key="PrimaryButtonStyle"
                TargetType="Button">
            <Setter Property="Background"
                    Value="#1976D2"/>
            <Setter Property="Foreground"
                    Value="White"/>
            <Setter Property="BorderThickness"
                    Value="0"/>
            <Setter Property="Height"
                    Value="40"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontWeight"
                    Value="Medium"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#1565C0"/>
                            </Trigger>
                            <Trigger Property="IsPressed"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle"
                TargetType="Button">
            <Setter Property="Background"
                    Value="Transparent"/>
            <Setter Property="Foreground"
                    Value="#1976D2"/>
            <Setter Property="BorderBrush"
                    Value="#1976D2"/>
            <Setter Property="BorderThickness"
                    Value="1"/>
            <Setter Property="Height"
                    Value="40"/>
            <Setter Property="FontSize"
                    Value="14"/>
            <Setter Property="FontWeight"
                    Value="Medium"/>
            <Setter Property="Cursor"
                    Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver"
                                    Value="True">
                                <Setter Property="Background"
                                        Value="#E3F2FD"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主内容区域 -->
        <ScrollViewer Grid.Row="0"
                VerticalScrollBarVisibility="Auto"
                HorizontalScrollBarVisibility="Disabled">
            <StackPanel x:Name="PropertyContainer">

                <!-- 基础设置组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="基础设置"
                                Style="{StaticResource GroupHeaderStyle}"/>

                        <!-- 筛选条件 -->
                        <TextBlock Text="筛选条件 *"
                                Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="FilterConditionTextBox"
                                 Style="{StaticResource TextBoxStyle}"
                                 Height="60"
                                 TextWrapping="Wrap"
                                 AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto"
                                 Text="score > 85"
                                 ToolTip="输入筛选表达式，如：score > 85 或 name == &quot;张三&quot;"/>

                        <!-- 筛选类型 -->
                        <TextBlock Text="筛选类型"
                                Style="{StaticResource LabelStyle}"/>
                        <ComboBox x:Name="FilterTypeComboBox"
                                Style="{StaticResource ComboBoxStyle}">
                            <ComboBoxItem Content="包含匹配项"
                                    Tag="include"
                                    IsSelected="True"/>
                            <ComboBoxItem Content="排除匹配项"
                                    Tag="exclude"/>
                        </ComboBox>
                    </StackPanel>
                </Border>

                <!-- 筛选选项组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="筛选选项"
                                Style="{StaticResource GroupHeaderStyle}"/>

                        <CheckBox x:Name="CaseSensitiveCheckBox"
                                  Content="区分大小写"
                                  Style="{StaticResource CheckBoxStyle}"
                                  ToolTip="字符串比较时是否区分大小写"/>
                    </StackPanel>
                </Border>

                <!-- 统计功能组 -->
                <Border Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="统计功能"
                                Style="{StaticResource GroupHeaderStyle}"/>

                        <!-- 启用统计 -->
                        <CheckBox x:Name="AggregationEnabledCheckBox"
                                  Content="启用统计计算"
                                  Style="{StaticResource CheckBoxStyle}"
                                  Checked="OnAggregationEnabledChanged"
                                  Unchecked="OnAggregationEnabledChanged"
                                  ToolTip="是否对筛选后的数据进行统计计算"/>

                        <!-- 统计函数 -->
                        <StackPanel x:Name="StatisticsPanel"
                                Visibility="Collapsed">
                            <TextBlock Text="统计函数"
                                    Style="{StaticResource LabelStyle}"/>
                            <ComboBox x:Name="AggregationFunctionComboBox"
                                    Style="{StaticResource ComboBoxStyle}">
                                <ComboBoxItem Content="计数"
                                        Tag="count"
                                        IsSelected="True"/>
                                <ComboBoxItem Content="求和"
                                        Tag="sum"/>
                                <ComboBoxItem Content="平均值"
                                        Tag="avg"/>
                                <ComboBoxItem Content="最小值"
                                        Tag="min"/>
                                <ComboBoxItem Content="最大值"
                                        Tag="max"/>
                            </ComboBox>

                            <!-- 统计字段 -->
                            <TextBlock Text="统计字段"
                                    Style="{StaticResource LabelStyle}"/>
                            <TextBox x:Name="AggregationFieldTextBox"
                                     Style="{StaticResource TextBoxStyle}"
                                     Text="score"
                                     ToolTip="要进行统计的字段名称（计数时可为空）"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- 底部按钮区域 -->
        <Border Grid.Row="1"
                Background="White"
                BorderBrush="#E0E0E0"
                BorderThickness="0,1,0,0"
                Padding="16">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="12"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Button Grid.Column="0"
                        x:Name="ResetButton"
                        Content="重置"
                        Style="{StaticResource SecondaryButtonStyle}"
                        Click="OnResetClick"/>

                <Button Grid.Column="2"
                        x:Name="ApplyButton"
                        Content="应用"
                        Style="{StaticResource PrimaryButtonStyle}"
                        Click="OnApplyClick"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
