using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using Nodify;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.Interfaces;

namespace ProjectDigitizer.Studio.ViewModels
{
    /// <summary>
    /// 子模板节点的视图模型
    /// </summary>
    public class ModuleNodeViewModel : ViewModelBase
    {
        private ModuleModel? _module;
        private bool _isSelected;
        private bool _isLightBulbOn;
        private bool _isExpanded;
        private bool _isLocked;
        private bool _isEnabled = true;
        private Brush? _headerBackground;
        private string? _title;
        private Point _location;

        /// <summary>
        /// 对应的模块模型
        /// </summary>
        public ModuleModel? Module
        {
            get => _module;
            set
            {
                if (_module != value)
                {
                    // 使用批量更新减少属性通知频率
                    BatchUpdate(() =>
                    {
                        _module = value;
                        OnPropertyChanged(nameof(Module));
                        OnPropertyChanged(nameof(IconGlyph));
                        OnPropertyChanged(nameof(StatusBrush));
                        OnPropertyChanged(nameof(NodeType));
                        OnPropertyChanged(nameof(NodeTypeMetadata));
                        UpdateHeaderColor();
                        ConfigureConnectors(); // 根据模块类型配置连接点
                        InitializeExpandedContent(); // 初始化展开内容
                        InitializeNodeProperties(); // 初始化节点属性
                        Title = _module?.Name;
                    });
                }
            }
        }

        /// <summary>
        /// 节点位置
        /// </summary>
        public Point Location
        {
            get => _location;
            set => SetProperty(ref _location, value);
        }

        /// <summary>
        /// 节点类型分类
        /// </summary>
        public NodeType NodeType => Module != null ? NodeTypeRegistry.GetNodeType(Module.Type) : NodeType.Transform;

        /// <summary>
        /// 节点类型元数据
        /// </summary>
        public NodeTypeMetadata NodeTypeMetadata => NodeTypeRegistry.GetNodeTypeMetadata(NodeType);

        /// <summary>
        /// 节点属性
        /// </summary>
        public INodeProperties? NodeProperties { get; private set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// 灯泡是否打开（参与数据关联）
        /// </summary>
        public bool IsLightBulbOn
        {
            get => _isLightBulbOn;
            set
            {
                if (SetProperty(ref _isLightBulbOn, value))
                {
                    if (Module != null)
                    {
                        Module.IsVisible = value;
                    }
                }
            }
        }

        /// <summary>
        /// 节点是否展开
        /// </summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set => SetProperty(ref _isExpanded, value);
        }

        /// <summary>
        /// 节点位置是否锁定
        /// </summary>
        public bool IsLocked
        {
            get => _isLocked;
            set => SetProperty(ref _isLocked, value);
        }

        /// <summary>
        /// 节点是否启用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (SetProperty(ref _isEnabled, value))
                {
                    // 更新模块的启用状态
                    if (Module != null)
                    {
                        Module.IsEnabled = value;
                    }

                    // 更新标题颜色
                    UpdateHeaderColor();

                    // 通知状态画刷更新
                    OnPropertyChanged(nameof(StatusBrush));

                    // 触发启用状态变化事件
                    EnabledChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// 标题栏背景色
        /// </summary>
        public Brush? HeaderBackground
        {
            get => _headerBackground;
            set => SetProperty(ref _headerBackground, value);
        }

        /// <summary>
        /// 节点标题
        /// </summary>
        public string? Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        /// <summary>
        /// 输入连接点集合
        /// </summary>
        public ObservableCollection<ConnectorViewModel> Inputs { get; }

        /// <summary>
        /// 输出连接点集合
        /// </summary>
        public ObservableCollection<ConnectorViewModel> Outputs { get; }

        /// <summary>
        /// 条件集合
        /// </summary>
        public ObservableCollection<string> Conditions { get; }

        /// <summary>
        /// 展开内容集合 - 用于显示模块特定的配置选项
        /// </summary>
        public ObservableCollection<object> ExpandedContent { get; }

        /// <summary>
        /// 切换展开状态命令
        /// </summary>
        public ICommand ToggleExpandCommand { get; }

        /// <summary>
        /// 节点启用状态变化事件
        /// </summary>
        public event EventHandler<bool>? EnabledChanged;

        /// <summary>
        /// 节点属性值
        /// </summary>
        public NodePropertyValues PropertyValues { get; } = new NodePropertyValues();

        /// <summary>
        /// 图标字形 - 用于显示模块类型图标
        /// </summary>
        public string IconGlyph
        {
            get
            {
                if (Module == null) return "&#xE8D4;"; // 默认图标

                return Module.Type switch
                {
                    ModuleType.PipeLine => "&#xE8D4;", // 管道图标
                    ModuleType.ClickTrigger => "&#xE7C3;", // 点击图标
                    ModuleType.DataFilter => "&#xE71C;", // 过滤器图标
                    ModuleType.FileGeneration => "&#xE8A5;", // 文件图标
                    _ => "&#xE8D4;" // 默认图标
                };
            }
        }

        /// <summary>
        /// 状态画刷 - 用于状态圆点颜色
        /// </summary>
        public Brush StatusBrush
        {
            get
            {
                if (Module?.IsEnabled == true)
                    return new SolidColorBrush(Colors.LimeGreen);
                else
                    return new SolidColorBrush(Colors.Red);
            }
        }

        /// <summary>
        /// 输入标签
        /// </summary>
        public string InputLabel => "输入";

        /// <summary>
        /// 输出标签
        /// </summary>
        public string OutputLabel => "输出";

        public ModuleNodeViewModel()
        {
            Inputs = new ObservableCollection<ConnectorViewModel>();
            Outputs = new ObservableCollection<ConnectorViewModel>();
            Conditions = new ObservableCollection<string>();
            ExpandedContent = new ObservableCollection<object>();

            // 初始化命令
            ToggleExpandCommand = new DelegateCommand(() => IsExpanded = !IsExpanded);

            // 默认设置位置
            Location = new Point(100, 100);

            // 默认标题
            Title = "未命名模块";

            // 默认灯泡打开
            IsLightBulbOn = true;

            // 默认收起状态
            IsExpanded = false;

            // 默认标题栏颜色
            HeaderBackground = new SolidColorBrush(Colors.Gray);
        }

        /// <summary>
        /// 根据模块类型更新标题栏颜色
        /// </summary>
        private void UpdateHeaderColor()
        {
            if (Module == null)
                return;

            // 根据NodeType设置颜色，符合设计规范
            var nodeType = NodeTypeRegistry.GetNodeType(Module.Type);
            Color startColor, endColor;

            // 如果节点被禁用，使用灰色
            if (!IsEnabled)
            {
                startColor = Color.FromRgb(158, 158, 158);  // #9E9E9E 灰色
                endColor = Color.FromRgb(117, 117, 117);    // #757575 深灰色
            }
            else
            {
                switch (nodeType)
                {
                    case NodeType.Input:
                        // 输入节点 - 绿色渐变
                        startColor = Color.FromRgb(76, 175, 80);   // #4CAF50 绿色
                        endColor = Color.FromRgb(56, 142, 60);     // #388E3C 深绿色
                        break;

                    case NodeType.Transform:
                        // 转换节点 - 蓝色渐变
                        startColor = Color.FromRgb(33, 150, 243);  // #2196F3 蓝色
                        endColor = Color.FromRgb(25, 118, 210);    // #1976D2 深蓝色
                        break;

                    case NodeType.Output:
                        // 输出节点 - 橙色渐变
                        startColor = Color.FromRgb(255, 152, 0);   // #FF9800 橙色
                        endColor = Color.FromRgb(245, 124, 0);     // #F57C00 深橙色
                        break;

                    case NodeType.Control:
                        // 控制节点 - 紫色渐变
                        startColor = Color.FromRgb(156, 39, 176);  // #9C27B0 紫色
                        endColor = Color.FromRgb(123, 31, 162);    // #7B1FA2 深紫色
                        break;

                    default:
                        // 默认 - 灰色渐变
                        startColor = Color.FromRgb(102, 102, 102); // #666666 灰色
                        endColor = Color.FromRgb(85, 85, 85);      // #555555 深灰色
                        break;
                }
            }

            // 创建线性渐变画刷
            var gradientBrush = new LinearGradientBrush
            {
                StartPoint = new Point(0, 0),
                EndPoint = new Point(0, 1),
                GradientStops = new GradientStopCollection
                {
                    new GradientStop(startColor, 0.0),
                    new GradientStop(endColor, 1.0)
                }
            };

            // 如果节点被禁用，降低透明度
            if (!IsEnabled)
            {
                gradientBrush.Opacity = 0.6;
            }

            HeaderBackground = gradientBrush;
        }

        /// <summary>
        /// 添加条件
        /// </summary>
        public void AddCondition(string condition)
        {
            if (!string.IsNullOrEmpty(condition))
            {
                Conditions.Add(condition);

                if (Module != null && !Module.Conditions.Contains(condition))
                {
                    Module.Conditions.Add(condition);
                }
            }
        }

        /// <summary>
        /// 移除条件
        /// </summary>
        public void RemoveCondition(string condition)
        {
            if (!string.IsNullOrEmpty(condition))
            {
                Conditions.Remove(condition);

                if (Module != null && Module.Conditions.Contains(condition))
                {
                    Module.Conditions.Remove(condition);
                }
            }
        }

        /// <summary>
        /// 清空所有条件
        /// </summary>
        public void ClearConditions()
        {
            Conditions.Clear();

            if (Module != null)
            {
                Module.Conditions.Clear();
            }
        }

        private void ConfigureConnectors()
        {
            Inputs.Clear();
            Outputs.Clear();

            if (Module == null)
                return;

            // 根据模块类型配置连接点
            switch (Module.Type)
            {
                // 输入类：只有输出，作为数据源
                case ModuleType.FileInput:
                case ModuleType.DatabaseInput:
                case ModuleType.APIInput:
                case ModuleType.CADInput:
                case ModuleType.ExcelInput:
                case ModuleType.CSVInput:
                case ModuleType.XMLInput:
                case ModuleType.JSONInput:
                    AddOutput("数据输出");
                    break;

                // 手动输入数据节点：只有输出，作为数据源
                case ModuleType.ManualDataInput:
                    AddOutput("手动数据");
                    break;

                // 常规数据类：只有输出，作为数据源
                case ModuleType.PipeLine:
                case ModuleType.RiserPipe:
                case ModuleType.PressureBox:
                case ModuleType.Excavation:
                case ModuleType.Demolition:
                case ModuleType.AntiCorrosion:
                case ModuleType.LightningProtection:
                    AddOutput("数据输出");
                    break;

                // 数据衍生关联类：有输入有输出
                case ModuleType.WarningBand:
                case ModuleType.WeldInspection:
                case ModuleType.InstallationTeam:
                case ModuleType.Measures:
                    AddInput("基础数据");
                    AddOutput("关联数据");
                    break;

                // 启动类：只有输出，没有输入（工作流开始）
                case ModuleType.ClickTrigger:
                case ModuleType.AssociationTrigger:
                case ModuleType.TimedTrigger:
                case ModuleType.FileChangeTrigger:
                case ModuleType.EnvironmentTrigger:
                    AddOutput("触发输出");
                    break;

                // 控制流程类：有输入有输出（流程控制）
                case ModuleType.ConditionalBranch:
                    AddInput("条件输入");
                    AddOutput("True分支");
                    AddOutput("False分支");
                    break;

                case ModuleType.LoopProcessor:
                    AddInput("循环输入");
                    AddOutput("循环体");
                    AddOutput("循环完成");
                    break;

                case ModuleType.ErrorHandler:
                    AddInput("处理输入");
                    AddOutput("正常输出");
                    AddOutput("错误输出");
                    break;

                case ModuleType.FlowControl:
                    AddInput("流程输入");
                    AddOutput("流程输出");
                    break;

                case ModuleType.ScriptExecutor:
                    AddInput("脚本输入");
                    AddOutput("执行结果");
                    break;

                case ModuleType.VariableManager:
                    AddInput("变量输入");
                    AddOutput("变量输出");
                    break;

                case ModuleType.StateManager:
                    AddInput("状态输入");
                    AddOutput("状态输出");
                    break;

                // 函数类（数字逻辑类）：有输入有输出
                case ModuleType.DataFilter:
                case ModuleType.TagSearch:
                case ModuleType.DataCalculation:
                case ModuleType.DataValidation:
                case ModuleType.DataTransform:
                case ModuleType.DataCondition:
                case ModuleType.Other:
                    AddInput("数据输入");
                    AddOutput("处理结果");
                    break;

                // 数组展开节点：有输入有输出
                case ModuleType.ArrayExpansion:
                    AddInput("数组数据");
                    AddOutput("展开数据");
                    break;

                // 整理类：有输入有输出
                case ModuleType.TableManager:
                case ModuleType.GraphicsAPI:
                case ModuleType.ExcelCSV:
                case ModuleType.WordProcessor:
                    AddInput("原始数据");
                    AddOutput("整理结果");
                    break;

                // 输出类：大部分既有输入也有输出（支持链式操作）
                case ModuleType.FileGeneration:
                case ModuleType.ManualLocation:
                case ModuleType.SpecifiedPath:
                case ModuleType.ThirdPartyAPI:
                case ModuleType.CADExport:
                case ModuleType.ExcelExport:
                case ModuleType.CSVExport:
                case ModuleType.WordExport:
                case ModuleType.PPTExport:
                case ModuleType.ImageExport:
                case ModuleType.PublishRelease:
                case ModuleType.NotificationAlert:
                case ModuleType.OtherOutput:
                    AddInput("输入数据");
                    AddOutput("操作结果"); // 支持后续操作
                    break;

                // 纯终端操作：只有输入，没有输出（真正的工作流终点）
                case ModuleType.DialogChat:
                    AddInput("对话输入");
                    break;

                // 智能体节点：有输入有输出
                case ModuleType.AIAgent:
                    AddInput("指令输入");
                    AddOutput("AI响应");
                    break;
            }
        }

        /// <summary>
        /// 添加输入连接点
        /// </summary>
        private void AddInput(string name)
        {
            var input = new ConnectorViewModel
            {
                Title = name,
                IsInput = true,
                Node = this
            };
            Inputs.Add(input);
        }

        /// <summary>
        /// 添加输出连接点
        /// </summary>
        private void AddOutput(string name)
        {
            var output = new ConnectorViewModel
            {
                Title = name,
                IsInput = false,
                Node = this
            };
            Outputs.Add(output);
        }

        /// <summary>
        /// 初始化节点属性
        /// </summary>
        private void InitializeNodeProperties()
        {
            if (Module == null) return;

            var nodeType = NodeTypeRegistry.GetNodeType(Module.Type);

            // 为特定的模块类型分配专门的属性类
            NodeProperties = Module.Type switch
            {
                ModuleType.ManualDataInput => new ManualDataInputProperties(),
                ModuleType.ArrayExpansion => new ArrayExpansionProperties(),
                ModuleType.AIAgent => new AIAgentProperties(),
                _ => nodeType switch
                {
                    NodeType.Input => new InputNodeProperties(),
                    NodeType.Transform => new TransformNodeProperties(),
                    NodeType.Output => new OutputNodeProperties(),
                    NodeType.Control => new ControlNodeProperties(),
                    _ => new TransformNodeProperties() // 默认为转换节点属性
                }
            };

            // 如果模块已有保存的属性值，则加载它们
            if (Module.Parameters.Count > 0)
            {
                foreach (var parameter in Module.Parameters)
                {
                    NodeProperties.SetValue(parameter.Key, parameter.Value);
                }
            }
        }

        /// <summary>
        /// 初始化展开内容
        /// </summary>
        private void InitializeExpandedContent()
        {
            ExpandedContent.Clear();

            if (Module == null) return;

            // 根据模块类型添加不同的展开内容
            switch (Module.Type)
            {
                case ModuleType.DataFilter:
                case ModuleType.TagSearch:
                    // 选择器类型 - 添加文件类型选择
                    ExpandedContent.Add(new FileTypeOption { Name = "jpg", IsSelected = true });
                    ExpandedContent.Add(new FileTypeOption { Name = "png", IsSelected = false });
                    ExpandedContent.Add(new FileTypeOption { Name = "mp3", IsSelected = false });
                    ExpandedContent.Add(new FileTypeOption { Name = "wav", IsSelected = false });
                    break;

                case ModuleType.DataCalculation:
                case ModuleType.DataTransform:
                    // 数据处理类型 - 添加参数配置
                    ExpandedContent.Add(new ParameterOption { Name = "阈值", Value = 50, Min = 0, Max = 100 });
                    ExpandedContent.Add(new ParameterOption { Name = "精度", Value = 2, Min = 1, Max = 10 });
                    break;

                default:
                    // 默认添加一些通用选项
                    ExpandedContent.Add(new TextOption { Label = "描述", Value = Module.Description });
                    break;
            }
        }
    }

    /// <summary>
    /// 文件类型选项
    /// </summary>
    public class FileTypeOption
    {
        public string Name { get; set; } = string.Empty;
        public bool IsSelected { get; set; }
    }

    /// <summary>
    /// 参数选项
    /// </summary>
    public class ParameterOption
    {
        public string Name { get; set; } = string.Empty;
        public double Value { get; set; }
        public double Min { get; set; }
        public double Max { get; set; }
    }

    /// <summary>
    /// 文本选项
    /// </summary>
    public class TextOption
    {
        public string Label { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }
}