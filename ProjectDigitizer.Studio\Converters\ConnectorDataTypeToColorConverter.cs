using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Converters
{
    /// <summary>
    /// 连接器数据类型到颜色的转换器
    /// 根据数据类型和方向（输入/输出）返回相应的颜色
    /// </summary>
    public class ConnectorDataTypeToColorConverter : IMultiValueConverter
    {
        public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
        {
            if (values.Length < 2) return GetDefaultColor();

            // 获取数据类型和方向
            var dataType = values[0] as ConnectorDataType? ?? ConnectorDataType.Any;
            var isInput = values[1] as bool? ?? false;

            return GetConnectorColor(dataType, isInput);
        }

        public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 根据数据类型和方向获取连接器颜色
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <param name="isInput">是否为输入连接器</param>
        /// <returns>颜色</returns>
        private Color GetConnectorColor(ConnectorDataType dataType, bool isInput)
        {
            // 基础颜色根据方向确定
            Color baseColor = isInput ? 
                Color.FromRgb(76, 175, 80) :   // 输入连接器基础绿色 #4CAF50
                Color.FromRgb(33, 150, 243);   // 输出连接器基础蓝色 #2196F3

            // 根据数据类型调整颜色
            switch (dataType)
            {
                case ConnectorDataType.Any:
                    // 使用方向基础色
                    return baseColor;

                case ConnectorDataType.Number:
                    // 数值型 - 橙色
                    return Color.FromRgb(255, 152, 0); // #FF9800

                case ConnectorDataType.Text:
                    // 文本型 - 紫色
                    return Color.FromRgb(156, 39, 176); // #9C27B0

                case ConnectorDataType.Boolean:
                    // 布尔型 - 红色
                    return Color.FromRgb(244, 67, 54); // #F44336

                case ConnectorDataType.File:
                    // 文件型 - 深绿色
                    return Color.FromRgb(56, 142, 60); // #388E3C

                case ConnectorDataType.Geometry:
                    // 几何型 - 青色
                    return Color.FromRgb(0, 188, 212); // #00BCD4

                case ConnectorDataType.Control:
                    // 控制流 - 深紫色
                    return Color.FromRgb(123, 31, 162); // #7B1FA2

                default:
                    return baseColor;
            }
        }

        /// <summary>
        /// 获取默认颜色
        /// </summary>
        /// <returns>默认颜色</returns>
        private Color GetDefaultColor()
        {
            return Color.FromRgb(158, 158, 158); // #9E9E9E 灰色
        }
    }

    /// <summary>
    /// 单值版本的连接器数据类型到颜色转换器
    /// 仅根据数据类型返回颜色，不考虑方向
    /// </summary>
    public class ConnectorDataTypeToColorSimpleConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is ConnectorDataType dataType)
            {
                return GetDataTypeColor(dataType);
            }

            return GetDefaultColor();
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 根据数据类型获取颜色
        /// </summary>
        /// <param name="dataType">数据类型</param>
        /// <returns>颜色</returns>
        private Color GetDataTypeColor(ConnectorDataType dataType)
        {
            switch (dataType)
            {
                case ConnectorDataType.Any:
                    return Color.FromRgb(158, 158, 158); // #9E9E9E 灰色

                case ConnectorDataType.Number:
                    return Color.FromRgb(255, 152, 0); // #FF9800 橙色

                case ConnectorDataType.Text:
                    return Color.FromRgb(156, 39, 176); // #9C27B0 紫色

                case ConnectorDataType.Boolean:
                    return Color.FromRgb(244, 67, 54); // #F44336 红色

                case ConnectorDataType.File:
                    return Color.FromRgb(56, 142, 60); // #388E3C 深绿色

                case ConnectorDataType.Geometry:
                    return Color.FromRgb(0, 188, 212); // #00BCD4 青色

                case ConnectorDataType.Control:
                    return Color.FromRgb(123, 31, 162); // #7B1FA2 深紫色

                default:
                    return GetDefaultColor();
            }
        }

        /// <summary>
        /// 获取默认颜色
        /// </summary>
        /// <returns>默认颜色</returns>
        private Color GetDefaultColor()
        {
            return Color.FromRgb(158, 158, 158); // #9E9E9E 灰色
        }
    }
}
