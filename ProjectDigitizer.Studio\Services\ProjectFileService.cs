using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 项目文件管理服务
    /// </summary>
    public class ProjectFileService
    {
        private readonly JsonSerializerOptions _jsonOptions;

        public ProjectFileService()
        {
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() }
            };
        }

        /// <summary>
        /// 保存项目文件
        /// </summary>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="filePath">文件路径</param>
        public async Task SaveProjectAsync(ProjectFile projectFile, string filePath)
        {
            try
            {
                // 更新最后修改时间
                projectFile.LastModifiedTime = DateTime.Now;

                // 序列化为JSON
                var json = JsonSerializer.Serialize(projectFile, _jsonOptions);

                // 写入文件
                await File.WriteAllTextAsync(filePath, json);

                System.Diagnostics.Debug.WriteLine($"项目已保存到: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存项目失败: {ex.Message}");
                throw new InvalidOperationException($"保存项目失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载项目文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>项目文件数据</returns>
        public async Task<ProjectFile> LoadProjectAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"项目文件不存在: {filePath}");
                }

                // 读取文件内容
                var json = await File.ReadAllTextAsync(filePath);

                // 反序列化
                var projectFile = JsonSerializer.Deserialize<ProjectFile>(json, _jsonOptions);

                if (projectFile == null)
                {
                    throw new InvalidOperationException("项目文件格式无效");
                }

                System.Diagnostics.Debug.WriteLine($"项目已加载: {filePath}");
                return projectFile;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载项目失败: {ex.Message}");
                throw new InvalidOperationException($"加载项目失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 从画布ViewModel创建项目文件数据
        /// </summary>
        /// <param name="canvasViewModel">画布视图模型</param>
        /// <param name="projectInfo">项目信息</param>
        /// <returns>项目文件数据</returns>
        public ProjectFile CreateProjectFileFromCanvas(CanvasViewModel canvasViewModel, ProjectInfo? projectInfo = null)
        {
            var projectFile = new ProjectFile
            {
                ProjectInfo = projectInfo ?? new ProjectInfo(),
                CanvasData = new CanvasData()
            };

            // 转换节点数据
            foreach (var node in canvasViewModel.Nodes)
            {
                var nodeData = new NodeData
                {
                    Id = node.Module?.Id ?? Guid.NewGuid().ToString(),
                    Title = node.Title ?? "",
                    Type = node.Module?.Type ?? ModuleType.PipeLine,
                    X = node.Location.X,
                    Y = node.Location.Y,
                    IsEnabled = node.Module?.IsEnabled ?? true,
                    IsLightBulbOn = node.IsLightBulbOn,
                    IsExpanded = node.IsExpanded,
                    PropertyValues = node.PropertyValues.GetAllValues(),
                    Conditions = node.Module?.Conditions ?? new List<string>()
                };

                // 转换连接器数据
                foreach (var input in node.Inputs)
                {
                    nodeData.Inputs.Add(new ConnectorData
                    {
                        Id = input.Id,
                        Title = input.Title ?? "",
                        DataType = input.DataType,
                        IsInput = true
                    });
                }

                foreach (var output in node.Outputs)
                {
                    nodeData.Outputs.Add(new ConnectorData
                    {
                        Id = output.Id,
                        Title = output.Title ?? "",
                        DataType = output.DataType,
                        IsInput = false
                    });
                }

                projectFile.CanvasData.Nodes.Add(nodeData);
            }

            // 转换连接线数据
            foreach (var connection in canvasViewModel.Connections)
            {
                var connectionData = new ConnectionData
                {
                    Id = connection.Id,
                    SourceNodeId = GetNodeIdFromConnector(connection.Source),
                    SourceConnectorId = connection.Source?.Id ?? "",
                    TargetNodeId = GetNodeIdFromConnector(connection.Target),
                    TargetConnectorId = connection.Target?.Id ?? "",
                    IsEnabled = true // 可以根据需要扩展
                };

                projectFile.CanvasData.Connections.Add(connectionData);
            }

            return projectFile;
        }

        /// <summary>
        /// 从连接器获取所属节点的ID
        /// </summary>
        private static string GetNodeIdFromConnector(ConnectorViewModel? connector)
        {
            if (connector?.Node is ModuleNodeViewModel nodeViewModel)
            {
                return nodeViewModel.Module?.Id ?? "";
            }
            return "";
        }

        /// <summary>
        /// 从项目文件数据恢复到画布ViewModel
        /// </summary>
        /// <param name="projectFile">项目文件数据</param>
        /// <param name="canvasViewModel">画布视图模型</param>
        public void RestoreCanvasFromProjectFile(ProjectFile projectFile, CanvasViewModel canvasViewModel)
        {
            // 清空当前画布
            canvasViewModel.Clear();

            // 恢复节点
            var nodeMap = new Dictionary<string, ModuleNodeViewModel>();

            foreach (var nodeData in projectFile.CanvasData.Nodes)
            {
                var moduleModel = new ModuleModel
                {
                    Id = nodeData.Id,
                    Name = nodeData.Title,
                    Type = nodeData.Type,
                    IsEnabled = nodeData.IsEnabled,
                    Conditions = nodeData.Conditions
                };

                var nodeViewModel = new ModuleNodeViewModel
                {
                    Module = moduleModel,
                    Title = nodeData.Title,
                    Location = new System.Windows.Point(nodeData.X, nodeData.Y),
                    IsLightBulbOn = nodeData.IsLightBulbOn,
                    IsExpanded = nodeData.IsExpanded
                };

                // 恢复属性值
                foreach (var property in nodeData.PropertyValues)
                {
                    nodeViewModel.PropertyValues.SetValue(property.Key, property.Value);
                }

                canvasViewModel.Nodes.Add(nodeViewModel);
                nodeMap[nodeData.Id] = nodeViewModel;
            }

            // 恢复连接线
            foreach (var connectionData in projectFile.CanvasData.Connections)
            {
                if (nodeMap.TryGetValue(connectionData.SourceNodeId, out var sourceNode) &&
                    nodeMap.TryGetValue(connectionData.TargetNodeId, out var targetNode))
                {
                    // 查找对应的连接器
                    var sourceConnector = sourceNode.Outputs.FirstOrDefault(o => o.Id == connectionData.SourceConnectorId);
                    var targetConnector = targetNode.Inputs.FirstOrDefault(i => i.Id == connectionData.TargetConnectorId);

                    if (sourceConnector != null && targetConnector != null)
                    {
                        // 创建连接
                        var connection = new ConnectionViewModel(sourceConnector, targetConnector)
                        {
                            Id = connectionData.Id
                        };

                        canvasViewModel.Connections.Add(connection);
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"已恢复 {projectFile.CanvasData.Nodes.Count} 个节点和 {projectFile.CanvasData.Connections.Count} 个连接");
        }

        /// <summary>
        /// 保存模板文件
        /// </summary>
        /// <param name="templateData">模板数据</param>
        /// <param name="filePath">文件路径</param>
        public async Task SaveTemplateAsync(TemplateData templateData, string filePath)
        {
            try
            {
                var json = JsonSerializer.Serialize(templateData, _jsonOptions);
                await File.WriteAllTextAsync(filePath, json);
                System.Diagnostics.Debug.WriteLine($"模板已保存到: {filePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存模板失败: {ex.Message}");
                throw new InvalidOperationException($"保存模板失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 加载模板文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>模板数据</returns>
        public async Task<TemplateData> LoadTemplateAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"模板文件不存在: {filePath}");
                }

                var json = await File.ReadAllTextAsync(filePath);
                var templateData = JsonSerializer.Deserialize<TemplateData>(json, _jsonOptions);

                if (templateData == null)
                {
                    throw new InvalidOperationException("模板文件格式无效");
                }

                System.Diagnostics.Debug.WriteLine($"模板已加载: {filePath}");
                return templateData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载模板失败: {ex.Message}");
                throw new InvalidOperationException($"加载模板失败: {ex.Message}", ex);
            }
        }
    }
}
