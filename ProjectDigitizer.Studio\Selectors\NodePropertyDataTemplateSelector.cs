using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Selectors
{
    /// <summary>
    /// 节点属性数据模板选择器
    /// </summary>
    public class NodePropertyDataTemplateSelector : DataTemplateSelector
    {
        /// <summary>
        /// 输入节点属性模板
        /// </summary>
        public DataTemplate? InputNodeTemplate { get; set; }

        /// <summary>
        /// 转换节点属性模板
        /// </summary>
        public DataTemplate? TransformNodeTemplate { get; set; }

        /// <summary>
        /// 输出节点属性模板
        /// </summary>
        public DataTemplate? OutputNodeTemplate { get; set; }

        /// <summary>
        /// 控制节点属性模板
        /// </summary>
        public DataTemplate? ControlNodeTemplate { get; set; }

        /// <summary>
        /// 默认属性模板
        /// </summary>
        public DataTemplate? DefaultTemplate { get; set; }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ModuleNodeViewModel nodeViewModel && nodeViewModel.Module != null)
            {
                var nodeType = NodeTypeRegistry.GetNodeType(nodeViewModel.Module.Type);
                
                return nodeType switch
                {
                    NodeType.Input => InputNodeTemplate ?? DefaultTemplate,
                    NodeType.Transform => TransformNodeTemplate ?? DefaultTemplate,
                    NodeType.Output => OutputNodeTemplate ?? DefaultTemplate,
                    NodeType.Control => ControlNodeTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }

    /// <summary>
    /// 节点类型数据模板选择器（用于节点外观）
    /// </summary>
    public class NodeTypeDataTemplateSelector : DataTemplateSelector
    {
        /// <summary>
        /// 输入节点模板
        /// </summary>
        public DataTemplate? InputNodeTemplate { get; set; }

        /// <summary>
        /// 转换节点模板
        /// </summary>
        public DataTemplate? TransformNodeTemplate { get; set; }

        /// <summary>
        /// 输出节点模板
        /// </summary>
        public DataTemplate? OutputNodeTemplate { get; set; }

        /// <summary>
        /// 控制节点模板
        /// </summary>
        public DataTemplate? ControlNodeTemplate { get; set; }

        /// <summary>
        /// 默认节点模板
        /// </summary>
        public DataTemplate? DefaultTemplate { get; set; }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ModuleNodeViewModel nodeViewModel && nodeViewModel.Module != null)
            {
                var nodeType = NodeTypeRegistry.GetNodeType(nodeViewModel.Module.Type);
                
                return nodeType switch
                {
                    NodeType.Input => InputNodeTemplate ?? DefaultTemplate,
                    NodeType.Transform => TransformNodeTemplate ?? DefaultTemplate,
                    NodeType.Output => OutputNodeTemplate ?? DefaultTemplate,
                    NodeType.Control => ControlNodeTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }

    /// <summary>
    /// 连接器类型数据模板选择器
    /// </summary>
    public class ConnectorTypeDataTemplateSelector : DataTemplateSelector
    {
        /// <summary>
        /// 输入连接器模板
        /// </summary>
        public DataTemplate? InputConnectorTemplate { get; set; }

        /// <summary>
        /// 输出连接器模板
        /// </summary>
        public DataTemplate? OutputConnectorTemplate { get; set; }

        /// <summary>
        /// 数据连接器模板
        /// </summary>
        public DataTemplate? DataConnectorTemplate { get; set; }

        /// <summary>
        /// 控制连接器模板
        /// </summary>
        public DataTemplate? ControlConnectorTemplate { get; set; }

        /// <summary>
        /// 默认连接器模板
        /// </summary>
        public DataTemplate? DefaultTemplate { get; set; }

        public override DataTemplate? SelectTemplate(object item, DependencyObject container)
        {
            if (item is ConnectorViewModel connectorViewModel)
            {
                // 根据连接器的数据类型选择模板
                return connectorViewModel.DataType switch
                {
                    "Input" => InputConnectorTemplate ?? DefaultTemplate,
                    "Output" => OutputConnectorTemplate ?? DefaultTemplate,
                    "Data" => DataConnectorTemplate ?? DefaultTemplate,
                    "Control" => ControlConnectorTemplate ?? DefaultTemplate,
                    _ => DefaultTemplate
                };
            }

            return DefaultTemplate;
        }
    }
}
