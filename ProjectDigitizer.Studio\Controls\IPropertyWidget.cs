using System.Windows;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 属性编辑器控件接口
    /// </summary>
    public interface IPropertyWidget
    {
        /// <summary>
        /// 属性定义
        /// </summary>
        PropertyDefinition PropertyDefinition { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        object? Value { get; set; }

        /// <summary>
        /// 值变化事件
        /// </summary>
        event System.EventHandler<PropertyValueChangedEventArgs> ValueChanged;

        /// <summary>
        /// 设置是否可编辑
        /// </summary>
        bool IsEnabled { get; set; }

        /// <summary>
        /// 验证当前值
        /// </summary>
        Models.ValidationResult Validate();

        /// <summary>
        /// 获取控件
        /// </summary>
        FrameworkElement GetElement();
    }

    /// <summary>
    /// 属性值变化事件参数
    /// </summary>
    public class PropertyValueChangedEventArgs : System.EventArgs
    {
        public string PropertyName { get; }
        public object? OldValue { get; }
        public object? NewValue { get; }

        public PropertyValueChangedEventArgs(string propertyName, object? oldValue, object? newValue)
        {
            PropertyName = propertyName;
            OldValue = oldValue;
            NewValue = newValue;
        }
    }


}