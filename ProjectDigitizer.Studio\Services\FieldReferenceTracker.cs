using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using ProjectDigitizer.Studio.Models;
using ProjectDigitizer.Studio.ViewModels;

namespace ProjectDigitizer.Studio.Services
{
    /// <summary>
    /// 字段引用追踪器 - 管理字段引用状态和双向同步
    /// </summary>
    public class FieldReferenceTracker
    {
        private readonly Dictionary<string, HashSet<string>> _fieldReferences; // 字段ID -> 引用它的表达式ID集合
        private readonly Dictionary<string, HashSet<string>> _expressionReferences; // 表达式ID -> 引用的字段ID集合
        private readonly Dictionary<string, FieldInfo> _fieldInfoCache; // 字段ID -> 字段信息缓存
        private readonly Dictionary<string, ConnectionViewModel> _connectionCache; // 字段ID -> 连接信息缓存

        /// <summary>字段引用状态变化事件</summary>
        public event EventHandler<FieldReferenceChangedEventArgs>? FieldReferenceChanged;

        /// <summary>连接状态变化事件</summary>
        public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

        public FieldReferenceTracker()
        {
            _fieldReferences = new Dictionary<string, HashSet<string>>();
            _expressionReferences = new Dictionary<string, HashSet<string>>();
            _fieldInfoCache = new Dictionary<string, FieldInfo>();
            _connectionCache = new Dictionary<string, ConnectionViewModel>();
        }

        /// <summary>
        /// 更新表达式的字段引用
        /// </summary>
        /// <param name="expressionId">表达式ID</param>
        /// <param name="expression">表达式内容</param>
        public void UpdateExpressionReferences(string expressionId, string expression)
        {
            if (string.IsNullOrEmpty(expressionId))
                return;

            // 解析表达式中的字段引用
            var referencedFields = ParseFieldReferences(expression);

            // 获取之前的引用
            var oldReferences = _expressionReferences.ContainsKey(expressionId)
                ? _expressionReferences[expressionId].ToList()
                : new List<string>();

            // 更新表达式引用记录
            _expressionReferences[expressionId] = new HashSet<string>(referencedFields);

            // 处理移除的引用
            var removedReferences = oldReferences.Except(referencedFields).ToList();
            foreach (var fieldId in removedReferences)
            {
                RemoveFieldReference(fieldId, expressionId);
            }

            // 处理新增的引用
            var addedReferences = referencedFields.Except(oldReferences).ToList();
            foreach (var fieldId in addedReferences)
            {
                AddFieldReference(fieldId, expressionId);
            }
        }

        /// <summary>
        /// 移除表达式的所有字段引用
        /// </summary>
        /// <param name="expressionId">表达式ID</param>
        public void RemoveExpressionReferences(string expressionId)
        {
            if (!_expressionReferences.ContainsKey(expressionId))
                return;

            var referencedFields = _expressionReferences[expressionId].ToList();
            foreach (var fieldId in referencedFields)
            {
                RemoveFieldReference(fieldId, expressionId);
            }

            _expressionReferences.Remove(expressionId);
        }

        /// <summary>
        /// 检查字段是否被引用
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <returns>是否被引用</returns>
        public bool IsFieldReferenced(string fieldId)
        {
            return _fieldReferences.ContainsKey(fieldId) && _fieldReferences[fieldId].Any();
        }

        /// <summary>
        /// 获取引用指定字段的表达式列表
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <returns>表达式ID列表</returns>
        public List<string> GetReferencingExpressions(string fieldId)
        {
            return _fieldReferences.ContainsKey(fieldId)
                ? _fieldReferences[fieldId].ToList()
                : new List<string>();
        }

        /// <summary>
        /// 获取表达式引用的字段列表
        /// </summary>
        /// <param name="expressionId">表达式ID</param>
        /// <returns>字段ID列表</returns>
        public List<string> GetReferencedFields(string expressionId)
        {
            return _expressionReferences.ContainsKey(expressionId)
                ? _expressionReferences[expressionId].ToList()
                : new List<string>();
        }

        /// <summary>
        /// 通知连接状态变化
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <param name="isConnected">是否已连接</param>
        /// <param name="connection">连接对象</param>
        public void NotifyConnectionStateChanged(string fieldId, bool isConnected, ConnectionViewModel? connection = null)
        {
            // 更新连接缓存
            if (isConnected && connection != null)
            {
                _connectionCache[fieldId] = connection;
            }
            else
            {
                _connectionCache.Remove(fieldId);
            }

            // 更新字段信息缓存中的连接状态
            if (_fieldInfoCache.ContainsKey(fieldId))
            {
                _fieldInfoCache[fieldId].IsConnected = isConnected;
            }

            // 触发连接状态变化事件
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(fieldId, isConnected, connection));

            // 更新连接线样式
            UpdateConnectionStyle(fieldId);
        }

        /// <summary>
        /// 注册字段信息
        /// </summary>
        /// <param name="fieldInfo">字段信息</param>
        public void RegisterField(FieldInfo fieldInfo)
        {
            if (fieldInfo != null && !string.IsNullOrEmpty(fieldInfo.Name))
            {
                _fieldInfoCache[fieldInfo.Name] = fieldInfo;
            }
        }

        /// <summary>
        /// 注销字段信息
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        public void UnregisterField(string fieldId)
        {
            _fieldInfoCache.Remove(fieldId);
            _fieldReferences.Remove(fieldId);
            _connectionCache.Remove(fieldId);
        }

        /// <summary>
        /// 获取字段的引用状态
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <returns>引用状态</returns>
        public FieldReferenceState GetFieldReferenceState(string fieldId)
        {
            var isReferenced = IsFieldReferenced(fieldId);
            var isConnected = _connectionCache.ContainsKey(fieldId);

            return isReferenced && isConnected ? FieldReferenceState.Active :
                   isReferenced ? FieldReferenceState.Referenced :
                   isConnected ? FieldReferenceState.Connected :
                   FieldReferenceState.Available;
        }

        /// <summary>
        /// 清空所有引用记录
        /// </summary>
        public void Clear()
        {
            _fieldReferences.Clear();
            _expressionReferences.Clear();
            _fieldInfoCache.Clear();
            _connectionCache.Clear();
        }

        /// <summary>
        /// 解析表达式中的字段引用
        /// </summary>
        /// <param name="expression">表达式内容</param>
        /// <returns>字段ID列表</returns>
        private List<string> ParseFieldReferences(string expression)
        {
            if (string.IsNullOrWhiteSpace(expression))
                return new List<string>();

            var fieldReferences = new HashSet<string>();

            // 使用正则表达式匹配字段引用模式
            // 支持多种引用格式：{fieldName}、[fieldName]、fieldName（标识符）
            var patterns = new[]
            {
                @"\{([a-zA-Z_][a-zA-Z0-9_]*)\}",  // {fieldName}
                @"\[([a-zA-Z_][a-zA-Z0-9_]*)\]",  // [fieldName]
                @"\b([a-zA-Z_][a-zA-Z0-9_]*)\b"   // fieldName (标识符)
            };

            foreach (var pattern in patterns)
            {
                var matches = Regex.Matches(expression, pattern);
                foreach (Match match in matches)
                {
                    if (match.Groups.Count > 1)
                    {
                        var fieldName = match.Groups[1].Value;
                        // 排除函数名和关键字
                        if (!IsReservedWord(fieldName))
                        {
                            fieldReferences.Add(fieldName);
                        }
                    }
                }
            }

            return fieldReferences.ToList();
        }

        /// <summary>
        /// 检查是否为保留字（函数名、关键字等）
        /// </summary>
        /// <param name="word">单词</param>
        /// <returns>是否为保留字</returns>
        private bool IsReservedWord(string word)
        {
            var reservedWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                // 数学函数
                "SUM", "AVG", "COUNT", "MAX", "MIN", "ABS", "ROUND", "FLOOR", "CEIL",
                "SQRT", "POW", "LOG", "EXP", "SIN", "COS", "TAN",
                
                // 字符串函数
                "CONCAT", "SUBSTRING", "LENGTH", "UPPER", "LOWER", "TRIM",
                
                // 逻辑关键字
                "AND", "OR", "NOT", "IF", "THEN", "ELSE", "TRUE", "FALSE",
                
                // 其他关键字
                "NULL", "EMPTY", "IS", "IN", "LIKE", "BETWEEN"
            };

            return reservedWords.Contains(word);
        }

        /// <summary>
        /// 添加字段引用
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <param name="expressionId">表达式ID</param>
        private void AddFieldReference(string fieldId, string expressionId)
        {
            if (!_fieldReferences.ContainsKey(fieldId))
            {
                _fieldReferences[fieldId] = new HashSet<string>();
            }

            var wasReferenced = _fieldReferences[fieldId].Any();
            _fieldReferences[fieldId].Add(expressionId);
            var isReferenced = _fieldReferences[fieldId].Any();

            // 更新字段信息缓存
            if (_fieldInfoCache.ContainsKey(fieldId))
            {
                _fieldInfoCache[fieldId].IsReferenced = isReferenced;
            }

            // 如果引用状态发生变化，触发事件
            if (wasReferenced != isReferenced)
            {
                FieldReferenceChanged?.Invoke(this, new FieldReferenceChangedEventArgs(fieldId, isReferenced, expressionId));
                UpdateConnectionStyle(fieldId);
            }
        }

        /// <summary>
        /// 移除字段引用
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        /// <param name="expressionId">表达式ID</param>
        private void RemoveFieldReference(string fieldId, string expressionId)
        {
            if (!_fieldReferences.ContainsKey(fieldId))
                return;

            var wasReferenced = _fieldReferences[fieldId].Any();
            _fieldReferences[fieldId].Remove(expressionId);
            var isReferenced = _fieldReferences[fieldId].Any();

            // 如果没有引用了，移除记录
            if (!isReferenced)
            {
                _fieldReferences.Remove(fieldId);
            }

            // 更新字段信息缓存
            if (_fieldInfoCache.ContainsKey(fieldId))
            {
                _fieldInfoCache[fieldId].IsReferenced = isReferenced;
            }

            // 如果引用状态发生变化，触发事件
            if (wasReferenced != isReferenced)
            {
                FieldReferenceChanged?.Invoke(this, new FieldReferenceChangedEventArgs(fieldId, isReferenced, expressionId));
                UpdateConnectionStyle(fieldId);
            }
        }

        /// <summary>
        /// 更新连接线样式
        /// </summary>
        /// <param name="fieldId">字段ID</param>
        private void UpdateConnectionStyle(string fieldId)
        {
            if (_connectionCache.ContainsKey(fieldId))
            {
                var connection = _connectionCache[fieldId];
                var isReferenced = IsFieldReferenced(fieldId);

                // 更新连接线的引用状态
                connection.IsFieldReferenced = isReferenced;
            }
        }
    }

    /// <summary>
    /// 连接状态变化事件参数
    /// </summary>
    public class ConnectionStateChangedEventArgs : EventArgs
    {
        public string FieldId { get; }
        public bool IsConnected { get; }
        public ConnectionViewModel? Connection { get; }

        public ConnectionStateChangedEventArgs(string fieldId, bool isConnected, ConnectionViewModel? connection)
        {
            FieldId = fieldId;
            IsConnected = isConnected;
            Connection = connection;
        }
    }
}
