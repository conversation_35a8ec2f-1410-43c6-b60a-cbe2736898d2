using System;
using System.Collections.Generic;

namespace ProjectDigitizer.Studio.Models
{
    /// <summary>
    /// 统计聚合结果
    /// </summary>
    public class AggregationResult
    {
        /// <summary>
        /// 统计函数类型
        /// </summary>
        public AggregationFunction Function { get; set; }

        /// <summary>
        /// 统计字段名称
        /// </summary>
        public string FieldName { get; set; } = string.Empty;

        /// <summary>
        /// 统计结果值
        /// </summary>
        public object? Value { get; set; }

        /// <summary>
        /// 参与统计的记录数量
        /// </summary>
        public int RecordCount { get; set; }

        /// <summary>
        /// 有效值数量（排除null值）
        /// </summary>
        public int ValidValueCount { get; set; }

        /// <summary>
        /// 统计是否成功
        /// </summary>
        public bool IsSuccess { get; set; } = true;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 统计执行时间
        /// </summary>
        public TimeSpan ExecutionTime { get; set; }

        /// <summary>
        /// 格式化显示的结果
        /// </summary>
        public string FormattedValue
        {
            get
            {
                if (!IsSuccess || Value == null)
                    return "N/A";

                return Function switch
                {
                    AggregationFunction.Count => Value.ToString() ?? "0",
                    AggregationFunction.Sum => FormatNumber(Value),
                    AggregationFunction.Average => FormatNumber(Value, 2),
                    AggregationFunction.Min => Value.ToString() ?? "N/A",
                    AggregationFunction.Max => Value.ToString() ?? "N/A",
                    _ => Value.ToString() ?? "N/A"
                };
            }
        }

        /// <summary>
        /// 统计结果的详细描述
        /// </summary>
        public string Description
        {
            get
            {
                if (!IsSuccess)
                    return $"统计失败: {ErrorMessage}";

                var functionName = Function switch
                {
                    AggregationFunction.Count => "计数",
                    AggregationFunction.Sum => "求和",
                    AggregationFunction.Average => "平均值",
                    AggregationFunction.Min => "最小值",
                    AggregationFunction.Max => "最大值",
                    _ => "未知"
                };

                var fieldDesc = string.IsNullOrEmpty(FieldName) ? "所有记录" : $"字段 '{FieldName}'";
                return $"{fieldDesc} 的{functionName}: {FormattedValue}";
            }
        }

        /// <summary>
        /// 格式化数字显示
        /// </summary>
        private static string FormatNumber(object? value, int decimals = 0)
        {
            if (value == null) return "N/A";

            if (double.TryParse(value.ToString(), out var number))
            {
                return decimals > 0 ? number.ToString($"F{decimals}") : number.ToString("F0");
            }

            return value.ToString() ?? "N/A";
        }
    }

    /// <summary>
    /// 多个统计结果的集合
    /// </summary>
    public class AggregationResultSet
    {
        /// <summary>
        /// 统计结果列表
        /// </summary>
        public List<AggregationResult> Results { get; set; } = new();

        /// <summary>
        /// 总的处理记录数
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// 筛选后的记录数
        /// </summary>
        public int FilteredRecords { get; set; }

        /// <summary>
        /// 统计执行的总时间
        /// </summary>
        public TimeSpan TotalExecutionTime { get; set; }

        /// <summary>
        /// 是否所有统计都成功
        /// </summary>
        public bool IsAllSuccess => Results.TrueForAll(r => r.IsSuccess);

        /// <summary>
        /// 错误信息列表
        /// </summary>
        public List<string> ErrorMessages => Results
            .Where(r => !r.IsSuccess && !string.IsNullOrEmpty(r.ErrorMessage))
            .Select(r => r.ErrorMessage!)
            .ToList();

        /// <summary>
        /// 添加统计结果
        /// </summary>
        public void AddResult(AggregationResult result)
        {
            Results.Add(result);
        }

        /// <summary>
        /// 获取指定函数的统计结果
        /// </summary>
        public AggregationResult? GetResult(AggregationFunction function, string? fieldName = null)
        {
            return Results.FirstOrDefault(r => 
                r.Function == function && 
                (fieldName == null || r.FieldName == fieldName));
        }

        /// <summary>
        /// 获取格式化的摘要信息
        /// </summary>
        public string GetSummary()
        {
            if (Results.Count == 0)
                return "无统计结果";

            var successCount = Results.Count(r => r.IsSuccess);
            var summary = $"共 {Results.Count} 项统计，{successCount} 项成功";

            if (FilteredRecords != TotalRecords)
            {
                summary += $"，处理 {FilteredRecords}/{TotalRecords} 条记录";
            }
            else
            {
                summary += $"，处理 {TotalRecords} 条记录";
            }

            return summary;
        }
    }

    /// <summary>
    /// 统计函数类型
    /// </summary>
    public enum AggregationFunction
    {
        /// <summary>
        /// 计数
        /// </summary>
        Count,

        /// <summary>
        /// 求和
        /// </summary>
        Sum,

        /// <summary>
        /// 平均值
        /// </summary>
        Average,

        /// <summary>
        /// 最小值
        /// </summary>
        Min,

        /// <summary>
        /// 最大值
        /// </summary>
        Max
    }

    /// <summary>
    /// 统计配置
    /// </summary>
    public class AggregationConfig
    {
        /// <summary>
        /// 统计函数
        /// </summary>
        public AggregationFunction Function { get; set; }

        /// <summary>
        /// 统计字段名称（Count函数可为空）
        /// </summary>
        public string? FieldName { get; set; }

        /// <summary>
        /// 是否忽略null值
        /// </summary>
        public bool IgnoreNullValues { get; set; } = true;

        /// <summary>
        /// 是否忽略空字符串
        /// </summary>
        public bool IgnoreEmptyStrings { get; set; } = true;

        /// <summary>
        /// 数值精度（用于Average等函数）
        /// </summary>
        public int Precision { get; set; } = 2;

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            // Count函数不需要字段名
            if (Function == AggregationFunction.Count)
                return true;

            // 其他函数需要字段名
            return !string.IsNullOrWhiteSpace(FieldName);
        }

        /// <summary>
        /// 获取配置描述
        /// </summary>
        public string GetDescription()
        {
            var functionName = Function switch
            {
                AggregationFunction.Count => "计数",
                AggregationFunction.Sum => "求和",
                AggregationFunction.Average => "平均值",
                AggregationFunction.Min => "最小值",
                AggregationFunction.Max => "最大值",
                _ => "未知"
            };

            if (Function == AggregationFunction.Count)
                return $"{functionName}(所有记录)";

            return $"{functionName}({FieldName})";
        }
    }
}
