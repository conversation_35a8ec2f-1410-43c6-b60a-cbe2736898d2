using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 函数执行结果面板交互逻辑
    /// </summary>
    public partial class FunctionResultPanel : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<FunctionResultItem> _functionResults = new();
        private ObservableCollection<FunctionExpression> _functions = new();

        /// <summary>函数执行结果列表</summary>
        public ObservableCollection<FunctionResultItem> FunctionResults
        {
            get => _functionResults;
            set
            {
                if (SetProperty(ref _functionResults, value))
                {
                    UpdateStatistics();
                }
            }
        }

        /// <summary>函数表达式列表</summary>
        public ObservableCollection<FunctionExpression> Functions
        {
            get => _functions;
            set => SetProperty(ref _functions, value);
        }

        /// <summary>结果数量</summary>
        public int ResultCount => _functionResults.Count;

        /// <summary>是否有结果</summary>
        public bool HasResults => _functionResults.Any();

        /// <summary>成功数量</summary>
        public int SuccessCount => _functionResults.Count(r => r.IsSuccess);

        /// <summary>错误数量</summary>
        public int ErrorCount => _functionResults.Count(r => !r.IsSuccess);

        /// <summary>总执行时间</summary>
        public long TotalExecutionTime => _functionResults.Sum(r => r.ExecutionTime);

        /// <summary>函数执行事件</summary>
        public event EventHandler? ExecuteAllRequested;

        /// <summary>结果导出事件</summary>
        public event EventHandler<FunctionResultExportEventArgs>? ResultExportRequested;

        public FunctionResultPanel()
        {
            InitializeComponent();
            DataContext = this;
            InitializeEventHandlers();
        }

        /// <summary>
        /// 初始化事件处理器
        /// </summary>
        private void InitializeEventHandlers()
        {
            ExecuteAllButton.Click += OnExecuteAllClick;
            RefreshButton.Click += OnRefreshClick;
            ExportButton.Click += OnExportClick;
            FormatButton.Click += OnFormatClick;
            MaterialLibraryButton.Click += OnMaterialLibraryClick;

            // 监听结果集合变化
            _functionResults.CollectionChanged += (s, e) => UpdateStatistics();
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            OnPropertyChanged(nameof(ResultCount));
            OnPropertyChanged(nameof(HasResults));
            OnPropertyChanged(nameof(SuccessCount));
            OnPropertyChanged(nameof(ErrorCount));
            OnPropertyChanged(nameof(TotalExecutionTime));
        }

        /// <summary>
        /// 添加函数执行结果
        /// </summary>
        public void AddResult(FunctionResultItem result)
        {
            if (result != null)
            {
                _functionResults.Add(result);
            }
        }

        /// <summary>
        /// 清空所有结果
        /// </summary>
        public void ClearResults()
        {
            _functionResults.Clear();
        }

        /// <summary>
        /// 更新函数结果
        /// </summary>
        public void UpdateFunctionResult(string functionId, object? result, bool isSuccess, string? errorMessage = null, long executionTime = 0)
        {
            var existingResult = _functionResults.FirstOrDefault(r => r.FunctionId == functionId);
            if (existingResult != null)
            {
                existingResult.RawResult = result;
                existingResult.IsSuccess = isSuccess;
                existingResult.ErrorMessage = errorMessage;
                existingResult.ExecutionTime = executionTime;
                existingResult.UpdateFormattedResult();
            }
            else
            {
                var function = _functions.FirstOrDefault(f => f.Id == functionId);
                if (function != null)
                {
                    var resultItem = new FunctionResultItem
                    {
                        FunctionId = functionId,
                        FunctionName = function.Name,
                        Expression = function.Expression,
                        RawResult = result,
                        IsSuccess = isSuccess,
                        ErrorMessage = errorMessage,
                        ExecutionTime = executionTime
                    };
                    resultItem.UpdateFormattedResult();
                    AddResult(resultItem);
                }
            }
        }

        /// <summary>
        /// 执行所有函数按钮点击
        /// </summary>
        private void OnExecuteAllClick(object sender, RoutedEventArgs e)
        {
            ExecuteAllRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 刷新按钮点击
        /// </summary>
        private void OnRefreshClick(object sender, RoutedEventArgs e)
        {
            ExecuteAllRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 导出按钮点击
        /// </summary>
        private void OnExportClick(object sender, RoutedEventArgs e)
        {
            var args = new FunctionResultExportEventArgs(_functionResults.ToList(), ExportType.All);
            ResultExportRequested?.Invoke(this, args);
        }

        /// <summary>
        /// 格式设置按钮点击
        /// </summary>
        private void OnFormatClick(object sender, RoutedEventArgs e)
        {
            FormatSettingsPopup.IsOpen = true;
        }

        /// <summary>
        /// 材料库绑定按钮点击
        /// </summary>
        private void OnMaterialLibraryClick(object sender, RoutedEventArgs e)
        {
            MaterialLibraryPopup.IsOpen = true;
        }

        /// <summary>
        /// 复制结果按钮点击
        /// </summary>
        private void OnCopyResultClick(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is FunctionResultItem result)
            {
                Clipboard.SetText(result.FormattedResult ?? result.RawResult?.ToString() ?? "");
            }
        }

        /// <summary>
        /// 复制整行按钮点击
        /// </summary>
        private void OnCopyRowClick(object sender, RoutedEventArgs e)
        {
            if (ResultDataGrid.SelectedItem is FunctionResultItem result)
            {
                var rowText = $"{result.FunctionName}\t{result.Expression}\t{result.FormattedResult}\t{result.ResultType}\t{result.Unit}\t{result.ExecutionTime}";
                if (!result.IsSuccess && !string.IsNullOrEmpty(result.ErrorMessage))
                {
                    rowText += $"\t{result.ErrorMessage}";
                }
                Clipboard.SetText(rowText);
            }
        }

        /// <summary>
        /// 格式设置按钮点击
        /// </summary>
        private void OnFormatSettingsClick(object sender, RoutedEventArgs e)
        {
            FormatSettingsPopup.IsOpen = true;
        }

        /// <summary>
        /// 材料库绑定按钮点击
        /// </summary>
        private void OnMaterialLibraryBindClick(object sender, RoutedEventArgs e)
        {
            MaterialLibraryPopup.IsOpen = true;
        }

        /// <summary>
        /// 导出选中项
        /// </summary>
        private void OnExportSelectedClick(object sender, RoutedEventArgs e)
        {
            var selectedResults = ResultDataGrid.SelectedItems.Cast<FunctionResultItem>().ToList();
            if (selectedResults.Any())
            {
                var args = new FunctionResultExportEventArgs(selectedResults, ExportType.Selected);
                ResultExportRequested?.Invoke(this, args);
            }
        }

        /// <summary>
        /// 导出全部
        /// </summary>
        private void OnExportAllClick(object sender, RoutedEventArgs e)
        {
            var args = new FunctionResultExportEventArgs(_functionResults.ToList(), ExportType.All);
            ResultExportRequested?.Invoke(this, args);
        }

        /// <summary>
        /// 格式应用按钮点击
        /// </summary>
        private void OnFormatApplyClick(object sender, RoutedEventArgs e)
        {
            // 获取格式设置
            var numberFormat = (NumberFormatComboBox.SelectedItem as ComboBoxItem)?.Tag?.ToString() ?? "default";
            var unit = UnitTextBox.Text?.Trim();
            var label = LabelTextBox.Text?.Trim();

            // 应用格式到选中的结果
            var selectedResults = ResultDataGrid.SelectedItems.Cast<FunctionResultItem>().ToList();
            if (!selectedResults.Any())
            {
                selectedResults = _functionResults.ToList();
            }

            foreach (var result in selectedResults)
            {
                result.NumberFormat = numberFormat;
                if (!string.IsNullOrEmpty(unit))
                {
                    result.Unit = unit;
                }
                if (!string.IsNullOrEmpty(label))
                {
                    result.Label = label;
                }
                result.UpdateFormattedResult();
            }

            FormatSettingsPopup.IsOpen = false;
        }

        /// <summary>
        /// 格式取消按钮点击
        /// </summary>
        private void OnFormatCancelClick(object sender, RoutedEventArgs e)
        {
            FormatSettingsPopup.IsOpen = false;
        }

        /// <summary>
        /// 材料库绑定应用按钮点击
        /// </summary>
        private void OnMaterialBindApplyClick(object sender, RoutedEventArgs e)
        {
            // TODO: 实现材料库绑定逻辑
            MessageBox.Show("材料库绑定功能开发中...", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            MaterialLibraryPopup.IsOpen = false;
        }

        /// <summary>
        /// 材料库绑定取消按钮点击
        /// </summary>
        private void OnMaterialBindCancelClick(object sender, RoutedEventArgs e)
        {
            MaterialLibraryPopup.IsOpen = false;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 函数执行结果项
    /// </summary>
    public class FunctionResultItem : INotifyPropertyChanged
    {
        private string _functionId = string.Empty;
        private string _functionName = string.Empty;
        private string _expression = string.Empty;
        private object? _rawResult;
        private string? _formattedResult;
        private string _resultType = string.Empty;
        private string _unit = string.Empty;
        private string _label = string.Empty;
        private string _numberFormat = "default";
        private bool _isSuccess = true;
        private string? _errorMessage;
        private long _executionTime;

        public string FunctionId
        {
            get => _functionId;
            set => SetProperty(ref _functionId, value);
        }

        public string FunctionName
        {
            get => _functionName;
            set => SetProperty(ref _functionName, value);
        }

        public string Expression
        {
            get => _expression;
            set => SetProperty(ref _expression, value);
        }

        public object? RawResult
        {
            get => _rawResult;
            set
            {
                if (SetProperty(ref _rawResult, value))
                {
                    UpdateResultType();
                    UpdateFormattedResult();
                }
            }
        }

        public string? FormattedResult
        {
            get => _formattedResult;
            private set => SetProperty(ref _formattedResult, value);
        }

        public string ResultType
        {
            get => _resultType;
            private set => SetProperty(ref _resultType, value);
        }

        public string Unit
        {
            get => _unit;
            set
            {
                if (SetProperty(ref _unit, value))
                {
                    UpdateFormattedResult();
                }
            }
        }

        public string Label
        {
            get => _label;
            set => SetProperty(ref _label, value);
        }

        public string NumberFormat
        {
            get => _numberFormat;
            set
            {
                if (SetProperty(ref _numberFormat, value))
                {
                    UpdateFormattedResult();
                }
            }
        }

        public bool IsSuccess
        {
            get => _isSuccess;
            set
            {
                if (SetProperty(ref _isSuccess, value))
                {
                    OnPropertyChanged(nameof(HasError));
                }
            }
        }

        public string? ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (SetProperty(ref _errorMessage, value))
                {
                    OnPropertyChanged(nameof(HasError));
                }
            }
        }

        public long ExecutionTime
        {
            get => _executionTime;
            set => SetProperty(ref _executionTime, value);
        }

        public bool HasError => !_isSuccess || !string.IsNullOrEmpty(_errorMessage);

        public bool CanBindToMaterialLibrary => _isSuccess && _rawResult != null;

        /// <summary>
        /// 更新结果类型
        /// </summary>
        private void UpdateResultType()
        {
            if (_rawResult == null)
            {
                ResultType = "null";
            }
            else
            {
                var type = _rawResult.GetType();
                ResultType = type.Name switch
                {
                    "Int32" or "Int64" or "Double" or "Decimal" or "Single" => "Number",
                    "String" => "Text",
                    "Boolean" => "Boolean",
                    "DateTime" => "DateTime",
                    _ => type.Name
                };
            }
        }

        /// <summary>
        /// 更新格式化结果
        /// </summary>
        public void UpdateFormattedResult()
        {
            if (!_isSuccess || _rawResult == null)
            {
                FormattedResult = _errorMessage ?? "错误";
                return;
            }

            try
            {
                var formatted = FormatValue(_rawResult, _numberFormat);
                FormattedResult = string.IsNullOrEmpty(_unit) ? formatted : $"{formatted} {_unit}";
            }
            catch
            {
                FormattedResult = _rawResult.ToString();
            }
        }

        /// <summary>
        /// 格式化数值
        /// </summary>
        private string FormatValue(object value, string format)
        {
            return format switch
            {
                "integer" when value is IConvertible convertible => ((long)convertible.ToDouble(null)).ToString(),
                "decimal2" when value is IConvertible convertible => convertible.ToDouble(null).ToString("F2"),
                "decimal4" when value is IConvertible convertible => convertible.ToDouble(null).ToString("F4"),
                "percentage" when value is IConvertible convertible => (convertible.ToDouble(null) * 100).ToString("F2") + "%",
                "scientific" when value is IConvertible convertible => convertible.ToDouble(null).ToString("E2"),
                _ => value.ToString() ?? ""
            };
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    /// <summary>
    /// 导出类型
    /// </summary>
    public enum ExportType
    {
        All,
        Selected
    }

    /// <summary>
    /// 函数结果导出事件参数
    /// </summary>
    public class FunctionResultExportEventArgs : EventArgs
    {
        public List<FunctionResultItem> Results { get; }
        public ExportType ExportType { get; }

        public FunctionResultExportEventArgs(List<FunctionResultItem> results, ExportType exportType)
        {
            Results = results;
            ExportType = exportType;
        }
    }
}
