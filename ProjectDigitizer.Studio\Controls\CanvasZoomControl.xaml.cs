using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 画布缩放控制器
    /// </summary>
    public partial class CanvasZoomControl : UserControl, INotifyPropertyChanged
    {
        private double _zoomPercentage = 100.0;
        private double _minZoom = 10.0;
        private double _maxZoom = 500.0;
        private double _zoomStep = 10.0;

        /// <summary>
        /// 缩放百分比
        /// </summary>
        public double ZoomPercentage
        {
            get => _zoomPercentage;
            set
            {
                if (Math.Abs(_zoomPercentage - value) > 0.01)
                {
                    _zoomPercentage = Math.Max(_minZoom, Math.Min(_maxZoom, value));
                    OnPropertyChanged();
                    ZoomChanged?.Invoke(this, new ZoomChangedEventArgs(_zoomPercentage / 100.0));
                }
            }
        }

        /// <summary>
        /// 最小缩放百分比
        /// </summary>
        public double MinZoom
        {
            get => _minZoom;
            set
            {
                _minZoom = Math.Max(1.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 最大缩放百分比
        /// </summary>
        public double MaxZoom
        {
            get => _maxZoom;
            set
            {
                _maxZoom = Math.Min(1000.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 缩放步长
        /// </summary>
        public double ZoomStep
        {
            get => _zoomStep;
            set
            {
                _zoomStep = Math.Max(1.0, value);
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// 缩放变化事件
        /// </summary>
        public event EventHandler<ZoomChangedEventArgs>? ZoomChanged;

        /// <summary>
        /// 重置缩放事件
        /// </summary>
        public event EventHandler? ResetZoomRequested;

        /// <summary>
        /// 适应画布事件
        /// </summary>
        public event EventHandler? FitToCanvasRequested;

        public CanvasZoomControl()
        {
            InitializeComponent();
            DataContext = this;
        }

        /// <summary>
        /// 设置缩放值（不触发事件）
        /// </summary>
        /// <param name="zoomLevel">缩放级别（0.1 = 10%, 1.0 = 100%）</param>
        public void SetZoomLevel(double zoomLevel)
        {
            var percentage = zoomLevel * 100.0;
            if (Math.Abs(_zoomPercentage - percentage) > 0.01)
            {
                _zoomPercentage = Math.Max(_minZoom, Math.Min(_maxZoom, percentage));
                OnPropertyChanged(nameof(ZoomPercentage));
            }
        }

        /// <summary>
        /// 放大
        /// </summary>
        public void ZoomIn()
        {
            ZoomPercentage += ZoomStep;
        }

        /// <summary>
        /// 缩小
        /// </summary>
        public void ZoomOut()
        {
            ZoomPercentage -= ZoomStep;
        }

        /// <summary>
        /// 重置缩放到100%
        /// </summary>
        public void ResetZoom()
        {
            ZoomPercentage = 100.0;
            ResetZoomRequested?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 适应画布
        /// </summary>
        public void FitToCanvas()
        {
            FitToCanvasRequested?.Invoke(this, EventArgs.Empty);
        }

        private void ZoomInButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomIn();
        }

        private void ZoomOutButton_Click(object sender, RoutedEventArgs e)
        {
            ZoomOut();
        }

        private void ResetZoomButton_Click(object sender, RoutedEventArgs e)
        {
            ResetZoom();
        }

        private void FitToCanvasButton_Click(object sender, RoutedEventArgs e)
        {
            FitToCanvas();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 缩放变化事件参数
    /// </summary>
    public class ZoomChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 缩放级别（0.1 = 10%, 1.0 = 100%）
        /// </summary>
        public double ZoomLevel { get; }

        /// <summary>
        /// 缩放百分比（10 = 10%, 100 = 100%）
        /// </summary>
        public double ZoomPercentage => ZoomLevel * 100.0;

        public ZoomChangedEventArgs(double zoomLevel)
        {
            ZoomLevel = zoomLevel;
        }
    }
}
