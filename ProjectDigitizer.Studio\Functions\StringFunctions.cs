using System;
using System.Collections.Generic;
using System.Linq;
using ProjectDigitizer.Studio.Interfaces;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Functions
{
    /// <summary>
    /// 字符串连接函数
    /// </summary>
    public class ConcatFunction : FunctionProviderBase
    {
        public override string Name => "CONCAT";
        public override string DisplayName => "字符串连接";
        public override string Category => "字符串";
        public override string Description => "连接多个字符串";
        public override FunctionType Type => FunctionType.String;
        public override bool SupportsVariableParameters => true;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "values",
                    DisplayName = "字符串列表",
                    DataType = FieldDataType.Array,
                    IsRequired = true,
                    Description = "要连接的字符串列表"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var values = GetArrayParameter<object>(parameters, "values");
                var stringValues = values.Select(v => v?.ToString() ?? "").ToArray();
                var result = string.Concat(stringValues);
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Text,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"字符串连接失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "CONCAT(\"Hello\", \" \", \"World\") = \"Hello World\"",
                "CONCAT({field1}, {field2})",
                "CONCAT(\"前缀\", {field}, \"后缀\")"
            };
        }
    }

    /// <summary>
    /// 字符串长度函数
    /// </summary>
    public class LengthFunction : FunctionProviderBase
    {
        public override string Name => "LENGTH";
        public override string DisplayName => "字符串长度";
        public override string Category => "字符串";
        public override string Description => "获取字符串的长度";
        public override FunctionType Type => FunctionType.String;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "text",
                    DisplayName = "文本",
                    DataType = FieldDataType.Text,
                    IsRequired = true,
                    Description = "要计算长度的文本"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var text = GetParameterValue<string>(parameters, "text", "") ?? "";
                var result = text.Length;
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Number,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"字符串长度计算失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "LENGTH(\"Hello\") = 5",
                "LENGTH({field})",
                "LENGTH(\"中文测试\") = 4"
            };
        }
    }

    /// <summary>
    /// 转大写函数
    /// </summary>
    public class UpperFunction : FunctionProviderBase
    {
        public override string Name => "UPPER";
        public override string DisplayName => "转大写";
        public override string Category => "字符串";
        public override string Description => "将字符串转换为大写";
        public override FunctionType Type => FunctionType.String;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "text",
                    DisplayName = "文本",
                    DataType = FieldDataType.Text,
                    IsRequired = true,
                    Description = "要转换的文本"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var text = GetParameterValue<string>(parameters, "text", "") ?? "";
                var result = text.ToUpper();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Text,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"转大写失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "UPPER(\"hello\") = \"HELLO\"",
                "UPPER({field})",
                "UPPER(\"Hello World\") = \"HELLO WORLD\""
            };
        }
    }

    /// <summary>
    /// 转小写函数
    /// </summary>
    public class LowerFunction : FunctionProviderBase
    {
        public override string Name => "LOWER";
        public override string DisplayName => "转小写";
        public override string Category => "字符串";
        public override string Description => "将字符串转换为小写";
        public override FunctionType Type => FunctionType.String;

        public override List<ParameterDefinition> GetParameters()
        {
            return new List<ParameterDefinition>
            {
                new ParameterDefinition
                {
                    Name = "text",
                    DisplayName = "文本",
                    DataType = FieldDataType.Text,
                    IsRequired = true,
                    Description = "要转换的文本"
                }
            };
        }

        public override FunctionResult Execute(Dictionary<string, object> parameters)
        {
            var startTime = DateTime.Now;

            try
            {
                var text = GetParameterValue<string>(parameters, "text", "") ?? "";
                var result = text.ToLower();
                var executionTime = (DateTime.Now - startTime).Milliseconds;

                return new FunctionResult
                {
                    IsSuccess = true,
                    Value = result,
                    ResultType = FieldDataType.Text,
                    ExecutionTimeMs = executionTime
                };
            }
            catch (Exception ex)
            {
                return FunctionResult.Error($"转小写失败: {ex.Message}");
            }
        }

        public override List<string> GetExamples()
        {
            return new List<string>
            {
                "LOWER(\"HELLO\") = \"hello\"",
                "LOWER({field})",
                "LOWER(\"Hello World\") = \"hello world\""
            };
        }
    }
}
