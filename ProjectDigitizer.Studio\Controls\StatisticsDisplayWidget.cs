using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using ProjectDigitizer.Studio.Models;

namespace ProjectDigitizer.Studio.Controls
{
    /// <summary>
    /// 统计结果显示控件
    /// 用于在节点内显示统计结果的紧凑视图
    /// </summary>
    public class StatisticsDisplayWidget : UserControl
    {
        private readonly StackPanel _mainPanel;
        private readonly TextBlock _summaryText;
        private readonly StackPanel _detailsPanel;
        private readonly Button _toggleButton;
        private bool _isExpanded = false;

        private AggregationResultSet? _resultSet;

        public StatisticsDisplayWidget()
        {
            // 创建主面板
            _mainPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                Margin = new Thickness(4, 4, 4, 4),
                Background = new SolidColorBrush(Color.FromArgb(20, 0, 120, 215)) // 淡蓝色背景
            };

            // 创建摘要文本
            _summaryText = new TextBlock
            {
                FontSize = 11,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Color.FromRgb(0, 120, 215)),
                Margin = new Thickness(4, 2, 4, 2),
                TextWrapping = TextWrapping.Wrap
            };

            // 创建切换按钮
            _toggleButton = new Button
            {
                Content = "▼",
                Width = 16,
                Height = 16,
                FontSize = 8,
                Margin = new Thickness(2, 2, 2, 2),
                Padding = new Thickness(0, 0, 0, 0),
                Background = Brushes.Transparent,
                BorderThickness = new Thickness(0, 0, 0, 0),
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Top,
                Cursor = System.Windows.Input.Cursors.Hand
            };
            _toggleButton.Click += OnToggleButtonClick;

            // 创建详细信息面板
            _detailsPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                Margin = new Thickness(4, 0, 4, 4),
                Visibility = Visibility.Collapsed
            };

            // 创建标题栏
            var headerPanel = new Grid();
            headerPanel.Children.Add(_summaryText);
            headerPanel.Children.Add(_toggleButton);

            // 组装控件
            _mainPanel.Children.Add(headerPanel);
            _mainPanel.Children.Add(_detailsPanel);

            Content = _mainPanel;

            // 设置默认样式
            BorderBrush = new SolidColorBrush(Color.FromRgb(0, 120, 215));
            BorderThickness = new Thickness(1, 1, 1, 1);
            Padding = new Thickness(0, 0, 0, 0);
        }

        /// <summary>
        /// 设置统计结果数据
        /// </summary>
        public void SetResults(AggregationResultSet? resultSet)
        {
            _resultSet = resultSet;
            UpdateDisplay();
        }

        /// <summary>
        /// 更新显示内容
        /// </summary>
        private void UpdateDisplay()
        {
            if (_resultSet == null || _resultSet.Results.Count == 0)
            {
                _summaryText.Text = "无统计结果";
                _detailsPanel.Children.Clear();
                _toggleButton.Visibility = Visibility.Collapsed;
                return;
            }

            // 更新摘要文本
            _summaryText.Text = GetSummaryText();
            _toggleButton.Visibility = _resultSet.Results.Count > 1 ? Visibility.Visible : Visibility.Collapsed;

            // 更新详细信息
            UpdateDetailsPanel();
        }

        /// <summary>
        /// 获取摘要文本
        /// </summary>
        private string GetSummaryText()
        {
            if (_resultSet == null || _resultSet.Results.Count == 0)
                return "无统计结果";

            if (_resultSet.Results.Count == 1)
            {
                var result = _resultSet.Results[0];
                return $"{GetFunctionDisplayName(result.Function)}: {result.FormattedValue}";
            }

            var successCount = _resultSet.Results.Count(r => r.IsSuccess);
            return $"{successCount}/{_resultSet.Results.Count} 项统计完成";
        }

        /// <summary>
        /// 更新详细信息面板
        /// </summary>
        private void UpdateDetailsPanel()
        {
            _detailsPanel.Children.Clear();

            if (_resultSet == null || _resultSet.Results.Count <= 1)
                return;

            foreach (var result in _resultSet.Results)
            {
                var resultPanel = CreateResultPanel(result);
                _detailsPanel.Children.Add(resultPanel);
            }

            // 添加执行时间信息
            if (_resultSet.TotalExecutionTime.TotalMilliseconds > 0)
            {
                var timePanel = new TextBlock
                {
                    Text = $"执行时间: {_resultSet.TotalExecutionTime.TotalMilliseconds:F1}ms",
                    FontSize = 9,
                    Foreground = Brushes.Gray,
                    Margin = new Thickness(0, 2, 0, 0)
                };
                _detailsPanel.Children.Add(timePanel);
            }
        }

        /// <summary>
        /// 创建单个结果显示面板
        /// </summary>
        private FrameworkElement CreateResultPanel(AggregationResult result)
        {
            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 1, 0, 1)
            };

            // 状态指示器
            var statusIndicator = new TextBlock
            {
                Text = result.IsSuccess ? "✓" : "✗",
                Foreground = result.IsSuccess ? Brushes.Green : Brushes.Red,
                FontSize = 10,
                Width = 12,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 函数名称
            var functionName = new TextBlock
            {
                Text = GetFunctionDisplayName(result.Function),
                FontSize = 10,
                Width = 40,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 字段名称（如果有）
            var fieldName = new TextBlock
            {
                Text = string.IsNullOrEmpty(result.FieldName) ? "" : $"({result.FieldName})",
                FontSize = 9,
                Foreground = Brushes.Gray,
                Width = 60,
                VerticalAlignment = VerticalAlignment.Center
            };

            // 结果值
            var value = new TextBlock
            {
                Text = result.FormattedValue,
                FontSize = 10,
                FontWeight = FontWeights.SemiBold,
                Foreground = result.IsSuccess ? Brushes.Black : Brushes.Red,
                VerticalAlignment = VerticalAlignment.Center
            };

            panel.Children.Add(statusIndicator);
            panel.Children.Add(functionName);
            panel.Children.Add(fieldName);
            panel.Children.Add(value);

            return panel;
        }

        /// <summary>
        /// 获取函数的显示名称
        /// </summary>
        private static string GetFunctionDisplayName(AggregationFunction function)
        {
            return function switch
            {
                AggregationFunction.Count => "计数",
                AggregationFunction.Sum => "求和",
                AggregationFunction.Average => "平均",
                AggregationFunction.Min => "最小",
                AggregationFunction.Max => "最大",
                _ => "未知"
            };
        }

        /// <summary>
        /// 切换按钮点击事件
        /// </summary>
        private void OnToggleButtonClick(object sender, RoutedEventArgs e)
        {
            _isExpanded = !_isExpanded;
            _detailsPanel.Visibility = _isExpanded ? Visibility.Visible : Visibility.Collapsed;
            _toggleButton.Content = _isExpanded ? "▲" : "▼";
        }

        /// <summary>
        /// 设置控件的紧凑模式
        /// </summary>
        public void SetCompactMode(bool isCompact)
        {
            if (isCompact)
            {
                _summaryText.FontSize = 10;
                _mainPanel.Margin = new Thickness(2, 2, 2, 2);
                _toggleButton.Visibility = Visibility.Collapsed;
                _detailsPanel.Visibility = Visibility.Collapsed;
                _isExpanded = false;
            }
            else
            {
                _summaryText.FontSize = 11;
                _mainPanel.Margin = new Thickness(4, 4, 4, 4);
                _toggleButton.Visibility = _resultSet?.Results.Count > 1 ? Visibility.Visible : Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 获取控件的首选高度
        /// </summary>
        public double GetPreferredHeight()
        {
            if (_resultSet == null || _resultSet.Results.Count == 0)
                return 24;

            if (!_isExpanded || _resultSet.Results.Count <= 1)
                return 28;

            return 28 + (_resultSet.Results.Count * 16) + 12; // 基础高度 + 结果行数 + 执行时间
        }

        /// <summary>
        /// 刷新显示
        /// </summary>
        public void Refresh()
        {
            UpdateDisplay();
        }

        /// <summary>
        /// 清空显示
        /// </summary>
        public void Clear()
        {
            _resultSet = null;
            UpdateDisplay();
        }
    }

    /// <summary>
    /// 统计显示模式
    /// </summary>
    public enum StatisticsDisplayMode
    {
        /// <summary>
        /// 不显示
        /// </summary>
        None,

        /// <summary>
        /// 紧凑模式 - 只显示摘要
        /// </summary>
        Compact,

        /// <summary>
        /// 详细模式 - 显示所有统计结果
        /// </summary>
        Detailed,

        /// <summary>
        /// 自动模式 - 根据结果数量自动选择
        /// </summary>
        Auto
    }
}
